import { useAppStore, CONF_KEYS } from "@packages/store/app";
import { setLanguageData, setLanguage, setLanguageType } from "@packages/logics/i18n";

export const setupIframeListener = () => {
  const appStore = useAppStore();

  //子页面监听父页面传递来的数据进行处理
  window.addEventListener(
    "message",
    message => {
      console.log("[map-edit] message =>", message.data);
      const { data } = message;
      switch (data.type) {
        case "language":
          appStore.setLanguage(data.body);
          break;
        // 修改语言
        case "langChange":
          setLanguageType(data.localLang);
          break;
        case "languageData":
          setLanguageData(data.body.language, data.body.type);
          break;
        case "option":
          // 批量传输option
          const { language, languageData, ...other } = data.body;
          Object.keys(other).forEach(otherKey => {
            appStore.setStoreData(otherKey, other[otherKey]);
          });

          languageData && setLanguageData(languageData);
          language && appStore.setLanguage(language);
          if (!language && other.autoI18n) {
            // 使用 setTimeout 使 setLanguage 进行异步处理
            setTimeout(() => setLanguage("zhcn"));
          }
          break;
        default:
          if (CONF_KEYS.includes(data?.type || "")) {
            appStore.setStoreData(data.type, data.body);
          }
          break;
      }
    },
    false,
  );

  window.parent?.postMessage({
    type: "ready",
    body: {},
  });
};
