/* ! <AUTHOR> at 2023/04/20 */
namespace MRender {
  /**
   * 机器人icon
   */
  export type robotIcon =
    | "robotNormal"
    | "robotWork"
    | "robotOffline"
    | "robotError"
    | "robot_box"
    | "robotSleep"
    | "robot_belt"
    | "robot_limit_error";
  export type robotFatIcon = "robotFatNormal" | "robotFatWork" | "robotFatOffline" | "robotFatError";
  export type robotThinIcon = "robotThinNormal" | "robotThinWork" | "robotThinOffline" | "robotThinError";
  export type forkIcon = "forkNormal" | "forkWork" | "forkOffline" | "forkError";

  /**
   * 货架icon
   */
  export type shelfIcon = "shelf" | "rack" | "poppick";
  export type shelfXIcon = "X_HOLDER" | "X_PALLET" | "X_HOLDER_PALLET" | "X_PALLET_STACK" | "X_HOLDER_PALLET_STACK";
  /**
   * 货架状态icon
   */
  export type shelfStatusIcon = "shelfLocked" | "shelfFault";
  /**
   * 设备icon
   */
  export type deviceIcon = "deviceNormal" | "deviceConfigError" | "deviceError" | "dmpDeviceError";
  export type xDeviceIcon =
    | "xDevice_ssd"
    | "xDevice_ssc"
    | "xDevice_dxj"
    | "xDevice_tsj"
    | "xDevice_dpj"
    | "xDevice_ssx"
    | "xDevice_ad"
    | "xDevice_other";
  /**
   * 其他元素icon
   */
  export type elementIcon = "charger" | "elevator" | "station" | "stationComplex" | "dest";
  /**
   * 元素状态icon
   */
  export type statusIcon = "cellPause" | "status_selected" | "stationDisable";
  /**
   * 抓手icon 空闲 |任务|休眠|离线|异常
   */
  export type gripperIcon =
    | "gripperIdle_left"
    | "gripperIdle_right"
    | "gripperTask_left"
    | "gripperTask_right"
    | "gripperOffLine_left"
    | "gripperOffLine_right"
    | "gripperAnomaly_left"
    | "gripperAnomaly_right"
    | "notWorking_left"
    | "notWorking_right"
    | "abnormal_left"
    | "abnormal_right";

  /** 所有icon名称 */
  export type iconsNames =
    | robotIcon
    | robotFatIcon
    | robotThinIcon
    | forkIcon
    | shelfIcon
    | shelfStatusIcon
    | deviceIcon
    | xDeviceIcon
    | elementIcon
    | statusIcon
    | gripperIcon;
}
