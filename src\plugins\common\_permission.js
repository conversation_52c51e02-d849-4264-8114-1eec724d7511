/* ! <AUTHOR> at 2021/02 */
import PFN from "./_permission_fn";

/**
 * 有权限模式下的btn对应code权限
 */
const ButtonCodeTranslations = {
  // 右侧面板上面三个指令按钮权限如下
  MonitorSystemStop: "auth.rms.monitor.button.systemStop", // 系统急停
  MonitorTaskRecovery: "auth.rms.monitor.button.taskRecovery", // 任务恢复
  MonitorFireStop: "auth.rms.monitor.button.fireStop", // 消防急停
  // 右侧面板业务指令Tab按钮权限如下
  MonitorShutDownOneKey: "auth.rms.monitor.button.shutDownOneKey", // 一键关机
  // MonitorDormancyOneKey: "auth.rms.monitor.button.dormancyOneKey", // 一键休眠
  MonitorDormancyOneKey: "auth.rms.monitor.button.allRobotsSleep", // 全场休眠
  // MonitorAwakenOneKey: "auth.rms.monitor.button.awakenOneKey", // 一键唤醒
  MonitorAwakenOneKey: "auth.rms.monitor.button.allRobotsAwake", // 全场唤醒
  MonitorTogetherOneKey: "auth.rms.monitor.button.togetherOneKey", // 一键集合
  MonitorScanOneKey: "auth.rms.monitor.button.scanOneKey", // 一键扫描
  MonitorReturnShelfOneKey: "auth.rms.monitor.button.returnShelfOneKey", // 一键归还货架
  MonitorTurningSurfaceOneKey: "auth.rms.monitor.button.turningSurfaceOneKey", // 一键转面
  MonitorCacheRobotRestarting: "auth.rms.monitor.button.resetRobot", // 一键重启机器人
  // 右侧面板机器人tab中的指令
  MonitorRobotGoSomeWhere: "auth.rms.monitor.button.robotGoSomeWhere", // 机器人去某处
  MonitorRobotGoCharging: "auth.rms.monitor.button.robotGoCharging", // 机器人去充电
  MonitorRobotUpdateFloor: "auth.rms.monitor.button.robotUpdateFloor", // 机器人更新楼层
  MonitorRobotJoin: "auth.rms.monitor.button.robotJoin", // 机器人加入
  MonitorRobotRemove: "auth.rms.monitor.button.robotRemove", // 机器人移除
  MonitorRobotRestart: "auth.rms.monitor.button.robotRestart", // 机器人重启
  MonitorRobotUnlock: "auth.rms.monitor.button.robotRelieve", // 机器人解除
  MonitorRobotResume: "disableResumeRobot", // 显示机器人恢复
  // 右侧面板货架tab中的指令
  MonitorShelfUpdate: "auth.rms.monitor.button.shelfUpdate", // 货架更新位置
  MonitorShelfMove: "auth.rms.monitor.button.shelfMove", // 货架移动
  MonitorShelfLocking: "auth.rms.monitor.button.shelfLock", // 货架锁定
  MonitorShelfRelieve: "auth.rms.monitor.button.shelfUnlock", // 货架解除
  MonitorShelfUpdateAngel: "auth.rms.monitor.button.shelfUpdateAngel", // 货架更新角度
  MonitorShelfReturnPlacement: "auth.rms.monitor.button.shelfReturnPlacement", // 货架回库
  // 右侧面板单元格tab中的指令
  MonitorCellLock: "auth.rms.monitor.button.mapLocking", // 单元格锁定
  MonitorCellStop: "auth.rms.monitor.button.mapSuspend", // 单元格暂停
  MonitorCellUnlock: "auth.rms.monitor.button.mapRelieve", // 单元格解除
  // 右侧面板充电站tab中的指令
  MonitorChargeDisable: "auth.rms.monitor.button.chargeDisable", // 充电站禁用/启用
  MonitorChargeRestart: "auth.rms.monitor.button.chargeRestart", // 充电站重启
  // 地图管理
  MapManageReloadMap: "auth.rms.mapManage.button.reloadMap",

  RobotSoftwareManagerAdd: "auth.rms.robotSoftwareManager.botton.add",
  RobotSoftwareManagerDelete: "auth.rms.robotSoftwareManager.botton.delete",

  ParamsNewLibrary: "auth.rms.params.button.newLibrary",
  ParamsCopyToCreateLibrary: "auth.rms.params.button.copyToCreateLibrary",
  ParamsApplyThisConfiguration: "auth.rms.params.button.applyThisConfiguration",
  ParamsEditMasterInformation: "auth.rms.params.button.editMasterInformation",
  ParamsEditParams: "auth.rms.params.button.editParams",
  ParamsDeleteLibrary: "auth.rms.params.button.deleteLibrary",

  AuthAddUser: "auth.rms.addUser",
  AuthUpdateUser: "auth.rms.updateUser",
  AuthQueryUser: "auth.rms.queryUser",
  AuthEnableUser: "auth.rms.enableUser",

  AuthAddRolePermission: "auth.rms.addRolePermission",
  AuthEditRolePermission: "auth.rms.editRolePermission",
  AuthQueryRole: "auth.rms.queryRole",

  AuthEnableRole: "auth.rms.enableRole",
  AuthDelRole: "auth.rms.delRole",
};

/**
 * 无权限模式下的btn对应code权限
 */
const ConfigButtonCodeTranslations = {
  // 右侧面板上面三个指令按钮权限如下
  MonitorSystemStop: "showSystemStop", // 显示系统急停按钮
  MonitorTaskRecovery: "showTaskRecovery", // 显示任务恢复按钮
  MonitorFireStop: "showFireStop", // 显示消防急停按钮
  // 右侧面板业务指令Tab按钮权限如下
  MonitorDormancyOneKey: "showRobotSleepButton", // 显示一键休眠按钮
  MonitorAwakenOneKey: "showRobotAwakenButton", // 显示一键唤醒按钮
  MonitorTogetherOneKey: "showCollection", // 显示一键集合按钮
  MonitorShutDownOneKey: "allRobotsShutdownButton", // 显示一键关机按钮
  MonitorScanOneKey: "showRobotScan", // 显示一键扫描
  MonitorReturnShelfOneKey: "allShelfReturnButton", // 显示一键归还货架按钮
  MonitorTurningSurfaceOneKey: "showShelfTurningRunningButton", // 显示一键转面按钮,
  MonitorCacheRobotRestarting: "showRobotReset", // 显示一键重启机器人
  // 地图管理
  MapManageReloadMap: "showReloadMap",
  // 右侧面板机器人tab中的指令
  disableGoSomewhere: "disableGoSomewhere", // 隐藏机器人去某处按钮
  disableGoCharging: "disableGoCharging", // 隐藏机器人去充电按钮
  disableUpdateFloor: "disableUpdateFloor", // 隐藏机器人更新楼层按钮
  disableRecoverRobot: "disableRecoverRobot", // 隐藏机器人加入按钮
  disableGoRemoveRobot: "disableGoRemoveRobot", // 隐藏机器人移除按钮
  disableRobotRestart: "disableRobotRestart", // 隐藏机器人重启按钮
  disableRobotUnlock: "disableRobotRelieve", // 隐藏机器人解除按钮
  disableResumeRobot: "disableResumeRobot", // 显示机器人恢复
  // 右侧面板货架tab中的指令
  disableChangeShelf: "disableChangeShelf", // 隐藏更新位置按钮
  disableMoveShelf: "disableMoveShelf", // 隐藏货架移动按钮
  disableChangeShelfAngle: "disableChangeShelfAngle", // 隐藏货架更新角度按钮
  disableReturnPlacementShelf: "disableReturnPlacementShelf", // 隐藏货架回库按钮
  // 右侧面板充电站tab中的指令
  disableChargeDisable: "disableChargeDisable", // 隐藏充电站禁用/启用按钮
};

/**
 * 监控页三级页签总汇
 */
const monitorTabPermission = {
  OrderGroupRobot: "auth.rms.monitor.page.robotManage", // 机器人
  OrderGroupShelf: "auth.rms.monitor.page.shelfManage", // 货架
  OrderGroupCell: "auth.rms.monitor.page.nodeManage", // 单元格
  OrderGroupCharger: "auth.rms.monitor.page.chargeManage", // 充电站
  OrderGroupInstruction: "auth.rms.monitor.page.businessInstructions", // 业务指令
  OrderGroupZone: "auth.rms.monitor.page.controlZoneManagement", // 控制分区
  OrderGroupWarehouse: "auth.rms.monitor.page.warehouseMaintainManagement", // 仓库管理
  OrderGroupDevice: "auth.rms.monitor.page.deviceManage", // 设备管理
  OrderGroupRack: "auth.rms.monitor.page.boxManage", // 货箱管理
};

/**
 * 仓库信息=>机器人管理的三级Tab
 */
const robotManageTabPermission = {
  TabInstancePage: "auth.rms.monitor.robotManage.instance.page", // 机器人实例
  TabModelPage: "auth.rms.monitor.robotManage.model.page", // 机器人型号
  TabBussinessPage: "auth.rms.monitor.robotManage.bussiness.page", // 业务特征模型
  TabComponentManage: "auth.rms.monitor.robotManage.component.page", // 机构组件模型
  TabMechanismPage: "auth.rms.monitor.robotManage.mechanism.page", // 机构模型
  TabOntologyPage: "auth.rms.monitor.robotManage.ontology.page", // 本体模型
};

/**
 * 仓库信息=>货架管理的三级Tab
 */
const shelfManageTabPermission = {
  TabShelfQueryManagePage: "auth.rms.shelfQueryManage.page", // 货架查询
  TabPositionQueryManagePage: "auth.rms.latticeQueryManage.page", // 货位查询
  TabShelfAddManagePage: "auth.rms.shelfAddManage.page", // 货架入场
  TabShelfHandleManagePage: "auth.rms.shelfHandleManage.page", // 货架操作
  TabShelfStaticAdjustControlPage: "auth.rms.shelfStaticAdjustControl.page", // 货架静态调整控制
  TabAdjustShelfControllerManagePage: "auth.rms.adjustShelfControllerManage.page", // 静态货架调整历史查询
};

/**
 * 仓库信息=>货箱管理的三级Tab
 */
const boxManageTabPermission = {
  TabBoxSearchPage: "auth.rms.menu.boxManagement.box.page", // 货箱查询
  TabLatticeSearchPage: "auth.rms.menu.boxManagement.lattice.page", // 货位查询
  TabBoxTrackPage: "auth.rms.menu.boxManagement.boxTrack.page", // poppick货箱轨迹查询
  TabRspBoxTrackPage: "auth.rms.menu.boxManagement.RSPboxTrack.page", // rsp货箱轨迹查询
};

/**
 * 仓库信息=>托盘管理的三级Tab
 */
const palletManageTabPermission = {
  TabPalletRackPage: "auth.rms.operator.pallet.rack.page", // 托盘架管理
  TabPalletLatticePage: "auth.rms.operator.pallet.lattice.page", // 托盘位管理
};

/**
 * 仓库信息=>容器模型管理的三级Tab
 */
const containerModelManageTabPermission = {
  TabShelfCategoryPage: "auth.rms.containerModelManagement.shelfCategory.page", // 容器类别管理
  TabModelPage: "auth.rms.containerModelManagement.model.page", // 容器模型管理
};

/**
 * 运营管理=>维修调度的三级Tab
 */
const maintenanceScheduleTabPermission = {
  TabMaintenanceScheduleOnePage: "auth.rms.maintenanceSchedule.one.page", // 维修调度
  TabMaintenanceScheduleRobotPage: "auth.rms.maintenanceSchedule.robot.page", // 机器人返场
};

/**
 * 运营管理=>回调管理的三级Tab
 */
const callbackManagementTabPermission = {
  TabCallbackManagementMsgPage: "auth.rms.page.menu.callbackManagement.msg", // 回调消息管理
  TabCallbackManagementChannelPage: "auth.rms.page.menu.callbackManagement.channel", // 回调通道管理
};

/**
 * 系统配置=>系统参数配置的三级Tab
 */
const paramsConfigureTabPermission = {
  TabParamsConfigureParamPage: "auth.rms.paramsConfigure.page.param", // 参数配置
  TabParamsConfigureDemoPage: "auth.rms.paramsConfigure.page.demo", // 配置模板
};

/**
 * 系统配置=>消息配置的三级Tab
 */
const menuNoticeConfigTabPermission = {
  TabNoticeConfigFaultPage: "auth.rms.page.menu.noticeConfig.fault", // 故障配置
  TabNoticeConfigNoticePage: "auth.rms.page.menu.noticeConfig.notice", // 通知管理
};

/**
 * 系统配置=>语言包管理的三级Tab
 */
const i18nControllerManageTabPermission = {
  TabI18nControllerManageHasPage: "auth.rms.i18nControllerManage.page.has", // 已添加语言
  TabI18nControllerManageNewPage: "auth.rms.i18nControllerManage.page.new", // 添加新语言
};

// 权限相关内容
export const checkPermission = (code, natural = null) => {
  const RMSPermission = $utils.Data.getRMSPermission();
  if (RMSPermission) return PFN.permission(ButtonCodeTranslations, code);
  else return PFN.noPermission(ConfigButtonCodeTranslations, code, natural);
};

/**
 *
 * Tab汇总
 *
 */

const MenuHasTabs = {
  monitor: monitorTabPermission,
  robotManage: robotManageTabPermission,
  shelfManage: shelfManageTabPermission,
  boxManage: boxManageTabPermission,
  palletManage: palletManageTabPermission,
  containerModelManage: containerModelManageTabPermission,
  maintenanceSchedule: maintenanceScheduleTabPermission,
  callbackManagement: callbackManagementTabPermission,
  paramsConfigure: paramsConfigureTabPermission,
  menuNoticeConfig: menuNoticeConfigTabPermission,
  i18nControllerManage: i18nControllerManageTabPermission,
};

// 权限相关内容
export const getMonitorTabPermission = code => {
  const RMSPermission = $utils.Data.getRMSPermission();
  if (RMSPermission) {
    const menuAuth = $app.$store.state.menuAuth;
    let tabs = [];
    for (let key in monitorTabPermission) {
      if (menuAuth.indexOf(monitorTabPermission[key]) !== -1) {
        tabs.push(key);
      }
    }
    return tabs;
  } else {
    let tabs = [];
    for (let key in monitorTabPermission) {
      tabs.push(key);
    }
    return tabs;
  }
};

// 三级tab权限相关内容
export const getTabPermission = (code, parentMenu) => {
  const RMSPermission = $utils.Data.getRMSPermission();

  let tabPermissionList = {};
  if (MenuHasTabs.hasOwnProperty(parentMenu)) {
    //有三级菜单的权限管理
    tabPermissionList = MenuHasTabs[parentMenu];
  } else {
    return true;
  }

  if (RMSPermission && MenuHasTabs.hasOwnProperty(parentMenu)) {
    //有权限模式下 且三级菜单有权限
    const menuAuth = $app.$store.state.menuAuth;

    if (tabPermissionList.hasOwnProperty(code)) {
      // console.log(
      //   "permission",
      //   menuAuth.indexOf(tabPermissionList[code]) !== -1,
      //   menuAuth.indexOf(tabPermissionList[code]),
      // );
      if (menuAuth.indexOf(tabPermissionList[code]) !== -1) {
        //能找到就显示 找不到就不显示 【有权限且配置了 就显示】
        return true;
      } else {
        return false;
      }
    }
  } else {
    return true;
  }
};
