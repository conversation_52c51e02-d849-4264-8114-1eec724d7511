<template>
  <div :class="{ 'geek-customize-table': true, 'geek-customize-table--nobtns': !headerSetting }">
    <!-- v-bind会自动将配置的属性赋值过来 -->
    <gp-rich-table
      ref="tableRef"
      :virtualized="false"
      v-bind="attrs"
      :page="tablePage"
      :show-setting="headerSetting"
      :data="data"
      :columns="tableColumns"
      @selection-change="selectionChange"
      @expand-change="expandChange"
      @cell-dblclick="cellDblclick"
      @pageChange="pageChange"
      @sort-change="handleSortChange"
      class="customize-table"
    >
      <template #operate v-if="tableConfig.actions">
        <template v-for="item in tableConfig.actions">
          <gp-button
            v-if="item.hasOwnProperty('permission') ? item.permission : true"
            :key="item.label"
            v-bind="item"
            :size="item.mini ? item.mini : 'middle'"
            @click="actionHandler(item.handler)"
          >
            {{ $t(item.label) }}
          </gp-button>
        </template>
      </template>
      <!-- table expand -->
      <template v-if="tableConfig.expand" #[tableConfig.expand.slotName]="props">
        <slot :name="tableConfig.expand.slotName" :row="props.row" />
      </template>

      <!-- table columns -->
      <template v-for="column in tableColumns" v-slot:[column.slotName]="scope">
        <template v-if="column.slotName">
          <slot :name="column.slotName" :row="scope.row" />
        </template>
      </template>
      <template v-for="column in tableColumns" #operateBth="scope">
        <template v-if="column.buttons">
          <template v-for="item in column.buttons[0]" v-if="item.continue ? item.continue(scope.row) : true">
            <template v-if="item.continue ? item.continue(scope.row) : true">
              <gp-link
                v-if="item.hasOwnProperty('permission') ? item.permission : true"
                :key="item.label"
                v-bind="item"
                :underline="false"
                :type="item.type || 'primary'"
                :disabled="item.isDisabled ? item.isDisabled(scope.row) : false"
                @click="operationHandler(item.handler, scope.row)"
              >
                {{ item.label ? $t(item.label) : $t(item.labelFormat(scope.row)) }}
              </gp-link>
            </template>
          </template>
          <gp-dropdown v-if="column.buttons[1]" @command="command => operationHandler(command, scope.row)">
            <gp-link type="info" :underline="false" style="margin-left: 15px" size="small"> ... </gp-link>
            <gp-dropdown-menu slot="dropdown">
              <gp-dropdown-item
                v-for="(item, index) in column.buttons[1]"
                v-if="item.hasOwnProperty('permission') ? item.permission : true"
                :command="item.handler"
                :key="index"
              >
                {{ item.label ? $t(item.label) : $t(item.labelFormat(scope.row)) }}
              </gp-dropdown-item>
            </gp-dropdown-menu>
          </gp-dropdown>
        </template>
      </template>
    </gp-rich-table>
  </div>
</template>
<script>
const getOptions = value => {
  let options = [value.operations];
  if (value.operations && value.operations.length > 4) {
    let originOptions = JSON.parse(JSON.stringify(value.operations));
    let pre = originOptions.splice(0, 3);
    options = [pre, originOptions];
  }
  return options;
};

export default {
  name: "GeekCustomizeTable",
  props: {
    data: {
      required: true,
      type: Array,
      default: () => [],
    },
    page: {
      type: Object,
      default: null,
    },
    tableConfig: {
      required: true,
      attrs: {
        type: Object,
        default: () => {
          return {
            selection: false, // 多选框
            index: false, // 序号
          };
        },
      },
      expand: {
        type: Object,
        default: null,
      },
      actions: { type: Object, default: null }, // table header action
      columns: {
        required: true,
        type: Array,
        default: () => [],
      },
    },
  },
  computed: {
    attrs() {
      return this.tableConfig.attrs || {}
    },
    headerSetting() {
      return !!(this.tableConfig.actions && this.tableConfig.actions.length > 0);
    },
    tableColumns() {
      const { expand: isExpand, attrs, columns } = this.tableConfig;
      const { selection: isSelection, index: isIndex } = attrs || {};
      let expand = [],
        selection = [],
        index = [];
      if (isExpand)
        expand = Object.assign({}, this.tableConfig.expand, {
          type: "expand",
          slotName: this.tableConfig.expand.slotName,
        });
      if (isSelection) selection = { type: "selection" };
      if (isIndex) index = { type: "index", nowrap: true, label: this.$t("lang.rms.fed.listSerialNumber") };
      const tableColumns = [].concat(
        expand,
        selection,
        index,
        columns.map((v, index) =>
          Object.assign(
            {},
            v,
            { label: this.$t(v.label), nowrap: true },
            // v.operatins有重复调用的问题 暂无时间解决
            v.operations ? { slotName: "operateBth", buttons: getOptions(v) } : {},
          ),
        ),
      );
      if (!this.headerSetting && tableColumns && tableColumns.length) {
        tableColumns[tableColumns.length - 1].showSetting = true;
        tableColumns[tableColumns.length - 1].disabled = true;
      }
      return tableColumns;
    },
    tablePage() {
      // const total = this.page ? this.page.pageCount : 0;
      return this.page ? Object.assign({}, this.page) : null;
    },
  },
  // mounted() {
  //   const that = this;
  //   that.$refs.tableRef.toggleRowExpansion(that.data[1], true);
  // },
  methods: {
    pageChange({ pageSize, currentPage }) {
      const isPageSize = this.page.pageSize !== pageSize;
      this.$emit("page-change", Object.assign({}, this.page, { currentPage: isPageSize ? 1 : currentPage, pageSize }));
    },
    actionHandler(handler) {
      this.$emit(handler);
    },
    operationHandler(handler, row) {
      this.$emit(handler, row);
    },
    selectionChange(selections) {
      this.$emit("selectionChange", selections);
    },
    expandChange(row, rowList) {
      this.$emit("expandChange", { row, rowList });
    },
    cellDblclick(row, column) {
      this.$emit("cellDblclick", { row, column });
    },
    handleSortChange(params) {
      this.$emit("sort-change", params);
    },
  },
};
</script>

<style lang="less">
.geek-customize-table {
  height: 100%;
}
// .geek-customize-tabl--nobtn {
//   padding-top: 14px;
// }
.geek-customize-table .no-expand .gp-table__expand-column .cell {
  display: none;
}
.geek-customize-table .cell .gp-link {
  margin-left: 0 !important;
  margin-right: 24px;
  &:last-child {
    margin-right: 0;
  }
}
</style>
