<template>
  <!-- ⭐ 在每个关键节点加入ref属性, 可以被mapVNode.ts中的函数捕获 ⭐ -->
  <div class="editMap editDevice">
    <!-- 头部面板 -->
    <div class="headerPanels">
      <MapToolPanel
        ref="headerPanels"
        v-model="topToolOption"
        :config="{ defActive: true }"
        border
        @trigger="triggerEvent"
      />
      <div></div>
    </div>

    <div class="content">
      <!-- 画布 -->
      <div class="editMapCanvas" v-loading="isMapDataLoad" element-loading-text="Loading...">
        <!-- 帧率 -->
        <!-- <frameRate /> -->
        <MapCanIcon v-model="iconsOption" />
        <div ref="editRef" id="edit" v-show="!isMapInitResultError"></div>

        <!-- 初始化需要的数据没有准备好 -->
        <div class="empty" v-if="!ready">
          <el-empty :description="$t('lang.rms.fed.notMapFloorIdMsg')"> </el-empty>
        </div>

        <!-- 初始化失败了 -->
        <el-result
          class="mapInitResultError"
          v-if="!!isMapInitResultError"
          :title="$t('lang.rms.fed.failInitMapMsg')"
          icon="error"
        >
          <template #sub-title>
            <div class="subTitle">
              {{ $t("lang.rms.fed.plCheckWhMapFloorExMsg") }}
            </div>
            <el-alert type="error" :closable="false" :description="$t(isMapInitResultError || '')" />
          </template>
          <template #extra>
            <el-button type="primary" @click="readyCallback">{{ $t("lang.common.retry") }}</el-button>
          </template>
        </el-result>
      </div>

      <!-- 右侧面板 -->
      <div class="rightPanels">
        <DeviceAttrPanel ref="rightPanels" />
      </div>
    </div>

    <!-- 底部面板/还没有内容 先预设 -->
    <div class="floorPanels"></div>
  </div>
</template>

<script setup lang="ts">
/**
 * UI入口
 * 结构分为上/下/左/右 和地图绘制区 5块
 */

// 组件
import MapToolPanel from "@packages/components/device-panel-tool/index.vue";
import DeviceAttrPanel from "@packages/components/device-panel-attr/index.vue";
import MapCanIcon from "@packages/components/map-canvas-icon/index.vue";
import MapGlobalSearch from "@packages/components/map-global-search/index.vue";

// 组件配置
import { toolPanelList } from "../../configure/deviceToolPanel.conf";
import { iconConf } from "../../configure/canvasIcons.conf";

// 全局数据
import { storeToRefs } from "pinia";
import { useAppStore } from "@packages/store/app";

// Map初始化与获取
import { initEditMap, loadEditMapData, initMapSetup, loadMapData } from "@packages/hook/useEdit";
import { triggerEvent, editBindEventBus, triggerLayers } from "@packages/hook/useEvent";

// vue
import { ref, Ref, computed, ComputedRef, watch, onMounted } from "vue";

const appStore = storeToRefs(useAppStore());
// 获取地图数据的loading
const isMapDataLoad: Ref<boolean> = ref(false);
// 接口数据异常的flag
const isMapInitResultError: Ref<string> = ref("");
const editRef = ref();
/* **** *
 * data *
 * **** */
const iconsOption = ref(iconConf);
const topToolOption = ref(toolPanelList);

watch(
  () => appStore.mapTopToolConfig,
  value => {
    value && (topToolOption.value = value as any);
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  () => appStore.mapIconConfig,
  value => {
    value && (iconsOption.value = value as any);
  },
  {
    immediate: true,
    deep: true,
  },
);

// 是否处于ready状态
const ready: ComputedRef<boolean> = computed(() => {
  return !!(appStore.mapId?.value && appStore.floorId?.value);
});

/**
 * 等待页面加载完成后初始化editMap组件
 * 这里如果页面加载完成后mapId和floorId还没有准备好, 则需要等待数据加载完成后继续
 */
onMounted(async () => {
  // 初始化地图
  await initEditMap(editRef.value);
  // 加载非必要数据
  loadMapData();

  // 检查必须参数的准备情况
  if (ready.value) {
    readyCallback();
  } else {
    watch(ready, value => {
      value && readyCallback();
    });
  }
});

/**
 * 加载地图的最后一步, 初始化地图组件
 */
async function readyCallback() {
  setCanvasViewLoading();
  try {
    const data = await initMapSetup();
    clearCanvasViewLoading();
    isMapInitResultError.value = "";
    loadEditMapData(data);

    // 注册事件
    editBindEventBus();

    //可选择
    triggerLayers(["DEVICE"]);
    // 向调用者传递 ready
    window.parent?.postMessage({ type: "ready" });
  } catch (message) {
    clearCanvasViewLoading();
    if (typeof message === "string") {
      isMapInitResultError.value = message;
    } else if (message && typeof message === "object") {
      isMapInitResultError.value = ((message as any).message as string) || "";
      console.error(message);
    }

    // 加载失败, 向调用者传递失败
    window.parent?.postMessage({ type: "loadFail", message });
  }
}

function setCanvasViewLoading() {
  isMapDataLoad.value = true;
}

function clearCanvasViewLoading() {
  isMapDataLoad.value = false;
}

defineExpose({
  setCanvasViewLoading,
  clearCanvasViewLoading,
});
</script>

<script lang="ts">
import { setRef } from "@packages/hook/useMapVNode";
export default {
  created() {
    console.log("this >", this);
    setRef(this);
  },
};
</script>

<style scoped lang="scss">
#edit {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}
.empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.subTitle {
  font-size: 18px;
  padding-bottom: 10px;
}

.mapInitResultError {
  height: 100%;
  position: relative;
  z-index: 2;
}
</style>
<style lang="scss">
.editMap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-align: left;
  display: flex;
  position: relative;
  flex-direction: column;

  .headerPanels {
    min-height: 5px;
    position: relative;
  }

  .floorPanels {
    min-height: 5px;
    position: relative;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .leftPanels,
    .rightPanels {
      height: 100%;
      min-width: 5px;
      position: relative;
    }

    .editMapCanvas {
      flex: 1;
      height: 100%;
      position: relative;
      overflow: hidden;
    }
  }

  .editMapToolTitleStyle {
    padding: 0 10px 0 5px;
    font-weight: bolder;
    color: #9ba4e4;
    font-size: 18px;
  }
}

.editDevice {
  .treeselect {
    right: 0;
  }
}
</style>
