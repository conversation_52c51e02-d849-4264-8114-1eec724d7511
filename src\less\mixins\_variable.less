@charset "utf-8";
@g-root-font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
  Helvetica Neue, Helvetica, Arial, sans-serif;
@g-root-size: 6.25rem;
@g-root-bg: #fff;
@g-root-color: #444;

@g-font-size: 0.16rem;
@g-placeholder-color: #999;
@g-a-color: #444;

@g-border-radius: 6px;
@g-map-layer-zIndex: 999;

//container-size
@g-header-height: 46px; // nav
@g-slide-max-width: 240px; // slide
@g-slide-min-width: 36px; // slide
@g-main-padding: 7px;

//main-container
@g-background-color: rgba(243, 243, 243, 0.8);
@g-scroll-color: rgb(20, 32, 46);
//nav
@g-nav-bg: rgba(19, 41, 65, 0.9);
@g-nav-right-color: #fff;
@g-nav-height: 46px;
@g-nav-border: 1px solid #e6e6e6;
//aside
@g-aside-bg: #fff;
@g-aside-active-bg: rgba(43, 85, 129, 0.5);
@g-aside-submenu-bg: rgba(19, 41, 65, 0.7);
@g-aside-submenu-active-bg: rgba(19, 41, 65, 0.5);
@g-aside-color: #f3f3f3;
@g-aside-active-color: #2194d3;

//main
@g-main-bg: rgba(233, 233, 233, 0.6);
@g-blue: #0083ff;
