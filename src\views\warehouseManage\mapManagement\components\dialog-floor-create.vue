<template>
  <div>
    <gp-dialog
      :title="dialogFloorCreate.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="540px"
      border
      @close="close"
    >
      <gp-form ref="floorCreate" :model="floorCreate" label-position="right" label-width="70px">
        <gp-form-item :label="$t('lang.rms.fed.mapName')" prop="mapName">
          <gp-input :value="rowData.name" disabled />
        </gp-form-item>
        <gp-form-item
          :label="$t('lang.rms.fed.FloorID')"
          prop="floorId"
          :rules="[
            { max: 32, message: $t('楼层ID长度不能超过32'), trigger: ['change', 'blur'] },
            { required: true, message: $t('lang.rms.fed.floorIdCanNotNull') },
            { pattern: /^[1-9]\d?$/, message: this.$t('lang.rms.containerManage.sendModelId.check') },
          ]"
        >
          <gp-input v-model="floorCreate.floorId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
        </gp-form-item>
      </gp-form>

      <div slot="footer">
        <gp-button @click="visible = false" plain>{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" :disabled="disable" @click="submit">
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";

export default {
  name: "DialogFloorCreate",
  data() {
    return {
      mapName: "",
      floorCreate: {
        floorId: null,
      },
      disable: false,
    };
  },
  computed: {
    ...mapState("mapManagement", ["dialogFloorCreate"]),
    visible: {
      get() {
        return this.dialogFloorCreate.visible;
      },
      set(val) {
        const { visible } = this.dialogFloorCreate;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogFloorCreate.rowData;
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["hideDialog"]),
    close() {
      this.floorCreate.floorId = null;
    },
    submit() {
      this.$refs["floorCreate"].validate(valid => {
        if (valid) {
          this.disable = true;
          $req
            .postParams("/athena/map/draw/createFloor", {
              mapId: this.rowData.id,
              floorId: this.floorCreate.floorId,
            })
            .then(res => {
              this.reqSuccess(res.msg);
            })
            .catch(e => {
              this.disable = false;
            });
        }
      });
    },
    reqSuccess(msg) {
      this.disable = false;
      this.visible = false;
      this.$emit("refreshList");
      msg = $utils.Tools.transMsgLang(msg);
      this.$success(msg);
    },
  },
};
</script>

<style lang="less" scoped></style>
