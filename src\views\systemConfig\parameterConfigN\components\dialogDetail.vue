<template>
  <div class="detailDialog">
    <gp-dialog
      :title="items.i18nCode + $t('lang.rms.fed.textDetails')"
      :visible.sync="visible"
      width="45%"
      @close="handleClose(1)"
    >
      <div class="detailBox">
        <div class="contentBox">
          <div class="contentBox-title">{{ $t("lang.rms.fed.nameOfParameter") }}</div>
          <div class="contentBox-content">{{ items.code }}</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">{{ $t("lang.rms.fed.describe") }}</div>
          <div class="contentBox-content">{{ $t(items.descrI18nCode) }}</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">{{ $t("lang.rms.fed.parameterValues") }}</div>
          <div class="contentBox-content">{{ items.value }}</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">{{ $t("lang.rms.fed.effectiveImmediately") }}</div>
          <div class="contentBox-content">
            {{ items.immediate == 1 ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer"></span>
    </gp-dialog>
  </div>
</template>

<script>
export default {
  name: "dialogDetail",
  props: {
    // dialogVisible:{
    //   type: Boolean,
    //   default(){
    //     return false
    //   }
    // },
    items: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      visible: true,
    };
  },

  mounted() {
    this.handleClose();
  },

  methods: {
    handleClose(n) {
      this.$emit("close", n);
    },
  },
};
</script>

<style lang="less" scoped>
.detailDialog /deep/.gp-dialog {
  border-radius: 10px;
}
/deep/.gp-dialog__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #323334;
}
/deep/.gp-dialog__header {
  padding: 13px 20px 13px;
}
.contentBox-title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #999ea5;
  width: 82px;
}
.contentBox-content {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #323334;
  width: 100%;
}
.contentBox {
  display: flex;
}
</style>
