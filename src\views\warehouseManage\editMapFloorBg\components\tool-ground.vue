<template>
  <div class="edit-map-bg-status-choose">
    <strong>{{ $t("lang.rms.fed.groundOperation") }}:</strong>
    <gp-cascader
      v-model="type"
      :options="options"
      size="mini"
      class="edit-cascader"
      popper-class="edit-map-bg-cascader"
      @change="handleChange('type')"
    />
    <gp-input-number
      v-show="currentType !== 'groundRegion'"
      v-model="boundary"
      size="mini"
      :min="1"
      :max="60"
      :step="1"
      step-strictly
      @change="handleChange('boundary')"
    />
  </div>
</template>

<script>
export default {
  name: "ToolGround",
  props: {
    isShow: {
      type: Boolean,
      require: true,
    },
    defaultBoundary: {
      type: Number,
      require: true,
    },
  },
  data() {
    return {
      type: ["groundErase"],
      options: [
        {
          value: "groundErase",
          label: this.$t("lang.rms.fed.erase"),
        },
        {
          value: "addGround",
          label: this.$t("lang.rms.fed.addTo1"),
          children: [
            { value: "groundFree", label: this.$t("lang.rms.fed.free") },
            { value: "groundStraightLine", label: this.$t("lang.rms.web.map.segment.type.line") },
            { value: "groundRegion", label: this.$t("lang.rms.fed.region") },
          ],
        },
      ],
      boundary: this.defaultBoundary,
    };
  },
  computed: {
    currentType() {
      const last = this.type.length - 1;
      return this.type[last];
    },
  },
  watch: {
    isShow(flag) {
      if (flag) {
        const last = this.type.length - 1;
        const type = this.type[last];
        this.$emit("change", type, this.boundary);
      }
    },
  },
  methods: {
    handleChange(key) {
      const last = this.type.length - 1;
      const type = this.type[last];
      if (key === "type") {
        this.$emit("change", type, null);
      } else if (key === "boundary") {
        this.$emit("change", null, this.boundary);
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
