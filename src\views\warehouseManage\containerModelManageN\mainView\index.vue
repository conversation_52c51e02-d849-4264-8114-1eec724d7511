<template>
  <div class="contentModelManageMainView">
    <!-- 查询 -->
    <SearchForm @onsubmit="onSubmit" />

    <div class="operateBtns">
      <gp-button class="addItem" type="primary" @click="addItem">{{ $t("lang.rms.fed.add") }}</gp-button>
    </div>

    <!-- 内容 -->
    <div class="content" @wheel="viewWheel">
      <gp-row class="content-row" v-if="recordList.length">
        <div class="w20_per" v-for="(recordItem, index) in recordList" :key="`${curk}_${index}`">
          <ContentModel
            class="contentModel"
            :option="recordItem"
            @delete="delItem(recordItem)"
            @handle="handleItem(recordItem)"
            @mouseenter="setMouseenter(true)"
            @mouseleave="setMouseenter(false)"
          />
        </div>
      </gp-row>
      <gp-empty v-else class="emptyStyle" :image-size="200"></gp-empty>
    </div>

    <!-- 翻页 -->
    <div style="text-align: right; margin-top: 30px">
      <gp-pagination
        background
        layout="total,prev, pager, next, sizes, jumper"
        :page-sizes="[10]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>

    <!-- editView -->
    <EditView v-if="editViewDialog" :visible.sync="editViewDialog" @updateList="getTableData" />
  </div>
</template>

<script>
import SearchForm from "./Components/SearchForm";
import ContentModel from "./Components/contentModel";
import EditView from "../editView";

import { getContainerModelData, deleteContainerModel } from "../api/index";

import { mapGetters, mapMutations, mapActions } from "vuex";

export default {
  name: "",
  components: {
    EditView,
    SearchForm,
    ContentModel,
  },
  data() {
    return {
      searchData: {},
      recordList: [],
      editViewDialog: false,
      paginationParams: { pageSize: 10, currentPage: 1, total: 0 },
      isMouseenter: false,
      curk: 1,
    };
  },
  computed: {},
  watch: {},
  created() {
    console.log("created");
    this.fetchShelfCategory();
    this.fetchContainerModelCategoryDict();
  },
  methods: {
    ...mapMutations("containerModal", ["setLayoutOpen", "setEditData"]),
    ...mapActions("containerModal", ["fetchShelfCategory", "fetchContainerModelCategoryDict"]),

    // 删除
    delItem(row) {
      deleteContainerModel({ id: row.id }).then(res => {
        if (res.code === 0) {
          this.$message({
            message: this.$t("lang.rms.fed.deleteSuccessfully"),
            type: "success",
          });
          this.getTableData();
        }
      });
    },

    addItem() {
      this.setEditData({});
      this.editViewDialog = true;
    },

    // 编辑
    handleItem(row) {
      this.setEditData(row);
      this.editViewDialog = true;
    },

    paginationChange(currentPage) {
      this.searchData.currentPage = currentPage;
      this.paginationParams.currentPage = currentPage;
      this.getTableData();
    },
    paginationPageChange(pageSize) {
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = pageSize;
      this.paginationParams.pageSize = pageSize;
      this.getTableData();
    },

    onSubmit(data) {
      this.searchData = Object.assign({}, data);
      this.searchData.currentPage = 1;
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = this.paginationParams.pageSize;
      this.getTableData();
    },

    async getTableData() {
      const result = await getContainerModelData(this.searchData);
      this.paginationParams.total = result.data.recordCount;
      // this.recordList = result.data.recordList;
      this.recordList = [];
      this.recordList = result.data.recordList;
      this.curk = this.curk + 1;
    },

    setMouseenter(isMouse) {
      this.isMouseenter = isMouse;
    },

    viewWheel(event) {
      if (this.isMouseenter) {
        event.preventDefault();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.contentModelManageMainView {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  .content {
    flex: 1;
    overflow: auto;
    padding: 5px 0;
  }

  .operateBtns {
    padding-right: 10px;
    .addItem {
      width: 120px;
      height: 32px;
    }
  }
}

.w20_per {
  width: 20%;
  display: inline-block;
}

.emptyStyle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
