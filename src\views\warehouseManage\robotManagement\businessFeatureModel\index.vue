<template>
  <section class="robot-manage-panel-wrap">
    <geek-customize-form
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
      class="instance-search-form"
    />
    <div class="robot-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-view="rowView"
        @row-add="rowAdd"
        @row-copy="rowCopy"
        @row-edit="rowEdit"
        @row-del="rowDel"
      >
        <template #taskTypes="{ row }">
          <gp-tag v-for="item in row.taskTypes" :key="item" type="info" style="margin-right: 6px; margin-bottom: 2px">
            {{ item }}
          </gp-tag>
        </template>
        <template #sizeTypes="{ row }">
          <gp-tag v-for="item in row.sizeTypes" :key="item" type="info" style="margin-right: 6px; margin-bottom: 2px">
            {{ item }}
          </gp-tag>
        </template>
        <template #containerTypes="{ row }">
          <gp-tag
            v-for="item in row.containerTypes"
            :key="item"
            type="info"
            style="margin-right: 6px; margin-bottom: 2px"
          >
            {{ item }}
          </gp-tag>
        </template>
        <template #chargerTypes="{ row }">
          <gp-tag
            v-for="item in row.chargerTypes"
            :key="item"
            type="info"
            style="margin-right: 6px; margin-bottom: 2px"
          >
            {{ item }}
          </gp-tag>
        </template>
      </geek-customize-table>
    </div>
    <EditDialog ref="editDialog" :sizeTypeOptions="sizeTypeOptions" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "BusinessFeatureModel",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      sizeTypeOptions: [],
      form: {
        name: "",
        sizeTypes: [],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {},
        actions: [
          {
            label: "lang.rms.api.result.warehouse.createRobotBusinessModel",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.api.result.warehouse.businessFeatureName", prop: "name", nowrap: true },
          {
            label: "lang.rms.api.result.warehouse.supportedTaskTypes",
            prop: "taskTypes",
            slotName: "taskTypes",
          },
          { label: "sizeType", prop: "sizeTypes", slotName: "sizeTypes", width: "200" },
          {
            label: "lang.rms.api.result.warehouse.containerType",
            prop: "containerTypes",
            slotName: "containerTypes",
            nowrap: true,
          },
          {
            label: "lang.rms.fed.robotParamConfig.chargingStationType",
            prop: "chargerTypes",
            slotName: "chargerTypes",
            nowrap: true,
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "250",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.web.map.version.copy",
                permission,
                handler: "row-copy",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
                type: "danger",
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    formConfig() {
      return {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.businessFeatureName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBusinessFeatureModelName",
          },
          sizeTypes: {
            label: "sizeType",
            tag: "select",
            default: "",
            multiple: true,
            filterable: true,
            "allow-create": true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterSelectedQueryCriteria",
            options: (this.sizeTypeOptions || []).map(item => {
              return {
                label: item,
                value: item,
              };
            }),
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      };
    },
  },
  activated() {
    this.getSizeTypes();
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open("add");
    },
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowCopy(row) {
      this.$refs.editDialog.open("copy", row);
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/businessDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/businessPageList?pageSize=${pageSize}&currentPage=${currentPage}`;
      const searchData = this.form;

      $req.post(url, searchData).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          total: result.recordCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    getSizeTypes() {
      $req.get("/athena/robot/manage/findDistinctSizeType").then(res => {
        if (res?.code !== 0) return;
        const data = res?.data || [];
        this.sizeTypeOptions = data;
        // this.formConfig.configs.sizeTypes.options = data;
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
}
</style>
