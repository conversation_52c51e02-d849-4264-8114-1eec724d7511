<template>
  <gp-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
    <div>
      <gp-form ref="editForm" :model="library" :rules="rules" label-width="80px">
        <gp-form-item :label="$t('lang.rms.fed.libraryName')" required="required" prop="name">
          <gp-input v-model="library.name"></gp-input>
        </gp-form-item>
        <gp-form-item :label="$t('lang.rms.fed.describe')" prop="description">
          <gp-input v-model="library.description" type="textarea"></gp-input>
        </gp-form-item>
      </gp-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <gp-button @click="dialogVisible = false">{{ $t("lang.rms.fed.cancel") }}</gp-button>
      <gp-button type="primary" @click="handleSave">{{ $t("lang.rms.fed.confirm") }}</gp-button>
    </span>
  </gp-dialog>
</template>
<script>
export default {
  name: "DialogLibraryEdit",
  data() {
    return {
      dialogVisible: false,
      dialogType: "add",

      library: {
        name: "",
        description: "",
      },
      rules: {
        name: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnter"),
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.dialogType === "add" ? this.$t("lang.rms.fed.create") : this.$t("lang.rms.fed.edit");
    },
  },
  methods: {
    open(type, libraryData) {
      this.dialogType = type;
      if (type === "edit" && libraryData) {
        this.library = libraryData;
      } else {
        this.library = {
          name: "",
          description: "",
        };
      }
      this.dialogVisible = true;
    },
    handleSave() {
      const params = this.library;
      this.$refs["editForm"].validate(valid => {
        if (!valid) return false;

        // 配置库创建
        $req.post("/athena/config/template/save", params).then(res => {
          if (res.code === 0) {
            this.$emit("updatedLibrary", res.data.id);
            this.dialogVisible = false;
          }
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
