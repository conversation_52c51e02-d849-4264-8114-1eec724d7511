<template>
  <section>
    <geek-customize-form ref="refFrom" :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #destLatticeAndShelf>
        <el-table-column 
          key="destLatticeAndShelf"
          :label="`${$t('lang.rms.fed.updateBoxLocation')}/${$t('lang.rms.fed.destBoxRack')}`"
          min-width="150"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.destLatticeAndShelf.length">
              {{ scope.row.destLatticeAndShelf.join("、") }}
              <!-- <p v-for="(item, index) in scope.row.destLatticeAndShelf" :key="index">
                {{item}}
              </p> -->
            </div>
          </template>
        </el-table-column>
      </template>
      <template #operation="{ row }">
        <div v-if="row.boxStatus === 'POSITION_CONFIRMING'">
          <el-button type="primary" size="mini" class="btn-opt" @click="itemSave(row)">
            {{ $t("lang.rms.fed.confirm") }}
          </el-button>
          <el-button type="primary" size="mini" class="btn-opt" @click="itemChange(row)">
            {{ $t("lang.rms.box.changeBtn") }}
          </el-button>
        </div>
      </template>
    </geek-customize-table>
  </section>
</template>
<script>
export default {
  data() {
    let start = new Date();
    let end = new Date();
    start.setTime(start.getTime() - 24 * 60 * 60 * 1000);
    return {
      form: {
        boxCode: "",
        dataRange: [],
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          boxCode: {
            label: "lang.rms.fed.boxCode",
            default: "",
            placeholder: "lang.rms.fed.web.boxCode.input",
            tag: "input",
          },
          dataRange: {
            label: "lang.rms.fed.updateTime",
            default: [start, end],
            valueFormat: "yyyy-MM-dd HH:ss:mm",
            type: "datetimerange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
          },
          {
            label: "lang.rms.fed.updateBoxLocation",
            prop: "destLatticeCode",
          },
          {
            label: "lang.rms.fed.destBoxRack",
            prop: "destShelfCode",
          },
          // { slotCustomName: "destLatticeAndShelf" },
          {
            label: "lang.rms.fed.stationNo",
            prop: "stationId",
          },
          { label: "lang.rms.fed.taskInstruction", prop: "instruction" },
          { label: "lang.rms.fed.taskCode", prop: "warehouseJobId" },
          { label: "lang.rms.fed.config.robotId", prop: "robotId" },
        ],
      },
    };
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 24 * 60 * 60 * 1000);
      this.tablePage.currentPage = 1;
      this.$refs.refFrom.setData({ dataRange: [start, end] });
      this.form = { boxCode: "", dataRange: [start, end] };
    },
    getTableList() {
      let startTime = null;
      let endTime = null;
      if (this.form.dataRange) {
        [startTime, endTime] = this.form.dataRange;
      }
      const params = {
        boxCode: this.form.boxCode,
        hostTaskType: "RSP",
        startTime: startTime ? new Date(startTime).valueOf() : null,
        endTime: endTime ? new Date(endTime).valueOf() : null,
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (!params.boxCode) {
        this.$warning(this.$t("lang.mb.login.required", [this.$t("lang.rms.fed.boxCode")]));
        return;
      }
      $req.get("/athena/box/movePath", params).then(res => {
        let result = res.data;
        if (!result) return;
        // result.recordList && result.recordList.forEach(item => {
        //   item.destLatticeAndShelf = []
        //   if (item.destLatticeCodeList?.length) {
        //     item.destLatticeCodeList.forEach((cItem, cIndex) => {
        //       let value = `${cItem}/${item.destShelfCodeList[cIndex]}`
        //       item.destLatticeAndShelf.push(value)
        //     })
        //   }
        // })
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
