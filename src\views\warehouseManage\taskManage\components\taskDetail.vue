<template>
  <m-dialog
    :is-need-footer="false"
    :visible="visible"
    @closed="handleCloseDialog"
    :title="$t('lang.rms.fed.taskDetail')"
  >
    <div class="ui-taskDetail__content">
      <!-- 子任务详情 -->
      <p class="mt15 mb10 f16">{{ $t("lang.rms.fed.taskInfo") }}</p>
      <gp-descriptions :column="3" border>
        <!-- 子任务ID -->
        <gp-descriptions-item :label="$t('lang.rms.fed.taskId')">{{ initRow.jobId || "--" }}</gp-descriptions-item>
        <!-- 容器编码 -->
        <gp-descriptions-item :label="$t('lang.rms.web.container.containerCode')">{{
          initRow.container || "--"
        }}</gp-descriptions-item>
        <!-- 任务状态 -->
        <gp-descriptions-item :label="$t('lang.rms.web.monitor.robot.taskState')">{{
          initRow.jobStatus || "--"
        }}</gp-descriptions-item>
        <!-- 机器人ID -->
        <gp-descriptions-item :label="$t('lang.mb.robotManage.robotId')">{{
          initRow.robotId || "--"
        }}</gp-descriptions-item>
        <!-- 任务耗时 -->
        <gp-descriptions-item :label="$t('lang.rms.fed.taskExpense')">{{
          initRow.costTime || "--"
        }}</gp-descriptions-item>
      </gp-descriptions>
      <p class="mt20 f16 mb10">{{ $t("lang.rms.fed.taskDetail") }}</p>
      <!-- 后期可能会加 -->
      <!-- <gp-input v-model="filterText">
        <gp-button slot="append" icon="gp-icon-search" @click="handleSearch"></gp-button>
      </gp-input> -->
      <gp-tree :data="treeData" :props="{ children: 'childTopics' }" :filter-node-method="filterNode" ref="tree">
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <p class="content">
              <span class="b">{{ $t(data.traceTopic) }}</span>
              ：{{ $t(data.resultDesc, data.resultDescValues || []) || "--" }}
            </p>
            <p class="time gp-button--text">{{ formatterTime(data.traceTime) }}</p>
          </div>
        </template>
      </gp-tree>
    </div>
  </m-dialog>
</template>
<script>
/**
 *  lang.rms.fed.taskInfo: 任务信息
 *  lang.rms.fed.taskExpense: 任务耗时
 */
export default {
  props: {
    visible: Boolean,
    initRow: Object,
  },
  data() {
    return {
      treeData: this.initRow.traces || [],
      filterText: "",
    };
  },
  methods: {
    fetchTaskDetail() {},
    handleCloseDialog() {
      this.$emit("update:visible", false);
    },
    formatterTime(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss") || "";
    },
    filterNode(value, data) {
      if (!value) return true;
      return this.$t(data.traceTopic).indexOf(value) !== -1;
    },
    handleSearch() {
      this.$refs.tree.filter(this.filterText);
    },
  },
};
</script>
<style lang="less" scoped>
.custom-tree-node {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
</style>
