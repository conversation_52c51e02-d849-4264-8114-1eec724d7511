/* ! <AUTHOR> at 2022/09/02 */
import LayerRealtimeObstacle from "./realtime-obstacle";
import LayerKnockArea from "./knock-area";
import LayerStopArea from "./stop-area";
import LayerFilterArea from "./filter-area";
import LayerSpeedLimitArea from "./speed-limit-area";
import LayerClearRobotArea from "./clear-robot-area";
import LayerRobotArea from "./robot-area";
import LayerDiffColorArea from "./diff-color-area";
import LayerInspectionArea from "./inspection-area";

class LayerArea {
  floorId: floorId;
  private layerRealtimeObstacle: LayerRealtimeObstacle;
  private layerStopArea: LayerStopArea;
  private layerFilterArea: LayerFilterArea;
  private layerKnockArea: LayerKnockArea;
  private layerSpeedLimitArea: LayerSpeedLimitArea;
  private layerClearRobotArea: LayerClearRobotArea;
  private layerRobotArea: LayerRobotArea;
  private layerDiffColorArea: LayerDiffColorArea;
  private layerInspectionArea: LayerInspectionArea;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.floorId = floor.floorId;
    this.layerRealtimeObstacle = new LayerRealtimeObstacle(mapCore, floor);
    this.layerStopArea = new LayerStopArea(mapCore, floor);
    this.layerFilterArea = new LayerFilterArea(mapCore, floor);
    this.layerKnockArea = new LayerKnockArea(mapCore, floor);
    this.layerSpeedLimitArea = new LayerSpeedLimitArea(mapCore, floor);
    this.layerRobotArea = new LayerRobotArea(mapCore, floor);
    this.layerClearRobotArea = new LayerClearRobotArea(mapCore, floor);
    this.layerDiffColorArea = new LayerDiffColorArea(mapCore, floor);
    this.layerInspectionArea = new LayerInspectionArea(mapCore, floor);
  }

  renderAreas(
    type:
      | "realtimeObstacles"
      | "knockAreas"
      | "stopAreas"
      | "filterAreas"
      | "speedLimitAreas"
      | "clearRobotAreas"
      | "robotAreas"
      | "diffColorAreas"
      | "inspectionArea",
    arr: Array<realtimeObstacleData> | Array<knockAreaData> | Array<any>,
    errorArr?: Array<any>,
  ) {
    switch (type) {
      case "realtimeObstacles":
        this.layerRealtimeObstacle.render(arr as Array<realtimeObstacleData>);
        break;
      case "knockAreas":
        this.layerKnockArea.render(arr as Array<knockAreaData>);
        break;
      case "stopAreas":
        this.layerStopArea.render(arr as Array<any>);
        break;
      case "filterAreas":
        this.layerFilterArea.repaint();
        this.layerFilterArea.render(arr as Array<any>);
        break;
      case "speedLimitAreas":
        this.layerSpeedLimitArea.render(arr as Array<any>);
        break;
      case "clearRobotAreas":
        this.layerClearRobotArea.render(arr as Array<any>);
      case "robotAreas":
        this.layerRobotArea.repaint();
        this.layerRobotArea.render(arr as any[]);

        break;
      case "diffColorAreas":
        this.layerDiffColorArea.render(arr as Array<any>);
        break;
      case "inspectionArea":
        this.layerInspectionArea.repaint();
        this.layerInspectionArea.render(arr as any[], errorArr as any[]);
        break;
    }
  }
  updateAreas(type: "realtimeObstacles" | "knockAreas", arr: Array<realtimeObstacleData> | Array<knockAreaData>) {
    switch (type) {
      case "realtimeObstacles":
        this.layerRealtimeObstacle.update(arr as Array<realtimeObstacleData>);
        break;
      case "knockAreas":
        this.layerKnockArea.update(arr as Array<knockAreaData>);
        break;
    }
  }

  toggleRealtimeObstacle(isShow: boolean) {
    this.layerRealtimeObstacle.toggle(isShow);
  }

  toggleRobotArea(isShow: boolean) {
    this.layerRobotArea.toggle(isShow);
  }

  getAreaCode(type: string, x: number, y: number) {
    switch (type) {
      case "realtimeObstacle":
        return this.layerRealtimeObstacle.getAreaCode(x, y);
      case "knockArea":
        return this.layerKnockArea.getAreaCode(x, y);
    }
  }

  getContainer(type: string) {
    switch (type) {
      case "realtimeObstacle":
        return this.layerRealtimeObstacle.getContainer();
      case "knockArea":
        return this.layerKnockArea.getContainer();
    }
  }

  repaint(type = "all") {
    switch (type) {
      case "filterAreas":
        this.layerFilterArea.repaint();
        break;
      case "diffColorAreas":
        this.layerDiffColorArea.repaint();

        break;
      default:
        this.layerRealtimeObstacle.repaint();
        this.layerFilterArea.repaint();
        this.layerKnockArea.repaint();
    }
  }
  destroy(): void {
    this.layerRobotArea.destroy();
    this.layerRobotArea = null;
    this.layerRealtimeObstacle.destroy();
    this.layerRealtimeObstacle = null;
    this.layerStopArea.destroy();
    this.layerStopArea = null;
    this.layerFilterArea.destroy();
    this.layerFilterArea = null;
    this.layerKnockArea.destroy();
    this.layerKnockArea = null;
    this.layerDiffColorArea.destroy();
    this.layerDiffColorArea = null;
    this.layerInspectionArea.destroy();
    this.layerInspectionArea = null;
  }
}
export default LayerArea;
