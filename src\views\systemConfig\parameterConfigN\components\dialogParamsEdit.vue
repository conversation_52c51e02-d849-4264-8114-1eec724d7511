<template>
  <gp-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="dialogVisible"
    :before-close="close"
    :append-to-body="true"
  >
    <gp-form label-position="top" label-width="80px" :inline="true" class="dialog-form">
      <gp-form-item :label="$t('lang.rms.fed.nameOfParameter')">
        <gp-input :value="rowData.code" disabled="disabled" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.effectiveImmediately')">
        <gp-input :value="formatterImmediate(rowData.immediate)" disabled="disabled" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.parameterValues')">
        <gp-input v-model="parameterValues" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.parameterLabel')">
        <gp-input :value="rowData.tags" disabled="disabled" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.describe')">
        <gp-input :value="$t(rowData.descr)" type="textarea" rows="4" disabled="disabled" />
      </gp-form-item>
    </gp-form>

    <span slot="footer" class="dialog-footer">
      <gp-button @click="close">{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
    </span>
  </gp-dialog>
</template>
<script>
export default {
  name: "DialogParamsEdit",
  props: ["immediateList"],
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      parameterValues: "",
    };
  },
  methods: {
    open(rowData) {
      this.rowData = rowData;
      this.parameterValues = rowData.value;
      this.dialogVisible = true;
    },
    close() {
      Object.assign(this.$data, this.$options.data());
    },
    save() {
      let data = Object.assign({}, this.rowData, { value: this.parameterValues });
      this.$emit("save", data);
      this.close();
    },
    formatterImmediate(value) {
      if (!value) return "";
      const immediateList = this.immediateList;
      let obj = immediateList.find(item => item["key"] === value.toString());
      return obj ? this.$t(obj.value) : value;
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-form {
  :deep(.gp-form-item) {
    width: 100%;
    margin-bottom: 10px;
  }

  :deep(.gp-form-item__label) {
    padding-bottom: 0;
    font-size: 13px;
    font-weight: 800;
  }
}
</style>
