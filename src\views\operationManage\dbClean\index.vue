<template>
  <geek-main-structure v-loading="loading" class="db-clean-panel-wrap">
    <div class="db-clean-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        @row-add="rowAdd"
        @row-del="rowDel"
        @row-edit="rowEdit"
        @row-results="rowResults"
        @row-task="rowTask"
      />
    </div>
    <DetailDialog
      v-if="dialogVisible"
      :rule-detail="ruleDetail"
      :dialog-visible.sync="dialogVisible"
      @onsubmit="handleSave"
    />
    <ResultDialog :results="logList" :result-visible.sync="resultVisible" />
  </geek-main-structure>
</template>

<script>
import DetailDialog from "./DetailDialog";
import ResultDialog from "./ResultDialog";

export default {
  components: {
    DetailDialog,
    ResultDialog,
  },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      dialogVisible: false,
      ruleDetail: {},
      resultVisible: false,
      logList: [],
      startTime: "",
      endTime: "",
      activeName: "",
      loading: false,

      tableData: [],
      tableConfig: {
        actions: [
          {
            label: "lang.rms.fed.newlyAdded",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "ID", prop: "id" },
          {
            label: "lang.rms.hygeia.ruleName",
            prop: "ruleName",
            width: "180",
          },
          { label: "lang.rms.hygeia.leaveDay", prop: "dataRetainDay" },
          { label: "lang.rms.hygeia.cleanSequence", prop: "schedulerIntervalDay" },
          {
            label: "lang.rms.fed.state",
            prop: "ruleStatus",
            formatter: (row, column) => {
              switch (row[column]) {
                case "enabled":
                  return this.$t("lang.rms.fed.enable");
                case "disabled":
                  return this.$t("lang.rms.fed.disable");
                default:
                  return row[column];
              }
            },
          },
          {
            label: "lang.rms.hygeia.lastExeTime",
            prop: "lastProcessTime",
            formatter: (row, column) => {
              if (!row[column]) return "";
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.hygeia.lastExeResult",
            prop: "lastProcessResult",
            formatter: (row, column) => {
              switch (row[column]) {
                case "SUCCESS":
                  return this.$t("lang.rms.hygeia.exeSuccess");
                case "NEW":
                  return this.$t("lang.rms.hygeia.init");
                case "CLEANING":
                  return this.$t("lang.rms.hygeia.cleaning");
                case "FAILED":
                  return this.$t("lang.rms.hygeia.exeFailed");
                default:
                  return row[column];
              }
            },
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "300",
                fixed: "right",
                operations: [
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                  {
                    label: "lang.rms.fed.delete",
                    handler: "row-del",
                  },
                  {
                    label: "lang.rms.hygeia.exeResult",
                    handler: "row-results",
                  },
                  {
                    label: "lang.rms.data.clean.executeTask",
                    isDisabled: row => row.ruleStatus === "disabled",
                    handler: "row-task",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.ruleDetail = {
        systemCode: "",
        targetDbInfo: {
          passwordType: 0,
        },
        ruleType: "DELETE_ONLY",
        ruleStatus: "enabled",
        dataRetainDay: 360,
        schedulerIntervalDay: 7,
        schedulerFixedTime: "00:00:00",
        schedulerMaxTime: 0,
        ruleOrder: 1,
        targetFileInfo: "",
        sourceDbInfo: {
          host: "127.0.0.1",
          port: 3306,
          schema: "",
          user: "root",
          password: "",
          passwordType: 0,
        },
        items: [
          {
            sourceTable: "",
            timeColumn: "",
            order: 0,
          },
        ],
      };
      this.dialogVisible = true;
    },
    rowEdit(rule) {
      this.loading = true;
      $req
        .get("/athena/clean/rule/detail", {
          ruleId: rule.id,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.ruleDetail = res.data;
            if (this.ruleDetail.targetDbInfo === null) {
              this.ruleDetail.targetDbInfo = { passwordType: 0 };
            }
            this.dialogVisible = true;
          }
        });
    },
    rowDel(rule) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete"))
        .then(() => {
          this.loading = true;
          $req
            .postParams("/athena/clean/rule/delete", {
              ruleId: rule.id,
            })
            .then(res => {
              this.loading = false;
              if (res.code === 0) {
                this.$success(this.$t("lang.rms.fed.deleteSuccessfully"));
                this.getTableList();
              }
            });
        })
        .catch(() => {});
    },
    // 执行任务
    rowTask(row) {
      $req
        .postParams("/athena/clean/rule/execute", {
          ruleId: row.id,
        })
        .then(res => {
          if (res.code === 0) {
            this.$success(this.$t("lang.rms.hygeia.jobExecuting"));
          }
        });
    },
    rowResults(rule) {
      this.loading = true;
      $req
        .get("/athena/clean/job/list", {
          ruleId: rule.id,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.logList = res.data;
            this.logList.map(item => {
              item.details = [];
            });
            this.resultVisible = true;
          }
        });
    },
    handleSave(detail) {
      this.loading = true;
      $req
        .post("/athena/clean/rule/save", {
          ...detail,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.$success(this.$t("lang.common.success"));
            this.dialogVisible = false;
            this.getTableList();
          } else {
            this.$message.error(res.message);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getTableList() {
      this.loading = true;
      $req
        .get("/athena/clean/rule/list")
        .then(res => {
          if (res.code != 0) return;
          let result = res?.data || [];
          this.tableData = result;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less">
.db-clean-panel-wrap {
  display: flex;
  flex-direction: column;
  // height: calc(100% -20px);
  .db-clean-panel-wrap__table {
    flex: 1;
  }
}
</style>
