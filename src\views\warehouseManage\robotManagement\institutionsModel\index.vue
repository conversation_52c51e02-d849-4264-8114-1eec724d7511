<template>
  <section class="robot-manage-panel-wrap">
    <geek-customize-form
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
      class="instance-search-form"
    />
    <div class="robot-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-view="rowView"
        @row-add="rowAdd"
        @row-copy="rowCopy"
        @row-edit="rowEdit"
        @row-del="rowDel"
      >
        <template #navigationModes="{ row }">
          <gp-tag
            v-for="item in row.navigationModes"
            :key="item"
            type="info"
            style="margin-right: 6px; margin-bottom: 2px"
          >
            {{ navigationModesMap[+item] }}
          </gp-tag>
        </template>
        <template #movingModes="{ row }">
          <gp-tag v-for="item in row.movingModes" :key="item" type="info" style="margin-right: 6px; margin-bottom: 2px">
            {{ item }}
          </gp-tag>
        </template>
      </geek-customize-table>
    </div>
    <EditDialog ref="editDialog" :robotSeries="robotSeries" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "RobotInstance",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      navigationModesMap: ["SLAM", "QR"],
      robotSeries: [],
      form: {
        name: "",
        series: "",
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {},
        actions: [
          {
            label: "lang.rms.api.result.warehouse.createRobotChassisModel",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.api.result.warehouse.ontologyName", prop: "name", nowrap: true },
          {
            label: "lang.rms.api.result.warehouse.series",
            prop: "series",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.lengthWidthHeight",
            prop: "length",
            nowrap: true,
            formatter: (row, column) => {
              let cellValue = row[column];
              let width = row.width,
                height = row.height;
              if (!cellValue && cellValue != 0) cellValue = "--";
              if (!width && width != 0) width = "--";
              if (!height && height != 0) height = "--";
              return `${cellValue}/${width}/${height}`;
            },
          },
          {
            label: "lang.rms.fed.rotationDiameter",
            prop: "diameter",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.bodyWeight",
            prop: "weight",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.navigationMode",
            prop: "navigationModes",
            slotName: "navigationModes",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.maxDrivingSpeed",
            prop: "maxVelocity",
            nowrap: true,
          },
          { label: "lang.rms.api.result.warehouse.maxTuringSpeed", prop: "maxAngularVelocity", nowrap: true },
          { label: "lang.rms.api.result.warehouse.maxAcceleration", prop: "maxAcceleration", nowrap: true },
          {
            label: "lang.rms.api.result.warehouse.moveMode",
            prop: "movingModes",
            slotName: "movingModes",
            "min-width": "180px",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.maxDistanceError",
            prop: "maxPositionError",
            nowrap: true,
          },
          {
            label: "lang.rms.api.result.warehouse.batteryType",
            prop: "batteryType",
            nowrap: true,
            formatter: (row, column) => {
              const cellValue = row[column];
              const mapLang = [
                "lang.rms.fed.battery.lto",
                "lang.rms.fed.battery.lfpo",
                "lang.rms.api.result.warehouse.LithiumTernary",
              ];
              return this.$t(mapLang[+cellValue]);
            },
          },
          {
            label: "lang.rms.api.result.warehouse.batteryMaxMah",
            prop: "maxBatteryCapacity",
            nowrap: true,
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "250",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.web.map.version.copy",
                permission,
                handler: "row-copy",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
                type: "danger",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getDictionary();
    this.getTableList();
  },
  computed: {
    formConfig() {
      return {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.ontologyName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterOntologyName",
          },
          series: {
            label: "lang.rms.api.result.warehouse.series",
            tag: "select",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.series",
            options: this.robotSeries || [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      };
    },
  },
  methods: {
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowAdd() {
      this.$refs.editDialog.open("add", {
        maxPositionError: 150,
      });
    },
    rowCopy(row) {
      this.$refs.editDialog.open("copy", row);
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/chassisDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/chassisPageList?pageSize=${pageSize}&currentPage=${currentPage}`;

      $req.get(url, this.form).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          total: result.recordCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    getDictionary() {
      $req.post("/athena/dict/query", { types: ["ROBOT_SERIES"] }).then(res => {
        if (res.code !== 0) return;
        const list = res?.data["ROBOT_SERIES"] || [];
        this.robotSeries = list.map(item => ({ label: item.fieldCode, value: item.fieldValue }));
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
  :deep(.gp-form-item) {
    width: 146px;
  }
}
</style>
