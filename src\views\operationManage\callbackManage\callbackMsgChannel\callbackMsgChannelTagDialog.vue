<template>
  <gp-dialog
    :title="$t('lang.rms.fed.callbackConfigChannelTag')"
    :visible.sync="channelTagDialog"
    width="80%"
    :before-close="closeDialog"
    center
  >
    <div class="app-container">
      <gp-card>
        <gp-form label-position="top" label-width="80px" :model="searchData">
          <gp-row :gutter="20">
            <gp-col :span="4" class="btnwarp">
              <gp-button @click="itemClick({}, false)">{{ $t("lang.rms.fed.add") }}</gp-button>
            </gp-col>
          </gp-row>
        </gp-form>
      </gp-card>

      <gp-dialog :title="$t('lang.rms.fed.edit')" :visible.sync="editDialog" center :append-to-body="true" width="50%">
        <gp-form ref="ruleForm" label-position="top" label-width="80px" :model="itemData" class="padding_20">
          <gp-form-item :label="$t('lang.rms.fed.channelId')">
            <gp-input v-model="propsChannelId" type="text" readonly="readonly" />
          </gp-form-item>
          <gp-form-item
            :label="$t('lang.rms.fed.callbackChannelTagProperty')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="property"
          >
            <gp-input v-model="itemData.property" type="text" />
          </gp-form-item>
          <gp-form-item
            :label="$t('lang.rms.fed.callbackChannelTagFilterType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseChoose') }]"
            prop="filterType"
          >
            <gp-select v-model="itemData.filterType">
              <gp-option v-for="(item, key) in filterTypeList" :key="key" :label="item" :value="key" />
            </gp-select>
          </gp-form-item>
          <gp-form-item
            :label="$t('lang.rms.fed.callbackChannelTagMatchType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseChoose') }]"
            prop="matchType"
          >
            <gp-select v-model="itemData.matchType">
              <gp-option v-for="(item, key) in matchTypeList" :key="key" :label="item" :value="key" />
            </gp-select>
          </gp-form-item>
          <gp-form-item
            :label="$t('lang.rms.fed.callbackChannelTagPattern')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="pattern"
          >
            <gp-input v-model="itemData.pattern" type="text" />
          </gp-form-item>
        </gp-form>
        <span slot="footer" class="dialog-footer">
          <gp-button @click="closeEditDialog">{{ $t("lang.common.cancel") }}</gp-button>
          <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
        </span>
      </gp-dialog>

      <gp-card class="mt-20">
        <!-- 列表信息 -->
        <gp-table :data="tableData" style="width: 100%">
          <gp-table-column :label="$t('lang.rms.fed.lineNumber')" width="50" align="center" type="index" />
          <!--通道ID-->
          <gp-table-column prop="channelId" :label="$t('lang.rms.fed.channelId')" align="center" />
          <gp-table-column prop="property" :label="$t('lang.rms.fed.callbackChannelTagProperty')" align="center" />
          <!-- 过滤类型 -->
          <gp-table-column prop="filterType" :label="$t('lang.rms.fed.callbackChannelTagFilterType')" align="center">
            <template slot-scope="scope">{{ filterTypeList[scope.row.filterType] }}</template>
          </gp-table-column>
          <!-- 值匹配类型 -->
          <gp-table-column prop="matchType" :label="$t('lang.rms.fed.callbackChannelTagMatchType')" align="center">
            <template slot-scope="scope">{{ matchTypeList[scope.row.matchType] }}</template>
          </gp-table-column>
          <!-- 匹配值 -->
          <gp-table-column prop="pattern" :label="$t('lang.rms.fed.callbackChannelTagPattern')" align="center" />

          <gp-table-column :label="$t('lang.rms.fed.operation')" align="center">
            <template slot-scope="scope">
              <gp-button type="text" size="small" @click="itemClick(scope.row, true)">
                {{ $t("lang.rms.fed.edit") }}
              </gp-button>
              <gp-button type="text" size="small" @click="deleteData(scope.row, true)">
                {{ $t("lang.rms.fed.delete") }}
              </gp-button>
            </template>
          </gp-table-column>
        </gp-table>
        <gp-pagination
          :current-page="pageOption.pagecurrent"
          :page-sizes="[10, 25, 50, 100]"
          :page-size="pageOption.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="taskCount"
          @size-change="pageSizeChange"
          @current-change="goPage"
        />
      </gp-card>
    </div>
  </gp-dialog>
</template>
<script>
export default {
  name: "CallbackChannelTag",
  components: {},
  props: {
    channelTagDialog: {
      type: Boolean,
      default() {
        return false;
      },
    },
    itemChannelData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      // 搜索内容
      searchData: {
        channelId: "",
        channelType: "",
      },

      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      pageOption: {
        // 当前页数
        pagecurrent: 1,
        // 每页展示数
        pagesize: 10,
      },
      itemData: {},
      filterTypeList: {
        AND: "AND",
        OR: "OR",
        NOT: "NOT",
      },
      matchTypeList: {
        REGEX: this.$t("lang.rms.fed.callbackChannelTagMatchTypeRegex"),
        EQUAL: this.$t("lang.rms.fed.callbackChannelTagMatchTypeEqual"),
      },
      isEdit: false,
      editDialog: false,
      propsChannelId: undefined,
    };
  },
  computed: {},
  watch: {
    itemChannelData(data) {
      this.propsChannelId = data.channelId;
    },
  },
  created() {
    this.getTableList();
    this.propsChannelId = this.itemChannelData.channelId;
  },
  methods: {
    /* 翻页 */
    pageSizeChange(data) {
      // this.pageOption.pagecurrent = 1
      this.pageOption.pagesize = data;
      this.getTableList();
    },
    // 跳页
    goPage(pageCurrent) {
      this.pageOption.pagecurrent = pageCurrent;
      this.getTableList();
    },

    // 编辑
    itemClick(data, isEdit) {
      this.isEdit = isEdit;
      this.editDialog = true;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs["ruleForm"].resetFields();
        this.itemData = data;
      });
    },
    // 机器人参数配置
    itemRobotRecord(data) {
      this.itemData = data;
    },
    deleteData(data) {
      const msgChannelTagIds = [];
      msgChannelTagIds.push(data.id);
      const para = { msgChannelTagIds };
      $req.post("/athena/apiCallback/deleteMsgChannelTag", para).then(json => {
        if (json.code === 0) {
          this.$message.success(this.$t(json.msg));
          this.getTableList();
        }
      });
    },
    // 重置搜索参数
    resetSearchData() {
      for (const key in this.searchData) {
        if (Object.prototype.hasOwnProperty.call(this.searchData, key)) {
          this.searchData[key] = "";
        }
      }
      this.onSearch();
    },
    // 查询
    onSearch() {
      this.pageOption.pagecurrent = 1;
      this.getTableList();
    },
    getTableList() {
      const data = { language: $utils.Data.getLocalLang() };
      const pageData = "?currentPage=" + this.pageOption.pagecurrent + "&pageSize=" + this.pageOption.pagesize;
      data.channelId = this.itemChannelData.channelId;
      $req.post("/athena/apiCallback/msgChannelTagPageList" + pageData, data).then((data = {}) => {
        const { currentPage = 0, pageSize = 0, recordCount = 0, recordList = [] } = data.data || {};
        this.tableData = recordList.map(item => {
          const descr = this.$t(item.descr);
          return { ...item, descr };
        });
        // id
        this.pageOption.pagesize = pageSize;
        this.pageOption.pagecurrent = currentPage === 0 ? 1 : currentPage;
        this.taskCount = recordCount;
      });
    },
    // 保存通道规则
    save() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          const data = this.itemData;
          data.channelId = this.itemChannelData.channelId;
          data.msgChannelTagId = this.itemData.id;
          $req.post("/athena/apiCallback/saveMsgChannelTag", data).then(json => {
            if (json.code === 0) {
              this.$message.success(this.$t(json.msg));
              this.editDialog = false;
              this.getTableList();
            }
          });
        }
      });
    },
    closeDialog() {
      this.$emit("update:channelTagDialog", false);
    },
    closeEditDialog() {
      this.editDialog = false;
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.btnwarp {
  padding: 43px 0 0;
}
</style>
