<template>
  <div class="sec-flex-box">
    <section class="library-list">
      <h5 class="title">
        <span>{{ $t("lang.rms.config.page.templateOverview") }}</span>
        <!-- <gp-input
          v-model="libraryName"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
          class="input-with-select"
        >
          <gp-button slot="append" icon="gp-icon-search" @click="getLibraries" />
        </gp-input> -->
        <div @click="editLibrary('add')">+</div>
      </h5>
      <ul>
        <li
          v-for="(item, index) in libraries"
          :key="index"
          :class="{ active: item.id === currentId }"
          @click="libSelected(item)"
          :title="item.name"
        >
          {{ item.name }}
        </li>
      </ul>
    </section>

    <library-detail
      class="library-detail"
      :current-library="currentLibrary"
      @updatedLibrary="updatedLibrary"
      @updatedLibraryParams="updatedLibraryParams"
    />
    <dialog-library-edit ref="libraryEdit" @updatedLibrary="updatedLibrary" />
  </div>
</template>
<script>
import libraryDetail from "./components/libraryDetail";
import DialogLibraryEdit from "./components/dialogLibraryEdit";
export default {
  name: "ParamLibrary",
  components: { libraryDetail, DialogLibraryEdit },
  data() {
    return {
      libraryName: "",
      libraries: [],
      currentId: null,
      currentLibrary: {},
    };
  },

  watch: {
    "$i18n.locale"(newLocale, oldLocale) {
      console.log("renderLibraries", this.libraries);
      this.getLibraries();
    },
  },
  activated() {
    this.getLibraries();
  },
  methods: {
    libSelected(item) {
      if (this.currentId === item.id) return;
      this.currentId = item.id;
      this.getCurrentLibrary();
    },
    updatedLibrary(id) {
      this.libraryName = "";
      this.currentId = id;
      this.getLibraries("updated");
      this.$emit("updatedLibrary");
    },
    updatedLibraryParams(id, data) {
      this.currentId = id;
      const type = Number(data.searchType);
      const params = {
        searchType: type,
        search: type === 3 ? Number(data.isImmediate) : data.searchValue,
        language: data.language,
      };
      this.getCurrentLibraryParams(params);
    },
    // 请求库列表
    getLibraries(type) {
      let params = { search: this.libraryName };
      $req
        .post("/athena/config/template/list", $utils.Tools.getParams(params), {
          headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" },
        })
        .then(res => {
          if (res.code !== 0) return;
          let libraries = res.data;
          if (libraries.length <= 0) return;

          this.libraries = libraries;
          if (this.currentId === null || type !== "updated") this.currentId = libraries[0].id;
          this.getCurrentLibrary();
        });
    },
    // 请求指定的配置库
    getCurrentLibrary() {
      $req
        .post(
          "/athena/config/template/show?templateId=" + this.currentId,
          // $utils.Tools.getParams(data),
          { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
        )
        .then(res => {
          if (res.code !== 0) return;
          this.currentLibrary = res.data;
        });
    },

    // 请求指定的配置库
    getCurrentLibraryParams(data) {
      $req
        .post("/athena/config/template/show?templateId=" + this.currentId, $utils.Tools.getParams(data), {
          headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" },
        })
        .then(res => {
          if (res.code !== 0) return;
          this.currentLibrary = res.data;
        });
    },
    editLibrary(type) {
      let template = this.currentLibrary.template;
      this.$refs.libraryEdit.open(type, template);
    },
  },
};
</script>
<style lang="less" scoped>
.sec-flex-box {
  border-top: 15px solid #f7f8fa;
  display: flex;
  height: 100%;
  .g-box-shadow-no-bottom();
  // position: absolute;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;

  .library-detail {
    height: 100%;
    border-top: 0;
    overflow: auto;
  }

  .library-list {
    border-right: 2px solid #eee;
    display: flex;
    flex-direction: column;

    > h5.title {
      padding: 15px 10px;
      span {
        font-weight: 500;
        font-family: "PingFang SC";
        font-style: normal;
        font-size: 14px;
        color: #313234;
        margin-left: 6px;
        position: relative;
        top: 2px;
      }
      div {
        font-size: 33px;
        color: #a7a5a5;
        font-weight: 200;
        width: 30px;
        height: 30px;
        background-color: #fafbfc;
        border: 1px solid #e6e7ea;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        margin-top: -2px;
        cursor: pointer;
        float: right;
        margin-right: 10px;
      }
    }

    > ul {
      flex: 1;
      overflow: auto;
      margin-top: 5px;
      li {
        padding: 12px 12px;
        font-size: 14px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        color: #686c71;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.active {
          color: #409eff;
          background: #f7f8fa;
          border-right: 2px solid #409eff;
        }
      }
    }
  }

  .library-detail {
    width: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }
}
</style>
