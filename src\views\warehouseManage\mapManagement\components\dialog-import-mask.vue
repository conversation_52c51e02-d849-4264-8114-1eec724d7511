<template>
  <div class="app-container import-mask">
    <gp-dialog
      :title="dialogImportMask.title"
      :visible.sync="visible"
      border
      width="540px"
      :close-on-click-modal="false"
    >
      <gp-form ref="formData" label-width="80px">
        <gp-row>
          <gp-col :span="12">
            <gp-form-item :label="`${$t('lang.rms.fed.maskResource')}:`">
              <gp-upload
                ref="uploadMap"
                action="https://httpbin.org/post"
                accept=".jpg,.png"
                :show-file-list="false"
                :limit="1"
                :on-success="handleSuccess"
                :before-upload="beforeUpload"
              >
                <gp-button type="text" size="small">
                  {{ $t("lang.rms.fed.maskImportFile") }}
                </gp-button>
              </gp-upload>
              <span class="file-tip">{{ $t("lang.rms.fed.maskImportTip1") }}</span>
            </gp-form-item>
          </gp-col>
        </gp-row>
        <gp-row>
          <gp-col>
            <gp-form-item :label="`${$t('lang.rms.fed.showMask')}:`">
              <div v-if="maskImg" class="show-mask">
                <gp-icon name="gp-icon-circle-close" @click="onDeleteImg" />
                <img :src="maskImg" alt="" />
              </div>
            </gp-form-item>
          </gp-col>
        </gp-row>
        <gp-row>
          <gp-col>
            <gp-form-item :label="`${$t('lang.rms.fed.showLocation')}:`">
              <div class="location-tip">{{ $t("lang.rms.fed.showLocationTip") }}map.upload.maskOffset</div>
            </gp-form-item>
          </gp-col>
        </gp-row>
      </gp-form>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="clearAddForm" plain>{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" @click="handleSubmit">
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
  name: "DialogImportMask",
  data() {
    return {
      dialoVisible: false,
      maskImg: null,
      msgToast: null,
      formData: {
        mapId: "",
        floorId: "",
      },
    };
  },
  computed: {
    ...mapState("mapManagement", ["dialogImportMask"]),
    visible: {
      get() {
        return this.dialogImportMask.visible;
      },
      set(val) {
        const { visible } = this.dialogImportMask;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogImportMask.rowData;
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.formData = {
            mapId: this.dialogImportMask.rowData.id,
            floorId: this.dialogImportMask.floorId,
          };
          this.getMapMask();
        }
      },
      immediate: true,
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["hideDialog"]),
    getMapMask() {
      $req
        .get("/athena/map/draw/getMapMask", {
          mapId: this.formData.mapId,
          floorId: this.formData.floorId,
        })
        .then(res => {
          this.maskImg = res.data;
        });
    },
    handleSubmit() {
      $req
        .post("/athena/map/draw/saveMapMask", {
          mapId: this.formData.mapId,
          floorId: this.formData.floorId,
          maskImage: this.maskImg,
        })
        .then(() => {
          this.$emit("refreshList");
          this.clearAddForm();
        });
    },
    clearAddForm() {
      this.maskImg = null;
      this.visible = false;
    },
    // 删除
    onDeleteImg() {
      this.maskImg = null;
    },
    handleSuccess(res, file) {
      this.msgToast.close();
      this.$message.success(this.$t("lang.rms.fed.maskImportTip3"));
      this.maskImg = res.files.file;
      this.$refs.uploadMap.clearFiles();
    },
    beforeUpload(file) {
      const isltImg = ["image/png", "image/jpg", "image/jpeg"].includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 2;

      this.msgToast = this.$message({
        showClose: true,
        duration: 0,
        message: this.$t("lang.rms.fed.maskImportTip2"),
        type: "warning",
      });

      if (!isltImg) {
        this.$message.error(this.$t("lang.rms.fed.uploadFileLimitType"));
        this.msgToast.close();
      }
      if (!isLt2M) {
        this.$message.error(this.$t("lang.rms.fed.uploadFileLimit2M"));
        this.msgToast.close();
      }
      return isltImg && isLt2M;
    },
  },
};
</script>

<style lang="less">
.import-mask {
  .gp-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: calc(100% - 30px);
  }
  .gp-dialog__body {
    padding: 15px 25px 15px 20px;
    flex: 1;
    overflow: auto;
  }
  .file-tip {
    color: #999;
    font-size: 13px;
  }
  .show-mask {
    position: relative;
    padding: 10px 16px 0 0;
    i {
      font-size: 18px;
      font-weight: bolder;
      position: absolute;
      top: -5px;
      right: -5px;
      cursor: pointer;
    }
    img {
      width: 100%;
    }
  }
  .location-tip {
    margin-top: 5px;
    font-size: 12px;
    word-break: break-all;
    line-height: 20px;
  }
}
</style>
