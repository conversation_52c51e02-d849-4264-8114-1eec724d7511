/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerInspectionArea implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private errorContainer: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private errorFillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "areaInspection";
    container.interactiveChildren = false;
    container.visible = true;
    container.alpha = 0.4;
    container.zIndex = utils.getLayerZIndex("area");
    this.container = container;

    let errorContainer = new PIXI.Container();
    errorContainer.name = "errorAreaInspection";
    errorContainer.interactiveChildren = false;
    errorContainer.visible = true;
    errorContainer.alpha = 1;
    errorContainer.zIndex = utils.getLayerZIndex("area");
    this.errorContainer = errorContainer;

    this.fillStyle.visible = true;
    this.fillStyle.color = 0xff0000;
    // 异常颜色
    this.errorFillStyle.visible = true;
    this.errorFillStyle.color = 0x880000;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<code>, errorArr?: Array<code>): void {
    this.drawArea(arr, errorArr);
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.errorFillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
  }

  private drawArea(cellCodes: Array<code>, errorArr?: Array<code>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      lineStyle = _this.lineStyle,
      errorFillStyle = _this.errorFillStyle,
      fillStyle = _this.fillStyle;

    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = cellCodes.length;

    let options, nsPosition, rect;
    for (let i = 0, len = cellCodes.length; i < len; i++) {
      options = mapData.cell.getData(cellCodes[i]);
      if (!options) continue;
      nsPosition = options["nsPosition"];

      rect = new PIXI.Polygon(...nsPosition);
      graphicsGeometry.drawShape(rect, fillStyle, lineStyle);
    }
    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = "areaInspection" + this.floorId;
    graphics.floorId = this.floorId;
    graphics.mapType = "areaInspection";
    graphics.visible = true;
    graphics.interactive = graphics.buttonMode = true;
    // 异常数据渲染
    let errorGraphicsGeometry: any = new PIXI.GraphicsGeometry();
    errorGraphicsGeometry.BATCHABLE_SIZE = errorArr.length;

    let errorOptions, errorNsPosition, errorRect;
    for (let i = 0, len = errorArr.length; i < len; i++) {
      errorOptions = mapData.cell.getData(errorArr[i]);
      if (!errorOptions) continue;
      errorNsPosition = errorOptions["nsPosition"];

      errorRect = new PIXI.Polygon(...errorNsPosition);
      graphicsGeometry.drawShape(errorRect, errorFillStyle, lineStyle);
    }
    const errorGraphics: any = new PIXI.Graphics(graphicsGeometry);
    errorGraphics.name = "errorAreaInspection" + this.floorId;
    errorGraphics.floorId = this.floorId;
    errorGraphics.mapType = "errorAreaInspection";
    errorGraphics.visible = true;
    errorGraphics.interactive = errorGraphics.buttonMode = true;

    this.meshList.push(graphics);
    this.meshList.push(errorGraphics);
    this.errorContainer.addChild(errorGraphics);
    this.container.addChild(graphics);
    this.container.addChild(this.errorContainer);
  }
}
export default LayerInspectionArea;
