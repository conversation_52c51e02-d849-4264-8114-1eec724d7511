<template>
  <!-- 编辑任务 -->
  <gp-form
    ref="form"
    class="mform"
    label-position="right"
    label-width="140px"
    size="mini"
    :rules="editTaskRules"
    :model="editTaskData"
  >
    <gp-form-item :label="$t('lang.rms.fed.trayModelName')" prop="modelName">
      <gp-input class="w200" v-model="editTaskData.modelName" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 外部编号 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="editDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- sizeType -->
    <gp-form-item prop="sizeTypes" :label="$t('lang.rms.fed.supportedSizeType')">
      <sizeTypeInput :value.sync="editTaskData.sizeTypes" @change="sizeTypesChange" />
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        @change="needSendRobotChange"
        :placeholder="$t('lang.rms.fed.choose')"
      >
        <gp-option :label="$t('lang.rms.fed.no')" :value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" :value="1" />
      </gp-select>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.containerManage.sendModelId.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text"><gp-icon name="gp-icon-question" /></gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        step-strictly
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="editDisabled || String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.fed.trayStructure')">
      <gp-radio-group v-model="editTaskData.extendJson.structure" @change="structureChange">
        <gp-radio label="doubleHole">{{ $t("lang.rms.fed.doubleHoleTray") }}</gp-radio>
        <gp-radio label="singleHole">{{ $t("lang.rms.fed.singleHoleTray") }}</gp-radio>
      </gp-radio-group>
    </gp-form-item>
    <gp-form-item prop="extendJson.recognitionDistance" :label="$t('lang.rms.fed.recognitionDistance')">
      <gp-input-number
        step-strictly
        class="w200"
        v-model="editTaskData.extendJson.recognitionDistance"
        :min="0"
        size="mini"
        :step="1"
      />
      mm
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.fed.trayMaterial')">
      <gp-select class="w200" v-model="editTaskData.extendJson.material" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option v-for="item in materialDict" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </gp-select>
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.fed.trayColor')">
      <gp-select class="w200" v-model="editTaskData.extendJson.colour" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option v-for="item in colorsDict" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </gp-select>
    </gp-form-item>
    <gp-form-item prop="extendJson.palletOutsite.length" :label="$t('lang.rms.fed.traySurface')" label-width="80px">
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.length") }}
        <gp-input-number
          step-strictly
          v-model="editTaskData.extendJson?.palletOutsite.length"
          :min="0"
          size="mini"
          :step="1"
        />
        mm
      </div>
    </gp-form-item>
    <gp-form-item prop="extendJson.palletOutsite.width" :label="$t('lang.rms.fed.traySurface')" label-width="80px">
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.textWidth") }}
        <gp-input-number
          step-strictly
          v-model="editTaskData.extendJson?.palletOutsite.width"
          :min="0"
          size="mini"
          :step="1"
        />
        mm
      </div>
    </gp-form-item>
    <gp-form-item prop="extendJson.edgeColumn.width" :label="$t('lang.rms.fed.edgeColumn')" label-width="80px">
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.textWidth") }}
        <gp-input-number
          step-strictly
          v-model="editTaskData.extendJson.edgeColumn.width"
          :min="0"
          size="mini"
          :step="1"
        />
        mm
      </div>
    </gp-form-item>
    <gp-form-item
      prop="extendJson.hole.middleColumnWidth"
      v-if="editTaskData.extendJson.structure === 'doubleHole'"
      :label="$t('lang.rms.fed.middleColumn')"
      label-width="80px"
    >
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.textWidth") }}
        <gp-input-number
          step-strictly
          v-model="editTaskData.extendJson.hole.middleColumnWidth"
          :min="0"
          size="mini"
          :step="1"
        />
        mm
      </div>
    </gp-form-item>
    <gp-form-item prop="extendJson.hole.length" :label="$t('lang.rms.fed.hole')" label-width="80px">
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.high") }}
        <gp-input-number step-strictly v-model="editTaskData.extendJson.hole.length" :min="0" size="mini" :step="1" />
        mm
      </div>
    </gp-form-item>
    <gp-form-item prop="extendJson.hole.width" :label="$t('lang.rms.fed.hole')" label-width="80px">
      <div class="taskFromItemSty">
        {{ $t("lang.rms.fed.textWidth") }}
        <gp-input-number step-strictly v-model="editTaskData.extendJson.hole.width" :min="0" size="mini" :step="1" />
        mm
      </div>
    </gp-form-item>
  </gp-form>
</template>

<script>
import { mapMutations, mapActions, mapState } from "vuex";
import sizeTypeInput from "./sizeTypeInput.vue";
const getDefaultData = () => {
  return {
    modelName: "",
    modelType: "",
    sizeTypes: "",
    sendModelId: 0,
    needSendRobot: 0,
    modelCategory: "PALLET",
    extendJson: {
      structure: "singleHole",
      recognitionDistance: 1350,
      material: "",
      colour: "",
      palletOutsite: {
        length: 1,
        width: 1,
      },
      edgeColumn: {
        width: 1,
      },
      hole: {
        length: 1,
        width: 1,
        middleColumnWidth: 100, // 中间立柱
      },
    },
  };
};

export default {
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 编辑任务数据
      editTaskData: getDefaultData(),
      // loading
      saveLoading: false,
      sizeTypeParamTip: null,
    };
  },
  components: { sizeTypeInput },
  computed: {
    ...mapState("containerModal", ["shelfCategoryDict", "editData"]),
    editDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    curShelfCategoryDict() {
      return this.shelfCategoryDict.filter(item => item.type === "PALLET_SHELF");
    },
    materialDict() {
      return [
        { label: "lang.rms.fed.textureMaterialWood", value: "wood" },
        { label: "lang.rms.fed.textureMaterialPlastic", value: "plastic" },
        { label: "lang.rms.fed.textureMaterialMetal", value: "metal" },
        { label: "lang.rms.fed.textureMaterialOther", value: "other" },
      ];
    },
    colorsDict() {
      return [
        { label: "lang.rms.fed.color.wood", value: "wood" },
        { label: "lang.rms.fed.color.blue", value: "blue" },
        { label: "lang.rms.fed.color.black", value: "black" },
        { label: "lang.rms.fed.color.gray", value: "gray" },
        { label: "lang.rms.fed.color.other", value: "other" },
      ];
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };
      return {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        // sizeTypes: [{
        //   // 正则 只允许输入英文，数字，和标点符号，限制15字符之内
        //   pattern: /^[a-zA-Z0-9\-\_]{0,15}$/,
        //   message: this.$t("lang.rms.fed.enter15CharactersOr01"),
        //   trigger: "blur",
        // }],
        "extendJson.recognitionDistance": [requiredRule],
        "extendJson.palletOutsite.length": [requiredRule],
        "extendJson.palletOutsite.width": [requiredRule],
        "extendJson.edgeColumn.width": [requiredRule],
        "extendJson.hole.width": [requiredRule],
        "extendJson.hole.length": [requiredRule],
        "extendJson.hole.middleColumnWidth": [requiredRule],
      };
    },
  },
  created() {
    this.fetchShelfCategory();
    if (this.editData.id) {
      this.editTaskData = JSON.parse(JSON.stringify(this.editData));
      const { palletOutsite, edgeColumn, hole } = this.editTaskData.extendJson;

      if (!palletOutsite) {
        this.editTaskData.extendJson.palletOutsite = {
          length: 1,
          width: 1,
        };
      }

      if (!edgeColumn) {
        this.editTaskData.extendJson.edgeColumn = {
          width: 1,
        };
      }

      if (!hole) {
        this.editTaskData.extendJson.hole = {
          length: 1,
          width: 1,
          middleColumnWidth: 100, // 中间立柱
        };
      }
    }
  },
  watch: {
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
    "editTaskData.sizeTypes"(val) {
      let valList = val ? val.split(",") : [];
      if (val) this.sizeTypesChange(valList);
    },
  },
  methods: {
    ...mapActions("containerModal", ["fetchShelfCategory"]),
    ...mapMutations("containerModal", ["setTrayModelView"]),
    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null;
        return;
      }
      const reg = /^[a-zA-Z]{0,15}$/;
      if (data) {
        data.forEach(item => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null;
          } else {
            this.sizeTypeParamTip = this.$t("lang.rms.fed.enterEnglish15Characters");
          }
        });
      }
    },
    structureChange(value) {
      if (value === "singleHole") {
        delete this.editTaskData.extendJson.hole.middleColumnWidth;
      } else {
        this.editTaskData.extendJson.hole.middleColumnWidth = 100;
      }
    },
    needSendRobotChange(value) {
      if (value) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },
    async validateData() {
      try {
        await this.$refs.form.validate();
        const { editTaskData } = this;

        // 仅查看
        if (this.editDisabled) {
          this.setTrayModelView("mainView");
          return;
        }

        // 如果是单孔, 删除中间立柱数据
        if (editTaskData.extendJson.structure === "singleHole") {
          delete editTaskData.extendJson.hole.middleColumnWidth;
        }

        if (editTaskData.needSendRobot === 0) {
          editTaskData.sendModelId = 0;
        }

        return editTaskData;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.mform {
  flex: 1;
  overflow: auto;
  position: relative;
  padding-bottom: 10px;
}

.editTask {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  width: 350px;
}

.floor {
  text-align: center;
}

:deep(.gp-radio) {
  margin-right: 15px;
}

.sizeType {
  min-width: 200px;
  max-width: 100%;
}

.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
