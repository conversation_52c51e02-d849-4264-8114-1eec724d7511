<template>
  <div>
    <gp-dropdown trigger="click" class="geek-user-info" @command="handleCommand" placement="bottom-end">
      <div class="gp-header__menu-item">
        {{ name }}
        <gp-icon name="gp-icon-caret-bottom"></gp-icon>
      </div>
      <gp-dropdown-menu slot="dropdown">
        <gp-dropdown-item command="toDashboard">
          {{ $t("lang.rms.fed.first") }}
        </gp-dropdown-item>
        <gp-dropdown-item v-if="permission" command="openChangePassword" divided>
          {{ $t("lang.rms.fed.changePassword") }}
        </gp-dropdown-item>
        <gp-dropdown-item command="logout" divided>
          {{ $t("lang.rms.fed.exit") }}
        </gp-dropdown-item>
      </gp-dropdown-menu>
    </gp-dropdown>
    <ChangePasswordPop
      v-if="permission"
      :change-password-show="changePasswordShow"
      @updateVisible="val => (changePasswordShow = val)"
    />
  </div>
</template>

<script>
import ChangePasswordPop from "@/views/login/components/changePasswordPop";

export default {
  name: "GeekUserInfo",
  components: { ChangePasswordPop },
  data() {
    const permission = $utils.Data.getRMSPermission();
    return {
      name: $utils.Data.getUserInfo(),
      permission,
      changePasswordShow: false,
    };
  },

  methods: {
    handleCommand(command) {
      if (command === "logout") {
        this.logout();
      } else if (command === "toDashboard") {
        this.toDashboard();
      } else if (command === "openChangePassword") {
        this.changePasswordShow = true;
      }
    },
    logout() {
      const logoutUrl = $utils.Data.getLogoutUrl();
      $req.post("/athena/api/coreresource/auth/logout/v1").then(res => {
        $utils.Data.removeAllStorage();
        if (logoutUrl) {
          const url = window.location.href;
          window.location.href = `${logoutUrl}?redirect_uri=${encodeURIComponent(url)}`;
        } else {
          const redirectUrl = window.location.origin + window.location.pathname + "#/dashboard";
          window.location.replace(`/athena/auth/index.html#/login?redirect_url=${redirectUrl}`);
        }
        // $utils.Data.removeAllStorage();
        // this.$router.push("/login");
        // const redirectUrl = window.location.origin + window.location.pathname + '#/dashboard'
        // window.location.replace(`/athena/auth/index.html#/login?redirect_url=${redirectUrl}`)
      });
    },
    toDashboard() {
      if (this.$route.name === "dashboard") return;
      this.$router.push("/dashboard");
    },
  },
};
</script>

<style lang="less" scoped>
.geek-user-info {
  cursor: pointer;
  margin-right: 0px !important;
  padding: 1px 0;
  .gp-header__menu-item {
    margin: 0px;
    // padding: 1px 0;
  }

  .avatar-wrapper {
    .g-flex();
    line-height: 50px;
    min-width: 20px;
    justify-content: flex-end;
    font-size: 13px;
    color: @g-nav-right-color;
  }
}
</style>
