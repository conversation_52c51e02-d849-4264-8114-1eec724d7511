<template>
  <div class="app-container shelf-manage-panel-wrap">
    <gp-form ref="customizeForm" label-position="top" :inline="true" label-width="80px">
      <gp-form-item :label="$t('lang.rms.fed.adjust.log.startTime')">
        <gp-date-picker
          v-model="dataRange"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('lang.rms.fed.startTime')"
          :end-placeholder="$t('lang.rms.fed.endTime')"
        />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.adjust.log.crossArea')" :placeholder="$t('lang.rms.fed.pleaseChoose')">
        <gp-select v-model="crossAreaFlag">
          <gp-option value="" :label="$t('lang.rms.fed.wholeStatus')" />
          <gp-option value="0" :label="$t('lang.rms.fed.adjust.disabledDiffArea')" />
          <gp-option value="1" :label="$t('lang.rms.fed.adjust.enabledDiffArea')" />
        </gp-select>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.adjust.log.shelfScore.from')">
        <gp-select v-model="shelfScoreFrom">
          <gp-option value="" :label="$t('lang.rms.fed.wholeStatus')" />
          <gp-option :value="0" label="wms" />
          <gp-option :value="1" label="ems" />
        </gp-select>
      </gp-form-item>

      <gp-form-item class="operation-button">
        <gp-button type="primary" @click="onQuery">
          {{ $t("lang.rms.fed.query") }}
        </gp-button>
        <gp-button @click="onReset">{{ $t("lang.rms.fed.reset") }}</gp-button>
      </gp-form-item>
    </gp-form>

    <div class="shelf-manage-panel-wrap__table">
      <geek-customize-table
        :key="tableIndex"
        ref="customizeTableRef"
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
      >
        <template #rowExpand="{ row }">
          <table-expand :row-data="row" />
        </template>
        <template #operation="{ row }">
          <gp-button type="text" @click="showDetail(row)">
            {{ $t("lang.rms.fed.adjustDetailBtn") }}
          </gp-button>
        </template>
      </geek-customize-table>
    </div>
  </div>
</template>
<script>
import tableExpand from "./table-expand.vue";

export default {
  name: "shelfStaticHistory",
  components: {
    tableExpand,
  },
  data() {
    return {
      tableIndex: 0,
      dataRange: "",
      crossAreaFlag: "",
      shelfScoreFrom: "",
      tableExpandRowKeys: [],

      tableConfig: {
        attrs: {
          index: true,
          "row-key": "id",
          // ref: "tableRef",
          expandRowKeys: this.tableExpandRowKeys,
        },
        expand: { slotName: "rowExpand" },
        columns: [
          { 
            label: "lang.rms.fed.adjust.log.adjustArea",
            prop: "adjustAreas",
            formatter: (row, column) => {
              if (row[column]) {
                return row[column].join(",");
              } else {
                return row[column];
              }
            },
          },
          {
            label: "lang.rms.fed.adjust.log.crossArea",
            prop: "crossAreaFlag",
            formatter: (row, column) => {
              if (row[column]) {
                return this.$t("lang.rms.fed.adjust.enabledDiffArea");
              } else {
                return this.$t("lang.rms.fed.adjust.disabledDiffArea");
              }
            },
          },
          { label: "lang.rms.fed.adjust.log.adjustShelf.num", prop: "adjustShelfNum" },
          { 
            label: "lang.rms.fed.adjust.log.shelfScore.from", 
            prop: "shelfScoreFrom",
            formatter: (row, column) => {
              if (row[column]) {
                return "ems";
              } else {
                return "wms";
              }
            },
          },
          { 
            label: "lang.rms.fed.adjust.log.startTime", 
            prop: "createTime",
            formatter: (row, column) => {
              if (!row[column]) {
                return null;
              }
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            prop: "operation",
            slotName: "operation",
            width: "120",
            className: "operation-btn",
          },
        ],
      },
      tableData: [],
      // tableData: [
      //   {
      //     "id": 11,
      //     "adjustAreas": null,
      //     "crossAreaFlag": null,
      //     "adjustShelfNum": 1,
      //     "shelfScoreFrom": true,
      //     "createTime": "2022-03-31 20:56:21",
      //     "finishTime": "2022-03-31 20:56:21",
      //     "shelfAdjustDetailEntities": [
      //       {
      //         "id": 9,
      //         "recordId": 11,
      //         "shelfCode": "dd",
      //         "shelfScore": null,
      //         "shelfOldArea": null,
      //         "shelfOldZone": null,
      //         "shelfOldPlacement": null,
      //         "shelfNewArea": null,
      //         "shelfNewPlacement": null,
      //         "shelfNewZone": null,
      //         "createTime": null,
      //         "finishTime": null
      //       }
      //     ]
      //   },
      //   {
      //     "id": 10,
      //     "adjustAreas": null,
      //     "crossAreaFlag": null,
      //     "adjustShelfNum": 1,
      //     "shelfScoreFrom": true,
      //     "createTime": "2022-03-31 20:56:21",
      //     "finishTime": "2022-03-31 20:56:21",
      //     "shelfAdjustDetailEntities": [
      //       {
      //         "id": 8,
      //         "recordId": 10,
      //         "shelfCode": "dd",
      //         "shelfScore": 1,
      //         "shelfOldArea": 2,
      //         "shelfOldZone": 3,
      //         "shelfOldPlacement": 4,
      //         "shelfNewArea": 5,
      //         "shelfNewPlacement": 6,
      //         "shelfNewZone": 7,
      //         "createTime": "2022-03-31 20:56:21",
      //         "finishTime": 9
      //       }
      //     ]
      //   }
      // ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      totalPage: 1,
      currentPage: 1,
      pageSize: 10,
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      let startTime = "",
        endTime = "";
      const dataRange = this.dataRange;
      if (dataRange) {
        startTime = new Date(dataRange[0]).getTime();
        endTime = new Date(dataRange[1]).getTime();
      }
      const params = {
        crossAreaFlag: this.crossAreaFlag,
        shelfScoreFrom: this.shelfScoreFrom,
        startTime,
        endTime,
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      $req.post("/athena/shelfStaticAdjust/recordList", params).then(res => {
        if (res.code !== 0 || !res.data || !res.data.content || !Array.isArray(res.data.content)) return;
        this.tableData = res.data.content;
        // this.totalPage = res.data.totalPages;
        this.tablePage = Object.assign({}, this.tablePage, {
          total: res.data.totalElements || 0,
        });
        const { customizeTableRef } = this.$refs;
        if (customizeTableRef) {
          const { tableData, tableExpandRowKeys } = this;
          tableExpandRowKeys.forEach(key => {
            const dataItem = tableData.find(item => item.id === key);
            if (dataItem) {
              const { tableRef } = customizeTableRef.$refs;
              tableRef && tableRef.toggleRowExpansion(dataItem, true);
              this.tableIndex++;
            }
          });
        }
      });
    },
    showDetail(rowData) {
      const { tableRef } = this.$refs.customizeTableRef.$refs;
      tableRef && tableRef.toggleRowExpansion(rowData, true);
    },
    onQuery() {
      this.tablePage.currentPage = 1;
      this.getTableList();
    },
    onReset() {
      this.tablePage.currentPage = 1;
      this.$refs.customizeForm.resetFields();

      this.dataRange = "";
      this.crossAreaFlag = "";
      this.shelfScoreFrom = "";
    },
    pageChange(page) {
      this.tablePage = Object.assign({}, this.tablePage, page);
      this.getTableList();
    },

    currentPageChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableList();
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableList();
    },
  },
};
</script>
<style lang="scss" scoped>
.operation-button {
  vertical-align: bottom;
}
</style>
