<template>
  <!-- 编辑任务 -->
  <gp-form
    class="mform"
    ref="mformRef"
    label-position="right"
    label-width="120px"
    size="mini"
    :model="editTaskData"
    :rules="editTaskRules"
  >
    <gp-form-item :label="$t('lang.rms.fed.palletFrameModelName')" prop="modelName">
      <gp-input class="w200" v-model="editTaskData.modelName" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 外部编号 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="editDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- sizeType -->
    <gp-form-item prop="sizeTypes" :label="$t('lang.rms.fed.supportedSizeType')">
      <sizeTypeInput :value.sync="editTaskData.sizeTypes" @change="sizeTypesChange" />
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.web.container.supportMove')" prop="move">
      <gp-select class="w200" v-model="editTaskData.move" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option :label="$t('lang.rms.web.container.canNotMove')" :value="0" />
        <gp-option :label="$t('lang.rms.web.container.canMove')" :value="1" />
      </gp-select>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.palletPositionManage.layer')" prop="layout">
      <gp-input-number step-strictly class="w200" v-model="editTaskData.layout" :min="1" :max="10000"></gp-input-number>
    </gp-form-item>

    <div class="detailBoxs">
      <gp-row class="detailRowItem" v-for="(item, index) in editTaskData.extendJson.detail" :key="index">
        <gp-form-item :prop="`extendJson.detail[${index}].height`" :label="$t('lang.rms.fed.high')" label-width="60px">
          <gp-input-number step-strictly v-model="item.height" :min="1" :max="10000"></gp-input-number>mm
        </gp-form-item>
      </gp-row>
    </div>
  </gp-form>
</template>

<script>
import { mapMutations, mapState, mapActions } from "vuex";
import sizeTypeInput from "./sizeTypeInput.vue";
const getDefaultData = () => {
  return {
    layout: 5,
    modelName: "",
    move: "",
    modelCategory: "PALLET_RACK",
    modelType: "",
    sizeTypes: "",
    extendJson: {
      detail: [...new Array(5)].map((data, index) => ({ layerNumber: index + 1, height: 1, index: index + 1 })),
    },
  };
};

export default {
  data() {
    return {
      // 编辑任务数据
      editTaskData: getDefaultData(),
      // loading
      saveLoading: false,
      sizeTypeParamTip: null,
    };
  },
  components: { sizeTypeInput },
  computed: {
    ...mapState("containerModal", ["shelfCategoryDict", "editData"]),
    editDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };
      const rule = {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        // sizeTypes: [{
        //   // 正则 只允许输入英文，数字，和标点符号，限制15字符之内
        //   pattern: /^[a-zA-Z0-9\-\_]{0,15}$/,
        //   message: this.$t("lang.rms.fed.enter15CharactersOr01"),
        //   trigger: "blur",
        // }],
        move: [requiredRule],
        layout: [requiredRule],
      };

      (this.editTaskData.extendJson.detail || []).forEach((item, index) => {
        rule[`extendJson.detail[${index}].height`] = [requiredRule];
      });

      return rule;
    },
  },
  created() {
    this.fetchShelfCategory();
    if (this.editData.id) {
      // 这里如果不脱离store, 编辑会报错
      this.editTaskData = JSON.parse(JSON.stringify(this.editData));
    }
  },
  watch: {
    "editTaskData.layout"(value) {
      const { extendJson } = this.editTaskData;
      const len = extendJson.detail?.length || 0;
      if (value > len) {
        const detail = [...new Array(value)].map((data, index) => ({
          layerNumber: index + 1,
          height: 1,
          index: index + 1,
        }));
        len && detail.splice(0, len, ...(extendJson.detail || []));
        extendJson.detail = detail;
      } else {
        extendJson.detail.splice(value);
      }
    },
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
    "editTaskData.sizeTypes"(val) {
      let valList = val ? val.split(",") : [];
      if (val) this.sizeTypesChange(valList);
    },
  },
  methods: {
    ...mapActions("containerModal", ["fetchShelfCategory"]),
    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null;
        return;
      }
      const reg = /^[a-zA-Z]{0,15}$/;
      if (data) {
        data.forEach(item => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null;
          } else {
            this.sizeTypeParamTip = this.$t("lang.rms.fed.enterEnglish15Characters");
          }
        });
      }
    },

    delLegs(index) {
      const legList = this.editTaskData.extendJson.legs;
      legList.splice(index, 1);
    },

    async validateData() {
      try {
        await this.$refs.mformRef.validate();
        return this.editTaskData;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.mform {
  flex: 1;
  position: relative;
  overflow: auto;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;
}
.detail {
  flex: 1;
  overflow: auto;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.sizeType {
  min-width: 200px;
  max-width: 100%;
}

.floor {
  text-align: center;
}

:deep(.gp-radio) {
  margin-right: 15px;
}

.detailBoxs {
  padding-top: 20px;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column-reverse;

  .detailRowItem {
    height: 40px;
    min-height: 40px;
  }
}

.delLegsItem {
  position: relative;

  .delLegsIcon {
    color: #f56c6c;
    cursor: pointer;
  }
}
.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
