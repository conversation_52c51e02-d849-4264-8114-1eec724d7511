<template>
  <div class="device-dialog">
    <gp-dialog
      :title="$t('lang.rms.fed.deviceInfo')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <div class="device-content">
        <div v-for="(item, index) in list" :key="index" class="form-item of-flex">
          <div class="form-item-input of-flex">
            <div class="label"><span class="of_red">*</span>IP</div>
            <gp-input v-model="item.ip" size="mini" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
          </div>
          <div class="form-item-input of-flex">
            <div class="label"><span class="of_red">*</span>PORT</div>
            <gp-input v-model="item.port" size="mini" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
          </div>
          <gp-button v-if="list.length !== 1" class="delete-btn" type="text" size="small" @click="onDelete(index)">{{
            $t("删除")
          }}</gp-button>
        </div>
        <div class="add-btn">
          <gp-button type="primary" size="small" @click="onAdd"
            >{{ $t("lang.venus.web.common.add") }}IP、PORT</gp-button
          >
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="onClose">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" :disabled="!isSave" @click="onSubmit">{{ $t("lang.rms.fed.save") }}</gp-button>
      </div>
    </gp-dialog>
  </div>
</template>
<script>
export default {
  props: {
    tableList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      list: [
        {
          ip: "",
          port: "",
        },
      ],
      dialogVisible: true,
    };
  },
  computed: {
    isSave() {
      let flag = true;
      if (this.list.length) {
        this.list.forEach(item => {
          if (item.ip === "" || item.port === "") {
            flag = false;
          }
        });
      } else {
        flag = false;
      }

      return flag;
    },
  },
  watch: {
    tableList: {
      handler(val) {
        if (val && val.length) {
          let arr = [];
          val.forEach(item => {
            arr.push(item);
          });
          this.list = arr;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  methods: {
    onAdd() {
      this.list.push({
        ip: "",
        port: "",
      });
    },
    onDelete(ind) {
      this.list.splice(ind, 1);
    },
    onClose() {
      this.$emit("close");
    },
    onSubmit() {
      let bindIps = [];
      this.list.forEach(item => {
        bindIps.push(`${item.ip}:${item.port}`);
      });
      const params = {
        bindIps: bindIps.join(";"),
      };
      $req.get("/athena/license/saveIp", params).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg);
          this.$emit("close", "success");
        }
      });
    },
  },
};
</script>

<style lang="less">
.device-dialog {
  .gp-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 650px;
    max-height: calc(100% - 30px);
  }
  .gp-dialog__body {
    padding: 15px 20px;
    min-height: 200px;
    flex: 1;
    overflow: auto;
  }
  .gp-input {
    width: 220px;
  }
  .of-flex {
    display: flex;
    align-items: center;
  }
  .form-item {
    margin-bottom: 10px;
  }
  .label {
    width: 60px;
    text-align: right;
    padding-right: 6px;
  }
  .of_red {
    color: red;
  }
  .add-btn {
    margin-left: 60px;
    margin-top: 16px;
  }
  .delete-btn {
    margin-left: 10px;
  }
}
</style>
