<template>
  <gp-menu
    :default-active="activeName"
    ref="commonNav"
    mode="horizontal"
    class="geek-tabs-menu"
    :class="{ 'tab-block': block }"
    @select="handleSelect"
  >
    <gp-menu-item v-for="item in navList" :index="item.id" :key="item.id" v-show="!item.hide">
      {{ $t(item.text) }}
    </gp-menu-item>
  </gp-menu>
</template>

<script>
export default {
  name: "GeekTabsNav",
  props: {
    block: { type: Boolean, require: false },
    navList: { type: Array, require: true },
    defaultActive: { type: String, default: "" },
  },
  data() {
    return {
      currentActive: "",
    };
  },
  computed: {
    activeName() {
      return this._getDefaultNav();
    },
  },
  mounted() {
    const defaultNav = this._getDefaultNav();
    if (defaultNav != this.currentActive) {
      this.trigger(defaultNav);
    }
  },
  activated() {
    const defaultNav = this._getDefaultNav();
    if (defaultNav != this.currentActive) {
      this.trigger(defaultNav);
    }
  },
  methods: {
    handleSelect(navName) {
      this.currentActive = navName;
      this.$emit("select", navName);
    },
    trigger(navName) {
      this.currentActive = navName;
      this.$refs.commonNav.activeIndex = navName;
    },

    _getDefaultNav() {
      const defaultNav = this.defaultActive || this.navList.length ? this.navList[0].id : "";

      return defaultNav;
    },
  },
};
</script>

<style lang="less">
@tab-nav-height: 38px;
.geek-tabs-menu {
  display: inline-block;

  > .gp-menu-item {
    margin: 0 10px;
    padding: 0 8px;
    height: 45px;
    line-height: 45px;
    color: #303133;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &.is-active,
    &.is-active:focus {
      color: #409eff;
    }
  }

  &.tab-block {
    width: 100%;
    padding: 0 12px;
    color: #fff;
    > .gp-menu-item {
      padding-top: 2px;
      height: @tab-nav-height;
      line-height: @tab-nav-height;
      font-size: 14px;
    }
  }
}
</style>
