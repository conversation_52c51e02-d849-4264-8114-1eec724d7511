<template>
  <geek-main-structure>
    <gp-tabs :value="activeName" @tab-click="tabsNavChange" ref="myTabs">
      <gp-tab-pane
        v-for="item in permissionNavList"
        :label="$t(item.text)"
        :name="item.id"
        :key="item.id"
      ></gp-tab-pane>
    </gp-tabs>
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import PackQuery from "./packQuery";
import LocationQuery from "./locationQuery";
import BoxHistorySearch from "./boxHistorySearch";
import RspBoxHistorySearch from "./rspBoxHistorySearch";
export default {
  components: { PackQuery, LocationQuery, BoxHistorySearch, RspBoxHistorySearch },
  data() {
    return {
      permissionNavList: [],
      navList: [
        { permissionName: "TabBoxSearchPage", id: "PackQuery", text: "auth.rms.packQuery.page" },
        { permissionName: "TabLatticeSearchPage", id: "LocationQuery", text: "lang.rms.fed.queryLattice" },
        { permissionName: "TabBoxTrackPage", id: "BoxHistorySearch", text: "lang.rms.fed.pppBoxHistorySearch" },
        { permissionName: "TabRspBoxTrackPage", id: "RspBoxHistorySearch", text: "lang.rms.fed.rspBoxHistorySearch" },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "boxManage"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(target) {
      this.activeName = this.$refs.myTabs.$data.currentName;
    },
  },
};
</script>
<style lang="less" scoped>
.rack-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
<style lang="less">
.rack-manage-panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);

  .rack-manage-panel-wrap__handle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  .rack-manage-panel-wrap__table {
    flex: 1;
  }
}
</style>
