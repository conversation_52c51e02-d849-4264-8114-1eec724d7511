/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";
import LayerRobotBox from "./robot-box";
import LayerRobotBelt from "./robot-belt";
import LayerRobotTrail from "./robot-trail";
import LayerRobotOccupy from "./robot-occupy";
import LayerRobotErrorCoverCell from "./robot-error-cover-cell";
import LayerRobotLimitError from "./robot-limit-error";

class LayerRobot implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;

  private layerRobotBox: LayerRobotBox = new LayerRobotBox();
  private layerRobotBelt: LayerRobotBelt = new LayerRobotBelt();
  private layerRobotTrail: LayerRobotTrail = new LayerRobotTrail();
  private layerRobotOccupy: LayerRobotOccupy = new LayerRobotOccupy();
  private layerRobotErrorCoverCell: LayerRobotErrorCoverCell = new LayerRobotErrorCoverCell();
  private layerRobotLimitError: LayerRobotLimitError = new LayerRobotLimitError();

  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  render(arr: Array<robotData>): void {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, options, code;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatRobot(item);
      code = options["code"];
      const robot = _this.drawSprite(options, utils);
      _this.container.addChild(robot);
      mapData.robot.setData(code, { element: robot, options });
    }
  }

  update(arr: Array<robotData>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapConfig: MRender.RenderConfigMain = mapCore.mapConfig,
      mapData = mapCore.mapData,
      mapEvent = mapCore.mapEvent,
      utils = mapCore.utils;

    const isTrail = mapConfig.getLayerVisible("robotTrail");
    const isOccupy = mapConfig.getLayerVisible("robotOccupy");
    const destCell = mapConfig.data.getDestCell();
    const deadRobotCodes = mapData.deadRobot.getAll();

    _this.layerRobotBox.repaint();
    _this.layerRobotBelt.repaint();
    _this.layerRobotTrail.repaint();
    _this.layerRobotOccupy.repaint();
    _this.layerRobotErrorCoverCell.repaint();
    _this.layerRobotLimitError.repaint();

    let destNum = 0;
    let item, code, options, robot, select;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatRobot(item);
      code = options["code"];

      robot = mapData.robot.getData(code);

      if (options["robotState"] === "REMOVED_FROM_SYSTEM") {
        mapEvent.clearSelects("robot", [code]);
        mapData.robot.delData(code);
        continue;
      }

      if (robot) {
        if (robot.options.floorId !== _this.floorId) {
          robot.element.parent && robot.element.parent.removeChild(robot);
          _this.container.addChild(robot.element);
        }

        mapData.robot.setData(options.code, Object.assign(robot, { options }));
        _this.updateSprite(robot, utils);
        select = mapEvent.updateSelect("robot", options);
      } else {
        robot = _this.drawSprite(options, utils);
        _this.container.addChild(robot);
        mapData.robot.setData(options.code, { element: robot, options });
      }

      // 机器人身上的箱子
      if (options.showBox) {
        _this.layerRobotBox.drawGeometry(options);
      }
      // 可恢复上限异常
      if (options.showLimitError) {
        _this.layerRobotLimitError.drawGeometry(options);
      }

      // 机器人皮带
      if (options.showBelt) {
        _this.layerRobotBelt.drawGeometry(options);
      }

      let isDrawTrail = false;
      if (deadRobotCodes && deadRobotCodes[code]) {
        this.layerRobotTrail.drawGeometry(options, deadRobotCodes[code]);
        isDrawTrail = true;
      }
      // 机器人路径
      if (isTrail) {
        if (!isDrawTrail) _this.layerRobotTrail.drawGeometry(options);
      } else if (select) {
        if (!isDrawTrail) _this.layerRobotTrail.drawGeometry(options);
      } else if (
        destCell &&
        (options["destCellCode"] === destCell.code || options["stationId"] === destCell.stationId)
      ) {
        ++destNum;
        if (!isDrawTrail) _this.layerRobotTrail.drawGeometry(options);
      }

      // 机器人预占区域
      if (isOccupy) {
        _this.layerRobotOccupy.drawGeometry(options);
      } else if (select) {
        _this.layerRobotOccupy.drawGeometry(options);
      }

      //机器人异常覆盖的点
      if (options['errorCoverCells'].length>0) {
        _this.layerRobotErrorCoverCell.drawGeometry({ ...options,color:0xe75552 });
      }
    }

    

    _this.layerRobotBox.render();
    _this.layerRobotBelt.render();
    _this.layerRobotTrail.render();
    _this.layerRobotOccupy.render();
    _this.layerRobotErrorCoverCell.render();
    _this.layerRobotLimitError.render();
    if (destNum > 0) {
      mapEvent.renderDestCircle(destCell, destNum);
    } else {
      mapEvent.removeDestCircle();
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = false;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.layerRobotBox.repaint();
    this.layerRobotBelt.repaint();
    this.layerRobotTrail.repaint();
    this.layerRobotOccupy.repaint();
    this.layerRobotErrorCoverCell.repaint();
    this.layerRobotLimitError.repaint();
  }

  destroy(): void {
    this.repaint();
    this.layerRobotBox.destroy();
    this.layerRobotBox = null;
    this.layerRobotBelt.destroy();
    this.layerRobotBelt = null;
    this.layerRobotTrail.destroy();
    this.layerRobotTrail = null;
    this.layerRobotOccupy.destroy();
    this.layerRobotOccupy = null;
    this.layerRobotErrorCoverCell.destroy();
    this.layerRobotErrorCoverCell = null;
    this.layerRobotLimitError.destroy();
    this.layerRobotLimitError = null;
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "device";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("robot");
    this.container = container;

    this.layerRobotBox.init(mapCore);
    this.layerRobotBelt.init(mapCore);
    this.layerRobotTrail.init(mapCore);
    this.layerRobotOccupy.init(mapCore);
    this.layerRobotErrorCoverCell.init(mapCore);
    this.layerRobotLimitError.init(mapCore);

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(this.layerRobotOccupy.getContainer());
    layerFloor.addChild(
      container,
      this.layerRobotBox.getContainer(),
      this.layerRobotBelt.getContainer(),
      this.layerRobotTrail.getContainer(),
      this.layerRobotErrorCoverCell.getContainer(),
      this.layerRobotLimitError.getContainer(),
    );
  }

  private updateSprite(robot: { element: any; options: mRobotData }, utils: any) {
    const { element, options } = robot;
    const { position, radAngle, iconName } = options;

    element.position.set(position.x, position.y); // 使图片居中
    element.rotation = radAngle; // 设置角度
    element.texture = utils.getResources(iconName);
  }

  private drawSprite(options: mRobotData, utils: any) {
    const { code, width, height, position, radAngle, iconName, anchor } = options;
    let sprite: any = new PIXI.Sprite(utils.getResources(iconName));
    sprite.mapType = "robot";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = true;
    sprite.anchor.set(...anchor);
    sprite.position.set(position.x, position.y); // 使图片居中
    sprite.rotation = radAngle; // 设置角度
    return sprite;
  }
}
export default LayerRobot;
