<template>
  <!-- 角色列表 -->
  <geek-main-structure style="padding: 0 0 60px">
    <div style="padding: 0 12px; height: 100%; overflow: auto">
      <div class="permission-header">
        <geek-customize-form ref="customForm" :form-config="formConfig" />

        <h3 class="permission-title">
          <span class="text">{{ $t("lang.rms.fed.tabAssignPagePermissions") }}</span>
          <!-- Fixme 后端说没权限传null。。。,有问题找后端 -->
          <gp-switch
            v-model="permissionStatus"
            :active-value="1"
            :inactive-value="null"
            :active-text="$t('lang.rms.fed.buttonEnablingAuthority')"
          />
        </h3>
      </div>

      <gp-tree
        ref="tree"
        :data="treeData"
        show-checkbox
        :default-expanded-keys="[1]"
        node-key="permissionId"
        :props="{
          children: 'children',
          label: item => {
            let text;
            if (item.code.indexOf('auth') !== -1) {
              text = this.$t(item.code);
            } else {
              text = item.code;
            }
            return text;
          },
        }"
      />
    </div>

    <div class="footer-button">
      <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
      <gp-button @click="$emit('setCurrentOperation', 'list')">{{ $t("lang.rms.fed.cancel") }}</gp-button>
    </div>
  </geek-main-structure>
</template>

<script>
export default {
  props: ["operation", "rowData"],
  data() {
    return {
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.rms.fed.inputRoleName",
            default: "",
            tag: "input",
            required: true,
            disabled: [1, 2].includes(this.rowData?.roleId),
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          descr: {
            label: "lang.rms.fed.inputRoleRoleDescription",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          permission: {
            label: "lang.rms.fed.inputPermissionType",
            tag: "input",
            disabled: true,
            placeholder: "lang.rms.fed.pagePermission",
          },
          status: {
            label: "lang.rms.fed.authSystem",
            tag: "input",
            default: "RMS",
            placeholder: "RMS",
            disabled: true,
          },
        },
      },
      permissionStatus: 1,
      treeData: [],
    };
  },
  activated() {
    const rowData = this.rowData || {};
    this.treeData = [];
    this.$refs.tree.setCheckedKeys([]);
    switch (this.operation) {
      case "edit":
        this.formConfig.configs.name.disabled = [1, 2].includes(rowData.roleId);
        this.$refs.customForm.setData({ name: rowData.name || "", descr: rowData.descr || "" });
        this.permissionStatus = Number(rowData.status);
        this.getEditTreeData();
        break;
      case "add":
        this.formConfig.configs.name.disabled = false;
        this.$refs.customForm.reset();
        this.getAddTreeData();
        break;
    }
  },
  methods: {
    save() {
      const $tree = this.$refs.tree;
      const formData = this.$refs.customForm.getData();
      const permissionId = $tree.getCheckedKeys().concat($tree.getHalfCheckedKeys());
      let data = {
        status: this.permissionStatus,
        name: formData.name,
        descr: formData.descr,
        type: 1,
        subsystem: [1],
        permList: [
          {
            subsysId: 1,
            permissionId,
          },
        ],
      };
      if (this.operation === "edit") data.roleId = Number(this.rowData.roleId);

      if (!this.validateData(data)) return;

      $req.post("/athena/api/coreresource/auth/permission/editWebPerm/v1", data).then(res => {
        if (res.code !== 0) return;
        this.$emit("setCurrentOperation", "list");
        this.$success($utils.Tools.transMsgLang(res.msg));
      });
    },
    validateData(data) {
      if (data.name === "") {
        this.$warning(this.$t("lang.rms.fed.pleaseEnterRoleName"));
        return false;
      } else if (data.descr === "") {
        this.$warning(this.$t("lang.rms.fed.pleaseEnterRoleDesc"));
        return false;
      } else if (data.permList[0].permissionId.length === 0) {
        this.$warning(this.$t("lang.rms.fed.pleaseAssignPagePermissions"));
        return false;
      } else {
        return true;
      }
    },
    getEditTreeData() {
      $req
        .get("/athena/api/coreresource/auth/permission/listWebPerm/v1", {
          roleId: this.rowData.roleId,
          subsysId: 1,
        })
        .then(res => {
          if (res.code !== 0) return;
          const sybSystemPermList = res?.data?.sybSystemPermList || [];
          this.treeData = sybSystemPermList[1] || [];
          this.$refs.tree.setCheckedKeys(sybSystemPermList?.checkedPermissionIds || []);
        });
    },
    getAddTreeData() {
      $req
        .get("/athena/api/coreresource/auth/permission/listPermissionsBySubsysId", {
          subsysId: 1,
        })
        .then(res => {
          if (res.code !== 0) return;
          const data = res?.data || [];

          this.treeData = data.filter(item => item.code === "pc");
          this.$refs.tree.setCheckedKeys([]);
        });
    },
  },
};
</script>

<style lang="less" scope>
.permission-header {
  position: sticky;
  padding-top: 12px;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 22;
  background: #fff;
}
.permission-title {
  .g-flex();
  line-height: 2.2;
  border-bottom: 1px solid #eee;

  span.text {
    font-size: 14px;
    color: #409eff;
    border-bottom: 2px solid #409eff;
  }
}

.footer-button {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 12px;
  text-align: right;
}
</style>
