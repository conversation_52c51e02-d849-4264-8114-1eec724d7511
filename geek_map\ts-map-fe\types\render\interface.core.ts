/* ! <AUTHOR> at 2023/04/20 */
namespace MRender {
  export interface MainCore {
    /** 地图 工具类 */
    utils: any;
    /** 地图 view 对象，App、viewport、render 等 */
    mapView: any;
    /** 地图 对组件外的 config 设置类 */
    mapConfig: any;
    /** 地图 单独元素数据 */
    mapData: any;
    /** 地图 mesh元素数据 */
    meshData: any;
    /** 地图 事件相关 */
    mapEvent: any;
    /** 地图各楼层Layer */
    mapFloors: { [propName: floorId]: any };
    init(mapReady: MRender.mapReady): void;
    renderAreaStop(areaData: Array<any>, isInit: boolean): void;
    renderAreaRobot(areaData: Array<any>, isInit: boolean): void;
    renderAreaSpeedLimit(areaData: Array<any>, isInit: boolean): void;

    /**
     * 初始化Map Floor数据
     * @param floorsData
     */
    renderFloors(floorsData: floorsData): void;

    /**
     * 初始化display数据
     * @param displays
     */
    renderDisplays(displays: displays): void;

    /**
     * 更新display数据
     * 更新的2d display数据中会包含cells，不包含racks，因为racks不需要更新位置渲染
     * @param displays
     */
    updateDisplays(displays: displays): void;

    /**
     * 如果显示（隐藏）就隐藏（显示）地图层。
     * @param layerNames
     * @param isShow
     */
    toggleLayer(layerName: MRender.toggleLayerName, isShow: boolean, data?: shelfHeatApiData | Array<code>): void;

    /**
     * 地图 layerNames 可点击
     * @param layerNames
     */
    triggerLayers(layerNames: Array<MRender.layerName>): void;
    renderAreaInDiffColor(areaData?: Array<any>): void;
    /** 地图重绘 */
    repaint(): void;
    /** 地图销毁 */
    destroy(): void;
  }
}
