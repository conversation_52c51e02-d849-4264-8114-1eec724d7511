<template>
  <gp-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="showDialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="640"
    border
    center
  >
    <gp-form ref="editForm" :rules="rules" :model="formData" label-width="160px">
      <gp-form-item :label="$t('lang.rms.fault.exception.code') + ':'" prop="systemCode">{{
        formData.systemCode
      }}</gp-form-item>
      <gp-form-item :label="$t('lang.rms.web.monitor.exception.info') + ':'" prop="exception">
        <span>{{ $t(formData.exception) }}</span>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.chargerSolution') + ':'" prop="solution">
        <span>{{ $t(formData.solution) }}</span>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.backlog.fault.maintenanceTask') + ':'" prop="maintenance">
        <span>{{ $t(formData.maintenance) }}</span>
      </gp-form-item>
      <!--延迟发送邮件-->
      <gp-form-item :label="$t('lang.rms.email.config.delaySendInterval') + ':'" prop="delaySendEmailInterval">
        <gp-input
          type="number"
          v-model="formData.delaySendEmailInterval"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
          <template slot="append">s</template>
        </gp-input>
      </gp-form-item>
      <gp-form-item :label="`${$t('lang.rms.fed.showEnableOrDisable')}:`" prop="status">
        <gp-switch :active-value="1" :inactive-value="0" v-model="formData.status"> </gp-switch>
        <span>{{
          formData.status === 1 ? $t("lang.rms.fed.chargerEnable") : $t("lang.venus.common.dict.disable")
        }}</span>
      </gp-form-item>
      <gp-form-item :label="`${$t('lang.rms.fed.callbackEnableOrDisable')}:`" prop="callbackDisable">
        <gp-switch :active-value="true" :inactive-value="false" v-model="formData.callbackDisable"> </gp-switch>
        <span>{{
          formData.callbackDisable ? $t("lang.rms.fed.chargerEnable") : $t("lang.venus.common.dict.disable")
        }}</span>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.callbackDuration') + ':'" prop="callbackDuration">
        <gp-input
          type="number"
          v-model="formData.callbackDuration"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
        </gp-input>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.todolistDuration') + ':'" prop="todolistDuration">
        <gp-input
          type="number"
          v-model="formData.todolistDuration"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
        </gp-input>
      </gp-form-item>
      <gp-form-item :label="`${$t('lang.rms.backlog.fault.isBacklog')}:`" prop="messageGroup">
        <gp-switch :active-value="2" :inactive-value="1" v-model="formData.messageGroup"> </gp-switch>
        <span>{{ formData.messageGroup === 2 ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </gp-form-item>

      <gp-form-item :label="`${$t('lang.rms.fed.voicePromptEnable')}:`" prop="voicePromptEnable">
        <gp-switch :active-value="true" :inactive-value="false" v-model="formData.voicePromptEnable"> </gp-switch>
        <span>{{ formData.voicePromptEnable ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </gp-form-item>

      <gp-form-item :label="$t('lang.rms.backlog.fault.isEmailNotice') + ':'" prop="isSendEmail">
        <gp-switch v-model="formData.isSendEmail"> </gp-switch>
        <span>{{ formData.isSendEmail ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.backlog.fault.isMaintenance') + ':'" prop="isMaintenance">
        <gp-switch v-model="formData.isMaintenance"> </gp-switch>
        <span>{{ formData.isMaintenance ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </gp-form-item>
    </gp-form>
    <div slot="footer">
      <gp-button @click="closeDialog">{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
    </div>
  </gp-dialog>
</template>
<script>
export default {
  name: "editMaxQueueNum",
  data() {
    //通知频率校验
    const delaySendEmailIntervalValidator = (rule, value, callback) => {
      if (value >= 0) {
        callback();
      } else {
        callback(new Error("请输入大于等于0的数字"));
      }
    };
    return {
      showDialog: false,
      formData: {},
      rules: {
        delaySendEmailInterval: [
          { type: "number", required: true, trigger: "blur", validator: delaySendEmailIntervalValidator },
        ],
      },
    };
  },
  methods: {
    open(rowData) {
      this.formData = Object.assign({}, rowData);
      this.showDialog = true;
    },

    closeDialog() {
      this.formData = {};
      this.showDialog = false;
    },
    // 保存
    save() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const editData = this.formData;
          const newData = {
            systemCode: editData.systemCode,
            isSendEmail: editData.isSendEmail,
            isMaintenance: editData.isMaintenance,
            messageGroup: editData.messageGroup,
            delaySendEmailInterval: Number(editData.delaySendEmailInterval),
            callbackDuration: Number(editData.callbackDuration),
            todolistDuration: Number(editData.todolistDuration),
            status: editData.status,
            callbackDisable: editData.callbackDisable,
            voicePromptEnable: editData.voicePromptEnable,
          };
          $req.post("/athena/fault/message/updateFaultMessage", newData).then(res => {
            if (res.code === 0) {
              this.$success(this.$t(res.msg));
              this.closeDialog();
              this.$emit("updateTableList");
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style scoped></style>
