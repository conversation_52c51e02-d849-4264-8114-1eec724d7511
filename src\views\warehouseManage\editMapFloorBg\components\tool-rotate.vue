<template>
  <div class="edit-map-bg-status-choose">
    <strong>{{ $t("lang.rms.fed.rotateChoice") }}:</strong>
    <gp-select v-model="type" size="mini" class="selector" @change="handleChange('type')">
      <gp-option :label="$t('lang.rms.fed.compass1')" value="rotateCompass" />
      <gp-option :label="$t('lang.rms.fed.free')" value="rotateFree" />
    </gp-select>
    <gp-input-number
      v-model="angle"
      size="mini"
      :min="0"
      :max="360"
      :step="0.1"
      :precision="1"
      step-strictly
      @change="handleChange('angle')"
    />
  </div>
</template>

<script>
export default {
  name: "ToolRotate",
  props: {
    isShow: {
      type: Boolean,
      require: true,
    },
    defaultAngel: {
      type: Number,
      require: true,
    },
  },
  data() {
    return {
      type: "rotateCompass",
      angle: this.defaultAngel,
    };
  },
  watch: {
    isShow(flag) {
      if (flag) {
        this.$emit("change", this.type, this.angle);
      }
    },
    defaultAngel(val) {
      this.angle = this.defaultAngel;
    },
  },
  methods: {
    handleChange(key) {
      if (key === "type") {
        this.$emit("change", this.type, null);
      } else if (key === "angle") {
        this.$emit("change", null, this.angle);
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
