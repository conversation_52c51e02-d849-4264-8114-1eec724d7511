/* ! <AUTHOR> at 2022/09/02 */
import * as <PERSON>IX<PERSON> from "pixi.js";
import TrafficAreaPop from "./map-trafficArea-pop";
class LayerAreaStop implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "areaStop";
    container.interactiveChildren = false;
    container.visible = false;
    container.alpha = 1;
    container.zIndex = utils.getLayerZIndex("areaStopCover");
    this.container = container;

    this.fillStyle.visible = true;
    this.fillStyle.color = 0x8bd4db;
    this.lineStyle.visible = true;
    this.lineStyle.width = 0.3;
    this.lineStyle.color = 0x838383;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<any>): void {
    let item;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = arr.length;

    let robotAreaText = new PIXI.Container();
    robotAreaText.name = "robotAreaText";
    robotAreaText.interactiveChildren = true;
    robotAreaText.visible = true;
    robotAreaText.alpha = 0.8;
    robotAreaText.zIndex = this.mapCore.utils.getLayerZIndex("shelf");
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      this.drawArea(item, graphicsGeometry);
      this.drawText(item, robotAreaText)
    }

    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = "robotArea";
    graphics.mapType = "robotArea";
    graphics.interactive = graphics.buttonMode = false;

    let robotAreaCon = new PIXI.Container();
    robotAreaCon.name = "robotAreaCon";
    robotAreaCon.interactiveChildren = true;
    robotAreaCon.visible = true;
    robotAreaCon.alpha = 0.5;
    robotAreaCon.zIndex = this.mapCore.utils.getLayerZIndex("area");
    robotAreaCon.addChild(graphics);
    
    this.meshList.push(robotAreaCon);
    this.container.addChild(robotAreaCon);
    this.meshList.push(robotAreaText);
    this.container.addChild(robotAreaText)
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
  }

  private drawText(data: any, robotAreaText:any) {
    // 区域生成结构数据
    const trafficAreaPopInstance = new TrafficAreaPop()

    // 生成pop
    const trafficAreaPop = trafficAreaPopInstance.render(data)
    const h = trafficAreaPop.height
    const w = trafficAreaPop.width
    let point: any = []
    if (data?.robotPoints?.length) {
      let pointObj = {
        xMin: 0,
        xMax: 0,
        yMax: 0
      }
      let xArr: any = [];
      data.robotPoints.forEach((item: any) => {
        xArr.push(item.x);
        if (item.y > pointObj.yMax) {
          pointObj.yMax = item.y;
        }
      })
      pointObj.xMin = Math.min.apply(null, xArr)
      pointObj.xMax = Math.max.apply(null, xArr)
      let x = (pointObj.xMax - pointObj.xMin) / 2 + pointObj.xMin;
      let y = pointObj.yMax
      point = [x, -y]
    }
    if (point.length) {
      robotAreaText.addChild(trafficAreaPop);
      trafficAreaPop.scale.set(0.04, 0.04);
      trafficAreaPop.position.set(point[0] - (w * 0.04 / 2), point[1] - h * 0.04)
    }
    //TODO 这里写text
    // const polygons = data?.robotPoints || [];
    // const style = new PIXI.TextStyle({
    //   fontFamily: "Arial",
    //   fontSize: 36,
    //   fontStyle: "italic",
    //   fill: "#ffffff",
    // });

    // const richText = new PIXI.Text("123", style);
    // richText.width = 20;
    // richText.height = 10;
    // richText.position.set(polygons[0].x, -polygons[0].y);

    // this.meshList.push(richText);
    // this.container.addChild(richText);
  }

  private drawArea(data: any, graphicsGeometry:any) {
    const _this = this;
    const lineStyle = _this.lineStyle,
      fillStyle = _this.fillStyle;

    const polygons = data?.robotPoints || [];
    if(polygons.length<=2) return;
  
    let result = [];
    let point;
    for (let i = 0, len = polygons.length; i < len; i++) {
      point = polygons[i];
      result.push(point.x);
      result.push(-point.y);
    }

    const polygon = new PIXI.Polygon(result);
    graphicsGeometry.drawShape(polygon, fillStyle, lineStyle);
  }
}
export default LayerAreaStop;
