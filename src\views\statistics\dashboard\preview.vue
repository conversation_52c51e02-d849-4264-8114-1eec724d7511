<template>
  <div class="dashboard" ref="dashboardRef" v-loading="isDataLoad">
    <div class="headerFilter">
      <gp-date-picker class="datetimerange" v-model="globalFilterDate" type="datetimerange"></gp-date-picker>
      <gp-button class="queryBtn" type="primary" @click="queryGlobalFilter">{{ $t('lang.rms.fed.query') }}</gp-button>
    </div>
    <template v-if="!isDataLoad">
      <DashboardChartView
        ref="dashboardChartViewRef"
        v-for="(item, index) in options"
        :key="componentId + index"
        :disabled="item.isActive || !disabled"
        :option="item"
        :gridWidth="gridWidth"
      />
    </template>
  </div>
</template>

<script>
import DashboardChartView from './dashboard-chart.vue'

export default {
  name: "chartViewMain",
  props: {
    options: {
      type: Array
    },
    componentId: {
      type: String,
      default() { 
        return 'chart';
      },
    }
  },
  components: { DashboardChartView },
  data() {
    return {
      width: 0,
      gridNumber: 48,
      isDataLoad: true,
      disabled: true,
      // 全局筛选日期, 默认今天0点~明天0点
      globalFilterDate: [new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(24, 0, 0, 0))],
    };
  },
  mounted() {
    this.resize();
    this.isDataLoad = false;
    window.addEventListener('resize', this.resize);
  },
  computed: {
    gridWidth() {
      return this.width / this.gridNumber;
    },
  },
  destroyed() {
    window.removeEventListener('resize', this.resize);
  },
  methods: {
    resize() {
      this.width = this.$refs.dashboardRef.offsetWidth;
    },

    queryGlobalFilter() {
      const option = { date: this.globalFilterDate };
      this.$refs.dashboardChartViewRef.forEach(ref => {
        ref.queryGlobalFilter && ref.queryGlobalFilter(option);
      })
    }
  },
};
</script>

<style lang="less" scoped>
.headerFilter {
  padding: 5px;
  font-size: 0;
  .datetimerange {
    width: 420px;
  }

  .queryBtn {
    margin-left: 10px;
    vertical-align: baseline;
  }

}

.dashboard {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-x: hidden;
  position: relative;
  padding-bottom: 30px;
}
</style>
