<template>
  <!-- x y -->
  <template v-for="lattice in curItem.latticeInfos">
    <el-form-item :label="$t('lang.rms.map.deviceEdit.anyLayerAnyColumn', [lattice.line, lattice.layerColumnNum])">
      <el-row>
        <el-col :span="6">
          <el-form-item label="x">
            <div class="my-imput--number">
              <el-input-number v-model="lattice.locationX" :controls="false" @onChange="locationChange" />
              <div class="define-append">mm</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6" :offset="1">
          <el-form-item label="y">
            <div class="my-imput--number">
              <el-input-number v-model="lattice.locationY" :controls="false" @onChange="locationChange" />
              <div class="define-append">mm</div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="3" :offset="1">
          <el-switch
            v-model="lattice.hasBcr"
            size="small"
            :active-text="$t('lang.rms.fed.ppp.stationModel.bcr')"
            :active-value="1"
            :inactive-value="0"
          />
        </el-col>
        <el-col :span="4" :offset="1">
          <el-switch
            v-model="lattice.hasWeight"
            size="small"
            :active-text="$t('lang.rms.fed.ppp.stationModel.weight')"
            :active-value="1"
            :inactive-value="0"
          />
        </el-col>
      </el-row>
    </el-form-item>
  </template>
</template>

<script setup lang="ts">
// import { computed } from "vue";
const props = defineProps<{
  curItem: any;
}>();

const locationChange = () => {
  console.log("curItem::变了么", props.curItem);
};
</script>
