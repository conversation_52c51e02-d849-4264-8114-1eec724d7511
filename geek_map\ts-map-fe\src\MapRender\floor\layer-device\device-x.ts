/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";

class LayerDeviceX implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  render(arr: Array<xDeviceData>): void {
    if (!arr || !arr.length) return;

    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, options, device;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatXDevice(item);
      device = _this.drawSprite(options, utils);

      container.addChild(device);
      mapData.device.setData(options.code, { element: device, options });
    }
  }

  update(arr: Array<xDeviceData>) {
    if (!arr || !arr.length) return;

    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, code, options, device;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatXDevice(item);
      code = options["code"];
      device = mapData.device.getData(code);
      if (device) {
        device.element.tint = options["color"];
        mapData.device.setData(code, Object.assign(device, { options }));
      } else {
        device = _this.drawSprite(options, utils);
        container.addChild(device);
        mapData.device.setData(code, { element: device, options });
      }
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }
  repaint(): void {
    console.log("xDevice repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
  }

  init(): void {
    const utils = this.mapCore.utils;

    let container = new PIXI.Container();
    container.name = "device";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("device");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  private drawSprite(options: mDeviceData, utils: any) {
    const { code, width, height, position, iconName, color } = options;

    let sprite: any = new PIXI.Sprite(utils.getResources(iconName));
    sprite.mapType = "device";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = true;
    sprite.anchor.set(0.5, 0.5);
    sprite.tint = color;
    sprite.position.set(position.x, position.y); // 使图片居中
    return sprite;
  }
}
export default LayerDeviceX;
