<template>
  <gp-dropdown trigger="click" class="geek-lang-selection" @command="setLocalLang">
    <gp-button v-if="icon === 'text'" text class="gp-header__menu-item">
      {{ showTitle }} <gp-icon><gp-icon-arrow-down /></gp-icon>
    </gp-button>
    <span class="gp-dropdown-link" v-else>
      <img v-if="icon === 'blue'" src="@imgs/common/icon-lang-blue.png" width="25" />
      <img v-else src="@imgs/common/icon-lang.png" width="20" />
    </span>
    <gp-dropdown-menu slot="dropdown" class="lang-menu">
      <gp-dropdown-item v-for="item in langList" :key="item.name" :command="item" :disabled="localLang === item.name">
        {{ item.text }}
      </gp-dropdown-item>
    </gp-dropdown-menu>
  </gp-dropdown>
</template>

<script>
import langType from "@lang/_lang-type-data";

export default {
  name: "GeekLangSelection",
  props: ["icon"],
  data() {
    return {
      localLang: $utils.Data.getLocalLang(),
      langList: [],
    };
  },
  created() {
    $req
      .get("/athena/api/coreresource/i18n/getLanguages/v1")
      .then(res => {
        if (res.code !== 0 || !res.data || !$utils.Type.isArray(res.data)) return;
        const langData = res.data;

        let defaultLang = "";
        let langList = [];
        langData.forEach(item => {
          const code = item.code;
          if (item.isDefault === 1) defaultLang = code;
          if (langType[code]) {
            langType[code].name = code;
            langType[code].text = item.name;
            langType[code].apiUrl = code;
          } else {
            langType[code] = {
              name: code,
              text: item.name,
              apiUrl: code,
              getElementLang: () => {
                return null;
              },
              getGeekElementLang() {
                return null;
              },
            };
          }
          langList.push(langType[code]);
        });
        this.langList = langList;
        this.langList.splice(0, 0);
        if (!$utils.Data.getLocalLang()) {
          if (defaultLang) $utils.Data.setLocalLang(defaultLang);
          else $utils.Data.setLocalLang();
        }

        const locale = $utils.Data.getLocalLang();
        $utils.Data.setI18nMessage(this.$i18n, langType[locale].apiUrl);
      })
      .catch(e => {
        this.langList = Object.values(langType);
        console.error(e);
      });
  },
  computed: {
    showTitle() {
      const value = this.langList.find(v => v.name === this.localLang);
      return value ? value.text : this.localLang;
    },
  },
  methods: {
    setLocalLang(lang) {
      const { Data } = $utils;
      const { name, apiUrl } = lang;
      this.localLang = name;
      Data.setLocalLang(name);
      Data.setI18nMessage(this.$i18n, apiUrl);

      this.postMessage();
    },

    postMessage() {
      $utils.postMessage("monitor2D", { type: "langChange", localLang: this.localLang });
      $utils.postMessage("singleEdit2D", { type: "langChange", localLang: this.localLang });
    },
  },
};
</script>

<style lang="less" scoped>
.geek-lang-selection {
  margin-right: 0px !important;
  .gp-dropdown-link {
    cursor: pointer;
  }
}
.lang-menu {
  max-height: 291px;
  overflow-y: scroll;
}
.gp-header__menu-item {
  margin: 0 !important;
}
</style>
