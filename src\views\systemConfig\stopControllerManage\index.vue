<template>
  <geek-main-structure class="flex flex-col">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset" />
    <div class="flex-1">
      <geek-customize-table :table-config="tableConfig" :data="tableData" @row-add="rowAdd">
        <template #operations="{ row }" v-if="!isRoleGuest()">
          <gp-link :underline="false" type="primary" @click="rowEdit(row)">
            {{ $t("lang.rms.fed.buttonEdit") }}
          </gp-link>
          <gp-link :underline="false" type="danger" @click="rowDel(row)">
            {{ $t("lang.rms.fed.delete") }}
          </gp-link>
        </template>
      </geek-customize-table>
    </div>
    <edit-dialog ref="editDialog" @updateList="getTableList" />
  </geek-main-structure>
</template>
<script>
import editDialog from "./components/editDialog";

export default {
  name: "StopControllerManage",
  components: { editDialog },
  data() {
    return {
      form: {
        deviceId: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          deviceId: {
            label: "lang.rms.fed.controllerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBusinessNo",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.addController",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.fed.controllerId", prop: "deviceId" },
          { label: "lang.rms.fed.IPAdress", prop: "ip" },
          {
            label: "lang.rms.fed.boundLogicIdArea",
            prop: "referBy",
            formatter: (row, column) => {
              if (!row[column] === "null") return "";
              return row[column];
            },
          },
          { label: "lang.rms.fed.channelAndWorkstation", prop: "channelsStr" },
          {
            label: "lang.rms.fed.listOperation",
            width: "140",
            slotName: "operations",
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open("add", {});
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      if (!row) return;
      this.$geekConfirm(this.$t("lang.rms.fed.pleaseConfirmDeleteController"))
        .then(() => {
          $req.get("/athena/baseDevice/delete", { id: row.id }).then(res => {
            if (res.code !== 0) return;
            this.getTableList();
            this.$success(this.$t(res.msg));
          });
        })
        .catch(e => console.log(e));
    },

    onQuery(val) {
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.form = Object.assign({}, val);
      this.getTableList();
    },

    getTableList() {
      $req.get("/athena/baseDevice/findAll", this.form).then(res => {
        if (res?.code != 0) return;
        this.tableData = res?.data || [];
      });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped></style>
