<template>
  <geek-customize-table
    :table-config="tableConfig"
    :data="rowDataList"
  >
<!--    <template #value="{row}">-->
<!--      <el-input v-model="row.value" v-number-only :maxlength="11" clearable></el-input>-->
<!--    </template>-->

    <template #actionType="{ row }">
      <gp-select v-model="row.actionType" disabled>
        <gp-option
          v-for="(item,index) in actionTypeOps"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #plcAddress="{ row }">
      <div class="input-con">
        <span class="required">*</span>
        <gp-input
          v-model="row.plcAddress"
          clearable
          @input="plcAddressChange(row)"
        ></gp-input>
      </div>
    </template>
    <template #registerType="{ row }">
      <gp-select v-model="row.registerType">
        <gp-option
          v-for="(item,index) in registerTypeList"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #checkType="{ row }">
      <gp-select v-model="row.checkType">
        <gp-option
          v-for="(item,index) in checkTypeOps"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #plcAddressValue="{row}">
      <div class="input-con">
        <span class="required" v-if="plcAddressValueRequired(row)">*</span>
        <gp-input
          :disabled="plcAddressValueDisable(row)"
          v-model="row.plcAddressValue"
          clearable
          maxlength="11"
        ></gp-input>
      </div>
<!--      <gp-select v-else v-model="row.inputValue"></gp-select>-->
    </template>
    <template #operation="{row}">
      <gp-button type="text" @click="deleteItem(row)">{{$t('lang.venus.web.common.delete')}}</gp-button>
    </template>

  </geek-customize-table>
</template>

<script>
export default {
  name: "ExpendStatus",
  props: {
    rowData: {
      type: Object,
      require: true,
    },
    id: {
      type: Number,
      require: true,
    }
  },
  computed:{
    rowDataList() {
      let list = []
      if(this.rowData) list = [this.rowData];
      return list
    },
    plcAddressValueRequired() {
      return (row) => {
        let flag = true
        const {checkType} = row
        if(checkType === 0) flag = false
        return flag
      }
    },
    plcAddressValueDisable() {
      return (row) => {
        if(row.checkType === 0)  row.plcAddressValue = '';
        return row.checkType === 0
      }
    }
  },
  data() {
    return {
      actionTypeOps:[
        {label:'lang.rms.web.device.read',value:0},
        {label:'lang.rms.web.device.write',value:10},
      ],
      registerTypeList:[
        {label:'lang.venus.common.dict.readCoil',value:0},
        {label:'lang.venus.common.dict.readAndWriteCoil',value:1},
        {label:'lang.venus.common.dict.readRegister',value:3},
        {label:'lang.venus.common.dict.readAndWriteRegister',value:4},
      ],
      checkTypeOps:[
        {label:'lang.rms.web.device.read',value:0},
        {label:'lang.rms.web.device.judgmentEqual',value:1},
        {label:'lang.rms.web.device.judgmentNotEqual',value:2},
        {label:'lang.rms.web.device.judgmentGreaterThan',value:3},
        {label:'lang.rms.web.device.judgmentLessThan',value:4},
      ],
      tableData:[],
      tableConfig: {
        // attrs: { "show-header": false },
        columns: [
          // {
          //   label: "点位值",
          //   prop: "value",
          //   width: "140",
          //   slotName: "value"
          // },
          {
            label: "lang.venus.web.common.operationType",
            prop: "actionType",
            width: "140",
            slotName: "actionType"
          },
          {
            label: "lang.venus.web.common.plcAddress",
            prop: "plcAddress",
            width: "314",
            slotName: "plcAddress"
          },
          {
            label: "lang.venus.web.common.registerType",
            prop: "registerType",
            width: "140",
            slotName: "registerType"
          },
          {
            label: "lang.rms.web.device.readType",
            prop: "checkType",
            width: "140",
            slotName: "checkType"
          },
          {
            label: "lang.rms.web.device.targetValue",
            prop: "plcAddressValue",
            width: "140",
            slotName: "plcAddressValue"
          },
          {
            label: "lang.rms.fed.listOperation",
            prop: "operation",
            slotName: "operation",
            width: "140",
            className: "operation-btn",
            "render-header":this.addBtn,
          },
          { label: "", prop: "empty", },
        ],
      },
    }
  },
  methods:{
    plcAddressChange(row) {
      const reg = row.checkType === 0 ? /[^0-9,]/g : /[^0-9]/g
      row.plcAddress = row.plcAddress.replace(reg, '');
    },
    addBtn(h) {
      return h('gp-button',{
        style: {
          display: !this.rowDataList.length ? 'block' : 'none',
        },
        props: {
          type: 'text'
        },
        domProps: {
          innerHTML: '新增'
        },
        on: {
          click: this.addItem
        },
      })
    },
    addItem() {
      const { deviceCode } = this.$route.query;
      const item = {
        deviceCode,
        actionType:0,
        plcAddress:'',
        registerType:'',
        checkType:'',
        plcAddressValue:''
      }
      this.$emit('add',{id:this.id,data:item})
    },
    deleteItem(row) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      ).then(() => {
        const delId = row.plcId
        const delIndex = this.rowDataList.findIndex(item => item.plcId === delId)
        console.log(row,delIndex)
        this.$emit('delete',{id:this.id})
      }).catch((err) => {
        console.log(err)
      })
    },
  }
};
</script>

<style scoped lang="scss">
.input-con{
  display: flex;
  .required{
    color: red;
    line-height: 32px;
    margin-right: 6px;
  }
}
</style>
