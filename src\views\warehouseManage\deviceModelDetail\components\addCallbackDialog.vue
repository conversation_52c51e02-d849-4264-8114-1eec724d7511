<template>
  <div class="form-con">
    <gp-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-position="top"
    >
      <gp-form-item
        v-for="(item,index) in formData"
        :key="index"
        :label="item.label" :prop="item.prop"
      >
        <gp-input
          v-bind="item.attr || {}"
          v-if="item.type === 'input'"
          v-model="ruleForm[item.prop]"
        ></gp-input>
        <gp-select
          style="width: 100%"
          v-bind="item.attr || {}"
          v-if="item.type === 'select'"
          v-model="ruleForm[item.prop]"
        >
        </gp-select>
      </gp-form-item>
      <gp-form-item class="btn-con">
        <gp-button type="primary" @click="submitForm">保存</gp-button>
        <gp-button @click="resetForm">重置</gp-button>
      </gp-form-item>
    </gp-form>
  </div>
</template>

<script>
export default {
  name: "addApplicationDialog",
  data() {
    return {
      formData:[
        {type:'input',prop:'code',label:'应用程序接口编码'},
        {type:'input',prop:'name',label:'应用程序接口名称'},
        {type:'select',prop:'outerParams',label:'出参字段'},
        {type:'input',prop:'remark',attr:{rows:3,type:"textarea"},label:'备注'},
      ],
      ruleForm: {
        code: '',
        name: '',
        outerParams: '',
        remark: ''
      },
      rules: {
        code: { required: true, message: this.$t('lang.wms.fed.pleaseAdd'), trigger: 'change' },
        name: { required: true, message: this.$t('lang.wms.fed.pleaseAdd'), trigger: 'change' },
      },
    }
  },
  mounted() {

  },
  methods:{
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if(valid){
          this.save()
        }
      })
    },
    resetForm() {
      this.ruleForm = {
        code: '',
        name: ''
      }
      this.$refs['ruleForm'].resetFields();
    },
    save() {

    }
  }
};
</script>

<style scoped lang="less">
.form-con{
  .btn-con{
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}
</style>
