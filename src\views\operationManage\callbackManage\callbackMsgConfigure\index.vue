<template>
  <section class="callback-manage-panel-wrap">
    <geek-customize-form ref="customRef" :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <div class="callback-manage-panel-wrap__table" style="padding-top: 16px">
      <geek-customize-table :table-config="tableConfig" :data="tableData" :page="tablePage" @page-change="pageChange">
        <template #operation="{ row }">
          <gp-link type="primary" size="small" @click="rowDetail(row)">
            {{ $t("lang.rms.fed.textDetails") }}
          </gp-link>
          <gp-link v-if="row.resendMsg" type="primary" size="small" @click="rowReCall(row)">
            {{ $t("lang.common.retry") }}
          </gp-link>
        </template>
      </geek-customize-table>
    </div>
    <gp-dialog
      :title="$t('lang.rms.fed.textDetails')"
      :visible.sync="msgDialog"
      :before-close="closeDialog"
      center
      :append-to-body="true"
      width="50%"
    >
      <gp-form label-position="top" label-width="80px" :model="rowData" class="padding_20">
        <gp-form-item :label="$t('lang.rms.fed.callbackSuccessChannelIds')">
          <gp-input :value="rowData.successChannelIds" :rows="2" readonly="readonly" />
        </gp-form-item>
        <gp-form-item :label="$t('lang.rms.fed.errorDesc')">
          <gp-input :value="rowData.errorDesc" type="textarea" :rows="8" readonly="readonly" />
        </gp-form-item>
        <gp-form-item :label="$t('lang.rms.fed.msgContent')">
          <gp-input :value="rowData.content" type="textarea" :rows="8" readonly="readonly" />
        </gp-form-item>
      </gp-form>
    </gp-dialog>
  </section>
</template>
<script>
import { customRef } from "vue";

export default {
  name: "CallbackMsg",
  data() {
    return {
      // 搜索内容
      form: {
        taskId: "",
        channelId: "",
        channelType: "",
        createTime: "",
        detail: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          taskId: {
            label: "lang.rms.fed.taskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelId: {
            label: "lang.rms.fed.channelId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelType: {
            label: "lang.rms.fed.channelType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          createTime: {
            label: "lang.rms.fed.createTime",
            default: +new Date(),
            tag: "date-picker",
            placeholder: "",
          },
          detail: {
            label: "lang.rms.fed.textDetails",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: { index: true },
        columns: [
          {
            label: "lang.rms.fed.taskId",
            align: "center",
            prop: "taskId",
          },
          {
            label: "lang.rms.fed.channelId",
            align: "center",
            prop: "channelId",
          },
          {
            label: "lang.rms.fed.channelType",
            prop: "channelType",
          },
          {
            label: "lang.rms.fed.clientCode",
            prop: "clientCode",
          },
          {
            label: "lang.rms.fed.warehouseCode",
            prop: "warehouseCode",
          },
          {
            label: "lang.rms.fed.msgType",
            prop: "msgType",
          },
          {
            label: "lang.rms.fed.callbackMsgStatus",
            prop: "status",
            formatter: (row, column) => {
              const statusList = {
                0: "lang.rms.fed.callbackMsgStatusSending",
                1: "lang.rms.fed.callbackMsgStatusSuccess",
                2: "lang.rms.fed.callbackMsgStatusFail",
                3: "lang.rms.fed.callbackMsgStatusIgnore",
              };
              return this.$t(statusList[row[column]]);
            },
          },
          {
            label: "lang.rms.fed.retryTimes",
            prop: "retryTimes",
          },
          {
            label: "lang.rms.fed.createTime",
            prop: "createTime",
            formatter: (row, column) => {
              if (!row[column]) return "--";
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
            formatter: (row, column) => {
              if (!row[column]) return "--";
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "135",
            slotName: "operation",
            "class-name": "operation-btn",
            fixed: "right",
          },
        ],
      },

      // 当前数据总数
      rowData: {},
      isEdit: false,
      // 报文内容弹框
      msgDialog: false,
      queryTimer: null,
    };
  },
  activated() {
    this.getTableList();
  },
  deactivated() {
    this.queryTimer && clearTimeout(this.queryTimer);
  },
  methods: {
    closeDialog() {
      this.msgDialog = false;
      this.rowData = {};
    },

    // 编辑
    rowDetail(data) {
      this.rowData = data;
      this.msgDialog = true;
    },
    rowReCall(row) {
      console.log(row);
      const { id } = row;
      const postObj = {
        id,
        onlyCallbackFailedChannel: 1, // 取值范围 0或1，1=只回调失败的通道: 0=回调全部通道: 默认为1
      };
      let config = {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      };
      $req.post("/athena/apiCallback/retrySendCallbackMsg", postObj, config).then((data = {}) => {
        // console.log(data)
        const { code } = data;
        if (code === 0) {
          this.getTableList();
        }
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    async getTableList() {
      if (this.queryTimer) {
        clearTimeout(this.queryTimer);
        this.queryTimer = null;
      }

      const { taskId, channelId, channelType, createTime, detail } = this.form;
      const { currentPage, pageSize } = this.tablePage;

      let data = { language: $utils.Data.getLocalLang() };
      if (taskId) data.taskId = taskId;
      if (channelId) data.channelId = channelId;
      if (channelType) data.channelType = channelType;
      if (createTime) {
        data.createTime = +new Date(new Date(createTime).setHours(0, 0, 0, 0));
      } else {
        data.createTime = +new Date(new Date().setHours(0, 0, 0, 0));
      }
      if (detail) data.detail = detail;
      this.$refs.customRef.setData(data);
      // 回调消息列表
      const res = await $req.post(
        `/athena/apiCallback/callbackMsgPageList?currentPage=${currentPage}&pageSize=${pageSize}`,
        data,
      );
      let result = res?.data;
      if (!result) return;
      this.tableData = result.recordList || [];
      this.tablePage = Object.assign({}, this.tablePage, {
        currentPage: result.currentPage || 1,
        total: result.recordCount || 0,
      });

      // this.queryTimer = setTimeout(() => {
      //   this.getTableList();
      // }, 2000)
    },
  },
};
</script>
<style scoped lang="less"></style>
