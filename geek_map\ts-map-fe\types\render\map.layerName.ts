/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /** 可点击的图层name */
  export type layerName =
    | "cell"
    | "robot"
    | "shelf"
    | "rack"
    | "xShelf"
    | "poppick"
    | "device"
    | "xDevice"
    | "charger"
    | "station"
    | "knockArea"
    | "realtimeObstacle";

  /** 可显示隐藏的图层name */
  export type toggleLayerName =
    | "location"
    | "load"
    | "unload"
    | "cellHeat"
    | "robotTrail"
    | "robotOccupy"
    | "shelfHeat"
    | "stopArea"
    | "realtimeObstacle"
    | "speedLimitArea"
    | "clearRobotArea"
    | "robotArea"
    | "rackAbnormalLattice"
    | "rackLockedBox";
}
