<template>
  <section class="shelf-manage-panel-wrap">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" style="padding: 5px 0" />
    <div class="table-content shelf-manage-panel-wrap__table">
      <geek-customize-table :table-config="tableConfig" :data="tableData" :page="tablePage" @page-change="pageChange" />
    </div>
  </section>
</template>

<script>
export default {
  data() {
    return {
      // 搜索条件
      form: {
        shelfCode: "",
        logicId: "",
        state: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          shelfCode: {
            label: "lang.rms.fed.shelfNumber",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          logicId: {
            label: "lang.rms.fed.region",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterAnNumber",
          },
          state: {
            label: "lang.rms.fed.lock",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {},
        columns: [
          { label: "lang.rms.fed.shelfNumber", prop: "shelfCode" },
          { label: "lang.rms.fed.region", prop: "logicId" },
          {
            label: "lang.rms.fed.lock",
            prop: "state",
            formatter: (row, column) => {
              if (row[column] === "NORMAL") {
                return this.$t("lang.rms.fed.no");
              } else {
                return this.$t("lang.rms.fed.yes");
              }
            },
          },
          { label: "lang.rms.fed.textLength", prop: "length" },
          { label: "lang.rms.fed.textWidth", prop: "width" },
          { label: "lang.rms.fed.textHeight", prop: "height" },
          { label: "lang.rms.fed.shelfPosition", prop: "location" },
          { label: "lang.rms.fed.placementCode", prop: "placement" },
          { label: "lang.rms.fed.angle", prop: "angle" },
          { label: "lang.rms.fed.shelfHeatDisplay", prop: "shelfHeat" },
          { label: "lang.rms.fed.cell", prop: "locationCellCode" },
          { label: "lang.rms.fed.placementCell", prop: "placementCellCode" },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      let params = Object.assign({}, this.form, { pageSize, currentPage });
      $req.get("/athena/shelf/findShelf", params).then(res => {
        let result = res?.data || {};
        this.tableData = result.recordList || [];

        this.tablePage = Object.assign({}, this.tablePage, {
          total: result.recordCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
