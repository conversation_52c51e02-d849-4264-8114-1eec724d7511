<template>
  <!-- 编辑任务 -->
  <gp-form ref="form" size="mini" :model="editTaskData" :rules="editTaskRules" label-width="140px">
    <!-- 货架模型名称 -->
    <gp-form-item prop="modelName" :label="$t('lang.rms.fed.shelfModelAlias')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelName"
        :disabled="viewDisabled"
        size="mini"
        maxlength="32"
        :placeholder="$t('lang.rms.fed.pleaseEnterContainerModelName')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 容器类型 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="viewDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- sizeType -->
    <gp-form-item prop="sizeTypes" :label="$t('lang.rms.fed.supportedSizeType')">
      <sizeTypeInput :value.sync="editTaskData.sizeTypes" @change="sizeTypesChange" />
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </gp-form-item>

    <!-- 是否可移动 -->
    <gp-form-item :label="$t('lang.rms.fed.isItMovable')">
      <gp-select class="w200" v-model="editTaskData.move" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option :label="$t('lang.rms.web.container.canNotMove')" :value="0" />
        <gp-option :label="$t('lang.rms.web.container.canMove')" :value="1" />
      </gp-select>
    </gp-form-item>

    <!-- 是否下发给机器人 -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        :placeholder="$t('lang.rms.fed.choose')"
        @change="val => handleNeedSendRobot(val)"
      >
        <gp-option :label="$t('lang.rms.fed.no')" :value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" :value="1" />
      </gp-select>
    </gp-form-item>

    <!-- 下发模型ID -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text"><gp-icon name="gp-icon-question" /></gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>

    <!-- 货架尺寸 -->
    <p class="modelTitle">{{ $t("lang.rms.fed.shelfSize") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="length"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.length"
            :min="dockModelMap[dockModelType].shelfMinHLen"
            :max="dockModelMap[dockModelType].shelfMaxHLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="width"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.width"
            :min="dockModelMap[dockModelType].shelfMinWLen"
            :max="dockModelMap[dockModelType].shelfMaxWLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="height" :label="$t('lang.rms.fed.textHeight') + '(mm):'" label-width="80px">
          <gp-input-number step-strictly v-model="editTaskData.height" :min="0" :max="100000" size="mini" :step="1" />
        </gp-form-item>
      </gp-col>
    </gp-row>
    <!-- 货架腿 -->
    <p class="modelTitle">{{ $t("lang.rms.fed.shelfFoot") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="legLength"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.legLength"
            :min="0"
            :max="dockModelMap[dockModelType].shelfMaxHLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="legWidth"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.legWidth"
            :min="0"
            :max="dockModelMap[dockModelType].shelfMaxWLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="legHeight" :label="$t('lang.rms.fed.textHeight') + '(mm):'" label-width="80px">
          <gp-input-number
            step-strictly
            v-model="editTaskData.legHeight"
            :min="0"
            :max="dockModelMap[dockModelType].shelfMaxHLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>
    <!-- 通行 -->
    <p class="modelTitle">{{ $t("lang.rms.fed.container.pass") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="passLength"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passLength"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="passWidth"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passWidth"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxWidth"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="passHeight" :label="$t('lang.rms.fed.textHeight') + '(mm):'" label-width="80px">
          <gp-input-number
            step-strictly
            v-model="editTaskData.passHeight"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxHeight"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>
  </gp-form>
</template>

<script>
import { mapMutations, mapState } from "vuex";
import sizeTypeInput from "./sizeTypeInput.vue";
const defaultEditTaskData = () =>
  JSON.parse(
    JSON.stringify({
      modelCategory: "SHELF",
      modelName: "",
      modelType: "",
      sizeTypes: "",
      length: 1280,
      width: 1350,
      height: 3580,
      legLength: 20,
      legWidth: 20,
      legHeight: 300,
      move: 1,
      needSendRobot: 0,
      sendModelId: "",
      passLength: 0,
      passWidth: 0,
      passHeight: 0,
    }),
  );

export default {
  props: {
    subCategory: String,
  },
  data() {
    return {
      sizeTypeParamTip: null,
      modelId: "",
      // 编辑任务数据
      editTaskData: defaultEditTaskData(),
      // loading
      saveLoading: false,
      requsetTaskLoading: false,
      requsetTaskTypeListLoading: false,
      dockModelType: "def",
      dockModelMap: {
        M100: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 800,
          shelfMaxWLen: 1100,
          shelfMaxHLen: 900,
          shelfMinWLen: 600,
          shelfMinHLen: 600,
        },
        M1000: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1220,
          shelfDefHLen: 1020,
          shelfMaxWLen: 1500,
          shelfMaxHLen: 1500,
          shelfMinWLen: 1020,
          shelfMinHLen: 1020,
        },
        def: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 1000,
          shelfMaxWLen: 50000,
          shelfMaxHLen: 50000,
          shelfMinWLen: 300,
          shelfMinHLen: 300,
          passMaxLen: 50000,
          passMaxWidth: 50000,
          passMaxHeight: 50000,
        },
      },
    };
  },
  components: { sizeTypeInput },
  computed: {
    ...mapState("containerModal", ["editData", "maxModelId", "shelfCategoryDict"]),
    isEmpty() {
      return this.$store.state.containerModal.emptySwich;
    },
    viewDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };
      return {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          requiredRule,
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        // sizeTypes: [{
        //   // 正则 只允许输入英文，数字，和标点符号，限制15字符之内
        //   pattern: /^[a-zA-Z0-9\-\_]{0,15}$/,
        //   message: this.$t("lang.rms.fed.enter15CharactersOr01"),
        //   trigger: "blur",
        // }],
        width: [requiredRule],
        length: [requiredRule],
        height: [requiredRule],
        legWidth: [requiredRule],
        legLength: [requiredRule],
        categoryId: [requiredRule],
        passWidth: [requiredRule],
        passHeight: [requiredRule],
        passLength: [requiredRule],
      };
    },
    isPopPik() {
      return this.subCategory === "PPP_SHELF";
    },
  },
  watch: {
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
    "editTaskData.sizeTypes"(val) {
      let valList = val ? val.split(",") : [];
      if (val) this.sizeTypesChange(valList);
    },
  },
  activated() {
    this.dockModelType = "def";
  },
  created() {
    if (this.editData.id) {
      this.editTaskData = JSON.parse(JSON.stringify(this.editData));
    }
  },
  methods: {
    ...mapMutations("containerModal", ["setHJModalData"]),
    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null;
        return;
      }
      const reg = /^[a-zA-Z]{0,15}$/;
      if (data) {
        data.forEach(item => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null;
          } else {
            this.sizeTypeParamTip = this.$t("lang.rms.fed.enterEnglish15Characters");
          }
        });
      }
    },
    claerData() {
      this.editTaskData = defaultEditTaskData();
    },
    handleNeedSendRobot(val) {
      if (val) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },
    async validateData() {
      try {
        await this.$refs.form.validate();
        return this.editTaskData;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.modelTitle {
  font-weight: 600;
  height: 26px;
  margin: 5px 0;
  text-align: left;
  font-size: 14px;
  padding: 3px 12px;
  background: #eee;
}

.labelTitle {
  text-align: right;
  padding-right: 8px;
  font-size: 14px;
}

.editTask {
  font-size: 14px;
  padding-bottom: 38px;

  .hr {
    height: 5px;
  }

  .sizeType {
    min-width: 200px;
    max-width: 100%;
  }

  .pointsBoxsMain {
    flex: 1;
    padding: 10px;
    overflow: auto;
    border-radius: 10px;
    border: 1px solid #ccc;
  }

  .taskTitle {
    text-align: left;
    padding-top: 20px;
    padding-left: 10px;
  }

  .taskAddTaskTitle {
    height: 32px;
    line-height: 32px;
    text-align: left;
  }

  .pointsBox {
    text-align: left;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    position: relative;
    margin-bottom: 10px;

    .clearPoint {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
}
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}
.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
