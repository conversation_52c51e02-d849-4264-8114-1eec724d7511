<template>
  <div class="container-type">
    <component
      :is="currentCom"
      :mode="mode"
      :row-detail.sync="row"
      @updateCom="updateCom"
      @update:row-detail="rowDetail = $event"
    />
  </div>
</template>
<script>
import PalletPositionManageList from "./components/list.vue"
import PalletPositionManageDetail from "./components/detail.vue"
export default {
  components: {
    PalletPositionManageList,
    PalletPositionManageDetail,
  },
  data() {
    return {
      currentCom: "PalletPositionManageList",
      mode: "",
      row: {},
    }
  },
  methods: {
    updateCom({ currentCom, mode, row = {} }) {
      this.currentCom = currentCom
      this.mode = mode
      this.row = row
      console.log(this.currentCom, this.mode, this.row)
    },
  },
}
</script>
