<template>
  <gp-pagination
    border
    :current-page="currentPage"
    :page-size="pageSize"
    :page-count="totalPage"
    :page-sizes="[10, 25, 50, 100]"
    layout="total, sizes, prev, pager, next, jumper"
    class="geek-pagination"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
  </gp-pagination>
</template>

<script>
export default {
  name: "GeekPagination",
  props: {
    currentPage: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    totalPage: {
      type: Number,
      default: 1,
    },
  },
  methods: {
    handleSizeChange(val) {
      const pageSize = val;
      this.$emit("pageSizeChange", pageSize);
    },
    handleCurrentChange(val) {
      const currentPage = val;
      this.$emit("currentPageChange", currentPage);
    },
  },
};
</script>

<style lang="less" scoped></style>
