import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 机器人充电无电趋势
 */
export default class RmsJobBoxArriveTransferToP40StartMoveBar extends Chart {
  /**
   * 初始化图表 - 机器人充电无电趋势
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "lang.rms.stats.show.rmsJobBoxArriveTransferToP40StartMove";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
        valType: 'yyyy-MM-dd',
        option: {

        }
      }
    ]
  }

  async request(params = {}) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/collect', {
      date: $utils.Tools.formatDate(params?.date || new Date(), "yyyy-MM-dd"),
      "showJobList": true,
      "showJobGroupList": true,
      "showJobGroupMinuteList": true,
      "showJobMinuteIdList": true,
    })

    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const rmsJobReceiveToRsStartMoveData = data.jobMinuteGroupList?.['lang.rms.stats.show.rmsJobBoxArriveTransferToP40StartMove'] || {};
    const { xAxisData, seriesData } = this.sortChartDataAt0(rmsJobReceiveToRsStartMoveData)
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      tooltip: { show: true, trigger: 'axis' },
      series: [{ data: seriesData, type: 'bar' }]
    })
  }
}