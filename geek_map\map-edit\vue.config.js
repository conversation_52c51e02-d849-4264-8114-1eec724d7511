const path = require("path");
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
function resolve(dir) {
  return path.join(__dirname, dir);
}
module.exports = {
  // pages: {
  //   index: {
  //     entry: "packages/main.ts",
  //     template: "public/index.html",
  //     filename: "index.html",
  //     title: "MapEdit",
  //   },
  // },
  transpileDependencies: false,
  lintOnSave: false,
  publicPath: process.env.NODE_ENV === "production" ? "./" : "/singleEdit2D/",
  // 代理
  devServer: {
    port: 8899,
    proxy: {
      "/athena": {
        // target: `http://172.16.4.193/`,
        // target: `http://172.16.4.234/`,
        target: `http://172.16.8.174/`,
        // target: `http://test-13051.local.k8s.ops.geekplus.cc/`,
        changeOrigin: true,
      },
    },
  },
  configureWebpack: {
    resolve: {
      alias: {
        "@packages": resolve("packages"),
        "@src": resolve("src"),
      },
    },
    
    plugins: [
      new BundleAnalyzerPlugin({
        analyzerMode: "json",
        generateStatsFile: false,
        reportFilename: "../../../vuls-scan/map-edit/report.json",
      })
    ]
  },
  chainWebpack: config => {
    // 为了解决这个wran: You are running the esm-bundler build of vue-i18n. It is recommended to configure your bundler to explicitly replace feature flag globals with boolean literals to get proper tree-shaking in the final bundle.
    config.resolve.alias.set("vue-i18n", "vue-i18n/dist/vue-i18n.cjs.js");
  },
};
