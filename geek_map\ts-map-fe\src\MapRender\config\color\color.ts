/* ! <AUTHOR> at 2022/07/26 */

const roadColor = 0xd2e5f5;
/** color配置 16进制*/
const MonitorMapColor = {
  BG: 0x9253f, // viewport背景色
  NUll_CELL: 0xfffff, // 空cell
  DEFAULT_CELL: 0xdddddd, // 默认cellColor

  OMNI_DIR_CELL: roadColor, // 道路节点
  E2W_PATH_CELL: roadColor, // 带方向道路节点
  W2E_PATH_CELL: roadColor, // 带方向道路节点
  S2N_PATH_CELL: roadColor, // 带方向道路节点
  N2S_PATH_CELL: roadColor, // 带方向道路节点
  E2W_S2N_PATH_CELL: roadColor, // 带方向道路节点
  E2W_N2S_PATH_CELL: roadColor, // 带方向道路节点
  W2E_S2N_PATH_CELL: roadColor, // 带方向道路节点
  W2E_N2S_PATH_CELL: roadColor, // 带方向道路节点
  E2W_W2E_PATH_CELL: roadColor, // 带方向道路节点
  N2S_S2N_PATH_CELL: roadColor, // 带方向道路节点
  E2W_W2E_N2S_PATH_CELL: roadColor, // 带方向道路节点
  E2W_W2E_S2N_PATH_CELL: roadColor, // 带方向道路节点
  N2S_S2N_E2W_PATH_CELL: roadColor, // 带方向道路节点
  N2S_S2N_W2E_PATH_CELL: roadColor, // 带方向道路节点

  CHARGER_CELL: 0xd2e5f5, // 充电站点
  STATION_CELL: 0xf29361, // 工作站
  DROP_CELL: 0x90dd9f, // 投递点
  QUEUE_CELL: 0xede2be, // 排队点
  SHELF_CELL: 0x74c3cb, // 货架点
  BOX_RACK_CELL: 0x42b983, // 固定货架
  TURN_CELL: 0xd7c99f, // 转面点
  CHARGER_PI_CELL: 0xdddddd, // 充电桩点
  BLOCKED_CELL: 0xdddddd, // 障碍点
  ELEVATOR_CELL: 0xd2e5f5, // 电梯点
  PALLET_RACK_CELL: 0x7890e9, // 托盘位点
  /* ---- 单元格状态相关 ---------------------------------------- */
  SELECTED: 0xf95959, // Cell、货架、机器人选中颜色
  LOCKED: 0x767676, // Cell锁定颜色
  STOPPED: 0xe83532, // Cell停止(暂停)颜色
  /* ---- 实体元素 -------------------------------------- */
  SHELF: 0x103c96, // 货架
  RACK: 0x103c96, // 货箱架
  RACK_EMPTY: 0x185be0, // 货箱架
  RACK_ABNORMAL: 0xff0000, // 异常的货箱架
  C_RACK: 0x103c96, // 移动货箱架 poppick
  LOCATION: 0xffff00, // Cell Location点
  SHELF_FRONT: 0xff9900, // 货架F面标识点
  CHARGER_NORMAL: 0xa7ce63, // 充电站正常颜色
  CHARGER_WORK: 0x4096ee, // 充电站工作状态颜色
  CHARGER_OFFLINE: 0xc44ef2, // 充电站离线颜色
  CHARGER_ERROR: 0xe75552, // 充电站错误颜色
  DEVICES_NORMAL: 0x215278, // 设备-正常
  DEVICES_ABNORMAL: 0xff0000, // 设备-异常
  DEVICES_UNUSUAL: 0xffd900, // 设备-配置异常
  /* ---- 区域相关 -------------------------------------- */
  AREA: 0x8bd4db, // 区域
  AREA_LINE: 0x838383, // 区域边线
  STATIONS_AREA: 0xff183e, // 工作站区域
  KNOCK_AREA: 0xcd2990, // 撞击区域
  /* ---- 线、空负载颜色 ---------------------------------- */
  S_LINE: 0x2222aa, // 路线直线
  BEZIER: 0x2222aa, // 路线曲线
  ARC: 0x2222aa, // 路线曲线
  ROBOT_PATH: 0xfb3341, // 机器人路径
  SEGMENT: 0x8bd4db, // map-segments的颜色
  LOAD_CELL_DIR: 0x2222aa, // cell负载箭头颜色
  UNLOAD_CELL_DIR: 0xd54056, // cell空载箭头颜色
  LOAD_LINE_DIR: 0x2222aa, // 线段负载箭头颜色
  UNLOAD_LINE_DIR: 0x2222aa, // 线段空载箭头颜色
  /* ---- 货架热度 0->10热度从低到高 ------------------------- */
  HOT_CONF: {
    0: 0x72fc03,
    1: 0xa1fc03,
    2: 0xbefc03,
    3: 0xf9fc03,
    4: 0xfcd003,
    5: 0xfc9b03,
    6: 0xfc7203,
    7: 0xfc4303,
    8: 0xfc2c03,
    9: 0xfc0303,
    10: 0xfc0303,
  },
};
export default MonitorMapColor;
