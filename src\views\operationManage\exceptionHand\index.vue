<template>
  <geek-main-structure class="exceptionHandBox">
    <gp-card>
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.exchange.shelf") }}</span>
      </div>
      <gp-form ref="form" :model="form" label-width="80px" label-position="top">
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.optionShelf') + 1">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 1)" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.optionShelf') + 2">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 2)" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div class="btnwarp2">
              <gp-button type="primary" @click="getShelf">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 修改货架老家位置 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.modifyHomeLocShelf") }}</span>
      </div>
      <gp-form>
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.optionShelf')">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 4)" />
            </gp-form-item>
          </gp-col>

          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.shelfPosition')">
              <gp-input v-model="cellCode" :placeholder="$t('lang.rms.fed.pleaseEnterTargetCellNumber')" />
            </gp-form-item>
          </gp-col>

          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitReviseLocation">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.workstation.cancellation") }}</span>
      </div>
      <gp-form>
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.web.monitor.robot.workStationId')">
              <geek-fuzzy-search :id="shelfId" query-type="Qstation" @fuzzySearchBub="val => fuzzySearchBub(val, 3)" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitShelf">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.task.canceled") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.web.monitor.robot.taskId')">
              <gp-input v-model="taskId" type="number" :placeholder="$t('lang.rms.web.monitor.robot.taskId')" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitTaskId">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.speed.stored.speedControllerManage") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.mapArea.id')">
              <gp-input v-model="areaIds" :placeholder="$t('lang.rms.fed.mapArea.id')" />
              <div v-show="areaIdTip" class="areaIdTip">
                {{ $t("lang.rms.fed.area.error.speedControllerManage") }}
              </div>
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitAreaId(true)">
                {{ $t("lang.rms.fed.speed.start.speedControllerManage") }}
              </gp-button>
              <gp-button type="primary" @click="submitAreaId(false)">
                {{ $t("lang.rms.fed.speed.stop.speedControllerManage") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 容器移除需求 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.web.container.remove") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.shelfRemove')">
              <gp-input v-model="shelfCode" :placeholder="$t('lang.rms.fed.pleaseEnterShelfCode')" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitRemoveShelf">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.web.box.remove')">
              <gp-input v-model="boxRemoveCode" :placeholder="$t('lang.rms.fed.web.boxCode.input')" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitRemoveBox">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 机器人累计错误重置 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.clear.robot.error.count") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.mb.robotManage.robotId')">
              <gp-input
                v-model="robotId"
                type="number"
                :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotID')"
                clearable
                onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitRemoveAbnormalCount">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 清除等待点 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.web.clear.waitCell") }}</span>
      </div>
      <div v-for="(item, index) in waitPointList" :key="index" class="waitpoint-group">
        <gp-row :gutter="20">
          <gp-col :span="5">
            <span class="label">{{ $t("lang.rms.web.monitor.robot.taskId") }}</span>
            <gp-input v-model="item.taskId" :placeholder="$t('lang.rms.web.monitor.robot.taskId')">
            </gp-input>
          </gp-col>
          <gp-col :span="5">
            <span class="label">{{ $t("lang.rms.fed.web.waitCellCode") }}</span>
            <gp-input
              v-model="item.waitCellCode"
              :placeholder="$t('lang.rms.fed.web.waitCellCode')"
            >
            </gp-input>
          </gp-col>
          <gp-col :span="12">
            <div class="btnbox">
              <gp-icon
                v-if="waitPointList.length > 1"
                class="hover"
                name="gp-icon-delete"
                @click="onDeleteWaitPoint(index, item.value)"
              ></gp-icon>
              <gp-button
                v-if="index === 0"
                type="primary"
                @click="onAddWaitPoint"
              >
                {{ $t("lang.rms.fed.addTo1") }}
              </gp-button>
              <gp-button
                v-if="index === 0"
                type="primary"
                @click="submitWaitPoint"
              >
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </div>
    </gp-card>

    <!-- 滚筒按需到达 -->
    <!-- <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.rollersArriveDemand") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.web.station.stationId')">
              <gp-input v-model="rollersArriveDemandStationId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="completedRollArrDe">
                {{ $t("lang.rms.fed.textCompleted") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card> -->

    <!-- 货箱任务异常处理 -->
    <!-- <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.boxTaskAbnormal") }}</span>
      </div>
      <gp-form>
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.stationNo')" required>
              <gp-select v-model="stationCode" :placeholder="$t('lang.rms.fed.stationNo')" @change="onStationChange">
                <gp-option v-for="item in stationList" :key="item" :label="item" :value="item"> </gp-option>
              </gp-select>
            </gp-form-item>
          </gp-col>

          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.park')">
              <gp-select v-model="stopPoint" :placeholder="$t('lang.rms.fed.park')">
                <gp-option v-for="item in stopList" :key="item" :label="item" :value="item"> </gp-option>
              </gp-select>
            </gp-form-item>
          </gp-col>

          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" :disabled="!stopPoint" @click="onSearch">
                {{ $t("lang.rms.fed.searchTask") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
      <div class="task-content">
        <div v-if="taskObj" class="task-content-form">
          <p>
            <span :title="$t('lang.rms.fed.jobTaskId')">{{ $t("lang.rms.fed.jobTaskId") }}</span
            >：
            <span>{{ taskObj.jobId }}</span>
          </p>
          <p>
            <span :title="$t('lang.rms.fed.plcTaskId')">{{ $t("lang.rms.fed.plcTaskId") }}</span
            >：
            <span>{{ taskObj.plcTaskId }}</span>
          </p>
          <p>
            <span :title="$t('lang.rms.fed.boxCode')">{{ $t("lang.rms.fed.boxCode") }}</span
            >：
            <span>{{ taskObj.boxCode }}</span>
          </p>
          <p>
            <span :title="$t('lang.rms.fed.boxPosition')">{{ $t("lang.rms.fed.boxPosition") }}</span
            >：
            <span>{{ taskObj.boxPosition }}</span>
          </p>
          <p>
            <span :title="$t('lang.rms.box.boxStatus')">{{ $t("lang.rms.box.boxStatus") }}</span
            >：
            <span>{{ taskObj.boxStatus }}</span>
          </p>
        </div>
        <div v-else class="no-task-data">
          {{ $t("lang.rms.fed.notExistData") }}
        </div>
      </div>
      <div v-if="taskObj" class="task-content-tip">
        <gp-alert
          :title="$t('lang.mb.robotOperate.tips')"
          type="warning"
          :closable="false"
          :description="$t('lang.rms.fed.boxTaskAbnormalTip')"
        />
      </div>
      <gp-form v-if="taskObj">
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.boxRealityPosition')" required>
              <gp-select v-model="containerPosition" :placeholder="$t('lang.rms.fed.boxRealityPosition')">
                <gp-option
                  v-for="item in containerPositionList"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                >
                </gp-option>
              </gp-select>
            </gp-form-item>
          </gp-col>

          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" :disabled="!containerPosition" @click="submitAbnormal">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card> -->

    <!-- 归还货箱 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.returnBox") }}</span>
      </div>
      <gp-form>
        <gp-row :gutter="20">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.boxCode')" required>
              <gp-input v-model="boxCode" :placeholder="$t('lang.rms.fed.boxCode')" />
            </gp-form-item>
          </gp-col>

          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.boxSides')">
              <gp-select v-model="boxSide" clearable :placeholder="$t('lang.rms.fed.boxSides')">
                <gp-option v-for="item in surfaceOptions" :key="item" :label="item" :value="item"> </gp-option>
              </gp-select>
            </gp-form-item>
          </gp-col>

          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" :disabled="boxCode === ''" @click="submitReturnContainer">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 工作站机器人转向 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.pp.workstation.robotTurn") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.mb.robotManage.robotId')">
              <gp-input
                v-model="stationRobotId"
                type="number"
                :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotID')"
                clearable
                onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitStationRobotTurn">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 清除PopPick设备残留任务 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.operator.devicejob.clear") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.stationPoint.parkId')">
              <gp-input
                v-model="parkId"
                type="number"
                :placeholder="$t('lang.rms.fed.stationPoint.parkId')"
                clearable
                onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitClearDeviceTask">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>

    <!-- 解锁账号 -->
    <gp-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.unlockUsername") }}</span>
      </div>
      <gp-form>
        <gp-row>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.userName')">
              <gp-input
                v-model="userName"
                :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.fed.userName')}`"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <div class="btnwarp2">
              <gp-button type="primary" @click="submitDeblocking">
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>
  </geek-main-structure>
</template>

<script>
/**
 *  lang.rms.fed.shelfRemove 货架移除
 */
export default {
  data() {
    return {
      shelfId: "",
      form: {
        sourceShelfCode: "",
        targetShelfCode: "",
      },
      cancelForm: {
        stationId: null,
      },
      taskId: null,
      tasksetInterval: null,
      options: [],
      headers: { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
      areaIds: null,
      areaIdTip: false,
      shelfCode: null,
      cellCode: "",
      localShelfCode: "",
      userName: "",
      stationCode: "",
      stationList: [],
      stopPoint: "",
      stopList: [],
      taskObj: null,

      containerPosition: "",
      containerPositionList: [],
      boxRemoveCode: null,

      boxCode: "",
      boxSide: "",
      surfaceOptions: [
        // {
        //   label: 'B',
        //   value: 'B'
        // },
        // {
        //   label: 'F',
        //   value: 'F'
        // },
        // {
        //   label: 'L',
        //   value: 'L'
        // },
        // {
        //   label: 'R',
        //   value: 'R'
        // }
      ],
      rollersArriveDemandStationId: "",
      robotId: "",
      waitPointList: [
        {
          taskId: "",
          waitCellCode: ""
        }
      ],
      stationRobotId: "",
      // 停靠点ID
      parkId: ""
    };
  },
  watch: {
    areaIds() {
      const reg = /^((-1|[1-9]\d*),)*(-1|[1-9]\d*)$/;
      reg.test(this.areaIds) ? (this.areaIdTip = false) : (this.areaIdTip = true);
    },
  },
  activated() {
    this.areaIdTip = false;
  },
  deactivated() {
    this.dataReset();
  },
  created() {
    this.getStationList();
    this.getBoxLocation();
    this.getBoxsides();
  },
  methods: {
    dataReset() {
      Object.assign(this.$data, this.$options.data.call(this));
    },
    getShelf() {
      // 异常处理货架交换
      $req
        .post(
          "/athena/shelf/swapShelf",
          $utils.Tools.getParams({
            sourceShelfCode: this.form.sourceShelfCode,
            targetShelfCode: this.form.targetShelfCode,
          }),
          this.headers,
        )
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    submitShelf() {
      const stationId = this.cancelForm.stationId;

      if (!stationId) {
        this.$error($utils.Tools.transMsgLang("lang.rms.api.result.warehouse.stationIdNull"));
        return false;
      }

      if(!/^[1-9]\d*$/.test(+stationId)) {
        this.$error($utils.Tools.transMsgLang("lang.rms.containerManage.sendModelId.check"));
        return false;
      }
      // 异常处理货架工作站取消
      $req
        .post(
          "athena/station/cancelTask",
          $utils.Tools.getParams({ stationId }),
          this.headers,
        )
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    submitTaskId() {
      if (!this.taskId) {
        this.$error($utils.Tools.transMsgLang("lang.rms.fed.taskIdCannotBeEmpty"));
        return false;
      }
      // 异常处理货架任务取消
      $req.post("/athena/task/cancel", $utils.Tools.getParams({ taskId: this.taskId }), this.headers).then(res => {
        this.$message({
          message: $utils.Tools.transMsgLang(res.msg),
          type: res.code === 0 ? "success" : "error",
        });
      });
    },
    // 货架移除
    async submitRemoveShelf() {
      if (!this.shelfCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterShelfCode"));
      const { code } = await window.$req.post("/athena/exceptionHandle/removeShelf", {
        shelfCode: this.shelfCode,
      });
      if (!code) return this.$success(this.$t("lang.common.success"));
    },
    // 货箱移除
    async submitRemoveBox() {
      if (!this.boxRemoveCode) return this.$error(this.$t("lang.rms.fed.web.boxCode.input"));
      const { code, msg } = await window.$req.get(
        "/athena/poppick/box/remove",
        {
          boxCode: this.boxRemoveCode,
        },
        { intercept: false },
      );
      if (!code) {
        this.boxRemoveCode = null;
        this.$success(this.$t("lang.common.success"));
      } else if (code === 23123) {
        const execData = /(?<lang>[\w.]+),\[(?<param>.+)\]/.exec(msg);
        const { lang, param } = execData.groups;
        this.$confirm(`${this.$t(lang, [param])}?`, {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.common.cancel"),
          type: "warning",
        })
          .then(async () => {
            const { code } = await window.$req.get("/athena/poppick/box/safeRemove", {
              boxCode: this.boxRemoveCode,
            });
            if (!code) {
              this.boxRemoveCode = null;
              this.$success(this.$t("lang.common.success"));
            }
          })
          .catch(() => {});
      } else {
        this.$error(this.$t(msg));
      }
    },
    // 机器人累计错误重置
    async submitRemoveAbnormalCount() {
      if (!this.robotId) return this.$error(this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotID"));
      const { code } = await window.$req.post("/athena/exceptionHandle/clearRobotExceptionCount", {
        robotId: Number(this.robotId),
      });
      if (!code) return this.$success(this.$t("lang.common.success"));
    },
    // 工作站机器人转向
    async submitStationRobotTurn() {
      if (!this.stationRobotId) return this.$error(this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotID"));
      const { code } = await window.$req.post("/athena/task/pp/turn", $utils.Tools.getParams({
        robotId: Number(this.stationRobotId),
      }), this.headers);
      if (!code) return this.$success(this.$t("lang.common.success"));
    },

    // 清除PopPick设备残留任务
    async submitClearDeviceTask() {
      if (!this.parkId) return this.$error(this.$t("lang.rms.api.result.parameter.stationParkIdNull"));
      const { code } = await window.$req.post("/athena/deviceTask/clearResidualJob", {
        parkId: Number(this.parkId),
      });
      if (!code) return this.$success(this.$t("lang.common.success"));
    },

    async submitReviseLocation() {
      if (!this.localShelfCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterShelfCode"));
      if (!this.cellCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterTargetCellNumber"));
      const { code } = await window.$req.postParams("/athena/shelf/changeShelfPlacement", {
        shelfCode: this.localShelfCode,
        cellCode: this.cellCode,
      });

      if (code === 0) return this.$success(this.$t("lang.common.success"));
    },
    submitAreaId(active) {
      const areaIdArr = this.areaIds ? this.areaIds.split(",") : [];
      if (!this.areaIds || areaIdArr.length === 0 || this.areaIdTip) {
        this.$error($utils.Tools.transMsgLang("lang.rms.api.result.areaIdIsNotAllowNull"));
        return false;
      }
      $req
        .post("/athena/exceptionHandle/changeSpeed", {
          areaIds: areaIdArr,
          active: active,
        })
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    // 接收子组件传递过来的数据
    fuzzySearchBub(params, val) {
      if (params.pointer.value) {
        //   // 使用模糊查询的情况
        if (val === 1) {
          this.form.sourceShelfCode = params.pointer.value;
        } else if (val === 2) {
          this.form.targetShelfCode = params.pointer.value;
        } else if (val === 4) {
          this.localShelfCode = params.pointer.value;
        } else {
          this.cancelForm.stationId = params.pointer.value;
        }
      } else {
        // 没有使用模糊查询的情况
        if (val === 1) {
          this.form.sourceShelfCode = params.pointer;
        } else if (val === 2) {
          this.form.targetShelfCode = params.pointer;
        } else if (val === 4) {
          this.localShelfCode = params.pointer;
        } else {
          this.cancelForm.stationId = params.pointer;
        }
      }
    },

    // 解锁账号
    submitDeblocking() {
      $req
        .post("/athena/api/coreresource/auth/user/unlockUser/v1", {
          userName: this.userName,
        })
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    // 获取poppick工作站
    getStationList() {
      $req.get("/athena/poppick/station/select").then(res => {
        console.log(res);
        this.stationList = res.data || [];
      });
    },
    onStationChange() {
      this.stopPoint = "";
      this.getStopList(this.stationCode);
    },
    // 查询停靠点
    getStopList(code) {
      $req.get(`/athena/poppick/station/select/${code}`).then(res => {
        console.log(res);
        this.stopList = res.data || [];
      });
    },
    // 查询停靠点任务
    getStopTask(point) {
      $req.get(`/athena/poppick/query/park/task/${point}`).then(res => {
        console.log(res);
        this.taskObj = res.data || null;
      });
    },
    // 查询箱子位置
    getBoxLocation() {
      $req.get(`/athena/poppick/boxLocation`).then(res => {
        console.log(res);
        const obj = res.data || {};
        const arr = [];
        for (let key in obj) {
          arr.push({
            value: key,
            label: obj[key],
          });
        }
        this.containerPositionList = arr;
      });
    },
    // 查询货箱面
    getBoxsides() {
      $req.get(`/athena/poppick/box/sides`).then(res => {
        // console.log(res)
        this.surfaceOptions = res.data || [];
      });
    },
    // 任务查询
    onSearch() {
      this.getStopTask(this.stopPoint);
    },
    // 货箱异常处理
    async submitAbnormal() {
      const { code } = await $req.post("/athena/poppick/exception/confirm", {
        parkId: this.stopPoint,
        boxPosition: this.containerPosition,
      });
      if (!code) return;
      this.containerPosition = "";
      this.$message.success(this.$t("lang.common.success"));
    },
    // 归还货箱
    async submitReturnContainer() {
      const { code } = await $req.post("/athena/poppick/box/return", {
        boxCode: this.boxCode,
        boxSide: this.boxSide,
      });
      if (!code) return this.$message.success(this.$t("lang.common.success"));
    },
    completedRollArrDe() {
      if (!this.rollersArriveDemandStationId) {
        this.$message({
          message: this.$t("lang.rms.api.result.parameter.stationIdNull"),
          type: "error",
        });
        return;
      }
      $req
        .get(`/athena/engine/tools/arrivedGroupTask/removeExceptionTask?stationId=${this.rollersArriveDemandStationId}`)
        .then(res => {
          this.rollersArriveDemandStationId = "";
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },

    // 清除等待点
    submitWaitPoint() {
      $req
        .post("/athena/task/clearWaitPoint", this.waitPointList)
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    onAddWaitPoint() {
      this.waitPointList.push({
        taskId: "",
        waitCellCode: ""
      })
    },
    onDeleteWaitPoint(index) {
      this.waitPointList.splice(index, 1)
    }
  },
};
</script>

<style lang="less" scoped>
.exceptionHandBox {
  .gp-select {
    width: 100%;
  }

  .btnwarp {
    padding: 43px 0 0;
  }

  .btnwarp2 {
    padding: 33px 10px 0;
  }
  .btnbox {
    display: flex;
    align-items: center;
    padding: 33px 0 0;

  }
  :deep(input[type="number"]) {
    -moz-appearance: textfield;
  }

  :deep(input[type="number"]::-webkit-inner-spin-button),
  :deep(input[type="number"]::-webkit-outer-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }

  .mt-5 {
    margin-top: 5px;
  }

  .areaIdTip {
    color: red;
  }

  .waitpoint-group {
    .label {
      line-height: 32px;
      font-size: 14px;
      color: #606266;
    }
    .hover {
      margin-right: 10px;
      font-size: 18px;
      cursor: pointer;
      &:hover {
        color: #409eff;
      }
    }
  }
}

.task-content-form {
  background: #efefef;
  padding: 16px;
  margin-bottom: 16px;
  font-size: 14px;

  p {
    margin: 4px 0;
    line-height: 20px;
    display: flex;
    align-items: center;

    span {
      &:first-of-type {
        display: inline-block;
        width: 110px;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}

.no-task-data {
  background: #efefef;
  padding-top: 50px;
  margin-bottom: 16px;
  font-size: 14px;
  text-align: center;
  height: 150px;
  color: #5e6d82;
}
</style>
