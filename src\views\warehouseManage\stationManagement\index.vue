<template>
  <geek-main-structure class="flex flex-col">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset" />
    <div class="flex-1">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @selectionChange="handleSelection"
        @enableStation="changeStationStatus(1)"
        @disableStation="changeStationStatus(0)"
        @page-change="pageChange"
        @row-edit="rowEdit"
        @row-cache-shelf="cacheShelfSet"
      >
        <template #rowExpand="{ row }">
          <geek-customize-table
            :table-config="expandTableConfig"
            :data="row.stationPoints"
            @expand-row-edit="expandRow => expandRowEdit(expandRow, row)"
          >
            <template #isWorking="{ row }">
              <gp-tag v-if="row.isWorking == 'true'" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</gp-tag>
              <gp-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</gp-tag>
            </template>

            <template #showDeviceMsg="{ row }">
              <gp-popover
                v-if="row.extendJson && row.extendJson.bindSubDeviceCode"
                placement="left-start"
                title=""
                width="450"
                trigger="click"
                @hide="hideDeviceMsg"
              >
                <pre class="code-show-box"><code class="json-core" v-text="curDeviceJson"></code></pre>
                <gp-button
                  slot="reference"
                  type="text"
                  @mouseover="e => (buttonRef = e.currentTarget)"
                  @click="expandDeviceMsg(row)"
                >
                  {{ $t("lang.rms.fed.buttonView") }}
                </gp-button>
              </gp-popover>
            </template>
          </geek-customize-table>
        </template>
        <template #manageStatus="{ row }">
          <gp-tag v-if="row.manageStatus == 1" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</gp-tag>
          <gp-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</gp-tag>
        </template>
      </geek-customize-table>
    </div>
    <cache-rack-dialog
      v-if="dialogRackVisible"
      :visible.sync="dialogRackVisible"
      :init-row="dialogInitRow"
      @save="getTableList"
    />
    <EditSingleStation ref="editSingleStationDialog" @updateMainList="getTableList" />
    <EditStation ref="editStationDialog" :robotTypes="robotTypes" @updateMainList="getTableList" />
    <EditPark ref="editParkDialog" :robotTypes="robotTypes" @updateMainList="getTableList" />
  </geek-main-structure>
</template>

<script>
import CacheRackDialog from "./components/cacheRackDialog";
import EditSingleStation from "./components/editSingleStation";
import EditStation from "./components/editStation";
import EditPark from "./components/editPark";

export default {
  components: { CacheRackDialog, EditSingleStation, EditStation, EditPark },
  data() {
    return {
      robotTypes: [],
      form: {
        stationId: "",
        hostCode: "",
        type: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          // 工作站id
          stationId: {
            label: "lang.rms.web.station.stationId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.station.stationIdPlaceHolder",
          },
          // 外部编码
          hostCode: {
            label: "lang.rms.fed.hostCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterExternalCode",
          },
          // 工作站type
          type: {
            label: "lang.rms.web.station.stationType",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
          "row-key": "stationId",
          "reserve-selection": true,
          "row-class-name": ({ row }) => {
            if (!row?.stationPoints || !row.stationPoints.length) {
              console.log(row);
              return "no-expand";
            }
          },
        },
        expand: null,
        actions: [
          {
            label: "lang.rms.fed.enable",
            type: "primary",
            handler: "enableStation",
          },
          {
            label: "lang.rms.fed.stopStatus",
            type: "primary",
            handler: "disableStation",
          },
        ],
        columns: [
          { label: "lang.rms.web.station.stationId", prop: "stationId" },
          {
            label: "lang.rms.web.station.status",
            prop: "manageStatus",
            slotName: "manageStatus",
            align: "center",
          },
          {
            label: "lang.rms.web.station.stationType",
            prop: "typeDesc",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          { label: "lang.rms.web.station.cellCode", prop: "cellCode" },
          {
            label: "lang.rms.fed.WorkStationFace",
            prop: "place",
            formatter: (row, column) => {
              switch (row[column]) {
                case "east":
                  return this.$t("lang.rms.fed.east");
                case "south":
                  return this.$t("lang.rms.fed.south");
                case "west":
                  return this.$t("lang.rms.fed.west");
                case "north":
                  return this.$t("lang.rms.fed.north");
                default:
                  return "";
              }
            },
          },
          {
            label: "lang.rms.fed.isPreDeployRobot",
            prop: "preDeployRobot",
            formatter: (row, column) => {
              return !row[column] ? this.$t("lang.rms.fed.no") : this.$t("lang.rms.fed.yes");
            },
          },
          { label: "lang.rms.fed.layout", prop: "layOut", width: "120" },
          { label: "lang.rms.fed.maxQueueNumber", prop: "maxRobotQueueSize" },
          {
            label: "lang.rms.fed.ongoingRobot",
            prop: "deliverRobotIds",
            formatter: (row, column) => {
              if ($utils.Type.isArray(row[column])) {
                return row[column].join(", ");
              } else {
                return row[column];
              }
            },
          },
          { label: "lang.rms.fed.hostCode", prop: "hostCode" },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            fixed: true,
            operations: [
              {
                label: "lang.rms.fed.buttonEdit",
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.cacheShelfSet",
                handler: "row-cache-shelf",
              },
            ],
          },
        ],
      },

      expandTableConfig: {
        columns: [
          {
            label: "lang.rms.web.station.parkId",
            width: "100",
            prop: "parkId",
          },
          {
            label: "lang.rms.fed.status",
            width: "90",
            prop: "isWorking",
            slotName: "isWorking",
            align: "center",
          },
          { label: "lang.rms.fed.type", prop: "parkType" },
          {
            label: "lang.rms.web.station.cellCode",
            width: "120",
            prop: "cellCode",
          },
          {
            label: "lang.rms.fed.WorkStationFace",
            width: "100",
            prop: "place",
            formatter: (row, column) => {
              switch (row[column]) {
                case "east":
                  return this.$t("lang.rms.fed.east");
                case "south":
                  return this.$t("lang.rms.fed.south");
                case "west":
                  return this.$t("lang.rms.fed.west");
                case "north":
                  return this.$t("lang.rms.fed.north");
                default:
                  return "";
              }
            },
          },
          {
            label: "lang.rms.fed.maxQueueNumber",
            width: "120",
            prop: "maxQueueSize",
          },
          {
            label: "lang.rms.fed.waitingReturnBox",
            prop: "waitingReturnBox",
            formatter: (row, column) => {
              return row[column] ? this.$t("lang.rms.fed.yes") : this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.fed.moveToReturnLattice",
            prop: "moveReturnLattice",
            formatter: (row, column) => {
              return row[column] ? this.$t("lang.rms.fed.yes") : this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.fed.moveToFetchLattice",
            prop: "moveFetchLattice",
            formatter: (row, column) => {
              return row[column] ? this.$t("lang.rms.fed.yes") : this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.fed.deviceMsg",
            prop: "showDeviceMsg",
            width: "100",
            slotName: "showDeviceMsg",
            align: "center",
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            operations: [
              {
                label: "lang.rms.fed.buttonEdit",
                handler: "expand-row-edit",
              },
            ],
          },
        ],
      },

      stationIds: [],

      dialogInitRow: {},
      dialogRackVisible: false,
      curDeviceJson: null,
    };
  },
  activated() {
    this.getStationType();
    this.getRobotType();
    this.getTableList();
  },
  methods: {
    rowEdit(row) {
      switch (row.type) {
        case 1:
        case 99:
          this.$refs.editSingleStationDialog.open(row);
          break;
        default:
          this.$refs.editStationDialog.open(row);
          break;
      }
    },
    expandRowEdit(expandRow, row) {
      this.$refs.editParkDialog.open(expandRow, row?.stationId || "");
    },
    cacheShelfSet(row) {
      this.dialogRackVisible = true;
      this.dialogInitRow = row;
    },
    changeStationStatus(status) {
      const stationIds = this.stationIds;
      if (!stationIds.length) return;
      if (status == 1) {
        $req.post("/athena/station/enable", stationIds).then(res => {
          this.$success();
          this.getTableList();
        });
      } else {
        $req.post("/athena/station/disable", stationIds).then(res => {
          this.$success();
          this.getTableList();
        });
      }
    },
    handleSelection(selections) {
      this.stationIds = selections.map(item => item.stationId);
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },

    getTableList() {
      const data = {
        ...this.form,
        pageSize: this.tablePage.pageSize,
        currentPage: this.tablePage.currentPage,
      };

      $req.get("/athena/station/stationPageList", data).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList;

        let stationPointsIndex = this.tableData.findIndex(
          item => item.hasOwnProperty("stationPoints") && item.stationPoints,
        );

        if (stationPointsIndex == -1) {
          this.tableConfig.expand = null;
        } else if (!this.tableConfig.expand) {
          this.tableConfig.expand = { slotName: "rowExpand" };
        }

        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          total: result.recordCount || 0,
        });
      });
    },
    getStationType() {
      $req.get("/athena/station/stationType").then(res => {
        const data = res?.data || {};
        let stationTypes = [];
        for (let key in data) {
          stationTypes.push({ label: data[key], value: key });
        }
        this.formConfig.configs.type.options = stationTypes;
      });
    },

    getRobotType() {
      $req.post("/athena/map/version/findRobotType").then(res => {
        const data = res?.data || [];
        this.robotTypes = data.map(item => {
          return { label: item.displayName, value: item.displayName };
        });
      });
    },
    expandDeviceMsg(row) {
      $req
        .post("/athena/engine/dms/ppsnapshot", {
          parkId: row.parkId,
        })
        .then(res => {
          const data = res?.data || [];
          this.curDeviceJson = this.formatterJson(data);
        });
    },
    hideDeviceMsg() {
      this.curDeviceJson = "";
    },
    formatterJson(data) {
      let obj = data;
      try {
        // 防止不标准格式JSON
        obj = JSON.parse(data);
      } catch (e) {
        console.log(e);
      }
      return Array.isArray(obj) ? data : JSON.stringify(obj, null, 2);
    },
  },
};
</script>
<style lang="less" scoped>
.code-show-box {
  max-height: 300px;
  overflow: scroll;
}
</style>
