<template>
  <div class="fn-con">
    <div
      class="fn-item"
      :class="fnActiveClass(item)"
      v-for="(item, index) in fnList"
      :key="index"
      @click="clickFn(item)"
    >
      <gp-tooltip effect="dark" :content="$t(item.label)" placement="bottom">
        <i class="monitorMap" :class="'monitor-font-' + item.name"></i>
      </gp-tooltip>
    </div>
  </div>
</template>

<script>
export default {
  name: "ToolBar",
  data() {
    return {
      fnList: [
        {
          label: "lang.rms.fed.reset",
          name: "map-reset",
        },
        {
          label: "lang.rms.fed.zoomOut",
          name: "zoom-out",
        },
        {
          label: "lang.rms.fed.zoomIn",
          name: "zoom-in",
        },
        {
          label: "lang.rms.fed.showUnloadRoadDirect",
          name: "show-unload-road-direct",
          isActive: false,
        },
        {
          label: "lang.rms.fed.showLoadRoadDirect",
          name: "show-load-road-direct",
          isActive: false,
        },
      ],
    };
  },
  computed: {
    fnActiveClass() {
      return item => {
        const { isActive } = item;
        if (isActive === undefined) return "";
        return isActive ? "fn-item-active" : "";
      };
    },
  },
  mounted() {},
  methods: {
    clickFn(item) {
      if (item.hasOwnProperty("isActive")) item.isActive = !item.isActive;
      this.$emit("clickFn", item);
    },
  },
};
</script>

<style scoped lang="less">
@import "./icon/iconfont.css";
.fn-con {
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  justify-content: flex-end;
  align-items: center;
  .fn-item-active {
    background-color: #bfddf6;
  }
  .fn-item {
    width: 30px;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    //border-left: 1px;
    //border-right: 1px;
    i {
      padding: 0 5px;
      cursor: pointer;
      font-size: 20px;
      color: #4693e8;
    }
  }
}
</style>
