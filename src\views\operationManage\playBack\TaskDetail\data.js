import i18n from "@lang";

export const traces = [
  {
      "traceTopic": "lang.rms.task.monitor.group.create",
      "resultDesc": "lang.rms.task.monitor.group.create",
      "resultDescValues": [],
      "resultDetail": "",
      "traceTime": 0,
      "costTime": 0,
      "childTopics": [
          {
              "traceTopic": "lang.rms.task.monitor.createJob.buildJob",
              "resultDesc": "lang.rms.task.monitor.createJob.buildJobDesc",
              "resultDescValues": [
                  "465042"
              ],
              "resultDetail": "",
              "traceTime": 1677206502183,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          }
      ],
      "expand": null,
      "isSearch": false,
      "logDownloadUrl": null,
      "isGroup": true
  },
  {
      "traceTopic": "lang.rms.task.monitor.group.allocate",
      "resultDesc": "lang.rms.task.monitor.group.allocate",
      "resultDescValues": [],
      "resultDetail": "",
      "traceTime": 0,
      "costTime": 0,
      "childTopics": [
          {
              "traceTopic": "lang.rms.task.monitor.allocateJob.addJob",
              "resultDesc": "lang.rms.task.monitor.allocateJob.addJobDesc",
              "resultDescValues": [
                  "100190",
                  "465042",
                  "GO_SOMEWHERE_TO_STAY",
                  "GO_NEXT"
              ],
              "resultDetail": "robotId:100190, location:[z=1, x=203.92, y=20.241], jobId:465042, jobType:GO_SOMEWHERE_TO_STAY, stageType:GO_NEXT, jobIds:[465042]",
              "traceTime": 1677206502183,
              "costTime": 1,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          }
      ],
      "expand": null,
      "isSearch": false,
      "logDownloadUrl": null,
      "isGroup": true
  },
  {
      "traceTopic": "lang.rms.task.monitor.group.execute",
      "resultDesc": "lang.rms.task.monitor.group.execute",
      "resultDescValues": [],
      "resultDetail": "",
      "traceTime": 0,
      "costTime": 0,
      "childTopics": [
          {
              "traceTopic": "lang.rms.task.monitor.createTask.buildTask",
              "resultDesc": "lang.rms.task.monitor.createTask.buildTaskDesc",
              "resultDescValues": [
                  "1723131",
                  "GO_SOMEWHERE_TO_STAY",
                  "GO_NEXT"
              ],
              "resultDetail": "robotId:100190, jobId:465042, taskId:1723131",
              "traceTime": 1677206502185,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.createTask.addTask",
              "resultDesc": "lang.rms.task.monitor.createTask.addTaskDesc",
              "resultDescValues": [
                  "100190",
                  "1723131",
                  "GO_SOMEWHERE_TO_STAY",
                  "GO_NEXT"
              ],
              "resultDetail": "robotId:100190, location:[z=1, x=203.92, y=20.241], taskId:1723131, taskType:GO_SOMEWHERE_TO_STAY, taskAction:GO_NEXT, taskIds:[1723131]",
              "traceTime": 1677206502185,
              "costTime": 1,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.createTask.buildExeTask",
              "resultDesc": "lang.rms.task.monitor.createTask.buildExeTaskDesc",
              "resultDescValues": [
                  "1999970",
                  "NULL"
              ],
              "resultDetail": "robotId:100190, taskId:1723131, exeTaskId:1999970",
              "traceTime": 1677206502303,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.composeTask.addExeTask",
              "resultDesc": "lang.rms.task.monitor.composeTask.addExeTaskDesc",
              "resultDescValues": [
                  "1999970",
                  "GO_SOMEWHERE"
              ],
              "resultDetail": "robotId:100190, taskId:1723131, exeTaskId:1999970, instruction:GO_SOMEWHERE, endPoints:[[z=1, x=199.87, y=24.291]], exeTaskIds:[1999970]",
              "traceTime": 1677206502303,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeTask.sendInstruction",
              "resultDesc": "lang.rms.task.monitor.executeTask.sendInstructionDesc",
              "resultDescValues": [
                  "1999970",
                  "GO_SOMEWHERE"
              ],
              "resultDetail": "send task. robotId:100190, location:[z=1, x=203.92, y=20.241], exeTaskId:1999970, instruction:GO_SOMEWHERE, commCount:9, receiveCommCount:8",
              "traceTime": 1677206502610,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeTask.taskInstructionHandshake",
              "resultDesc": "lang.rms.task.monitor.executeTask.taskInstructionHandshakeDesc",
              "resultDescValues": [
                  "1999970",
                  "GO_SOMEWHERE",
                  "152"
              ],
              "resultDetail": "robot:100190 received task, instruction:GO_SOMEWHERE, cost time:152, commCount:9, receivedCommCount:9",
              "traceTime": 1677206502763,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeTask.callback",
              "resultDesc": "lang.rms.task.monitor.executeTask.callbackDesc",
              "resultDescValues": [
                  "465042",
                  "1723131",
                  "GO_NEXT",
                  "MOVING"
              ],
              "resultDetail": "{\"warehouseCode\":\"DEFAULT\",\"jobId\":465042,\"parentTaskId\":0,\"taskId\":1723131,\"taskType\":\"GO_SOMEWHERE_TO_STAY\",\"taskAction\":\"GO_NEXT\",\"robotId\":100190,\"taskStatus\":\"EXECUTING\",\"instruction\":\"GO_SOMEWHERE\",\"taskPhase\":\"MOVING\",\"destCellCode\":\"24851175\",\"waitDir\":-1,\"startTime\":1677206502185,\"doneTime\":0,\"extProperties\":{},\"lastPath\":true}resp:{\"code\":0,\"msg\":\"success\",\"success\":true}",
              "traceTime": 1677206503671,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeTask.completeExeTask",
              "resultDesc": "lang.rms.task.monitor.executeTask.completeExeTaskDesc",
              "resultDescValues": [
                  "1999970",
                  "GO_SOMEWHERE"
              ],
              "resultDetail": "taskId:1723131, exeTaskId:1999970, costTime:7411",
              "traceTime": 1677206509596,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeTask.callback",
              "resultDesc": "lang.rms.task.monitor.executeTask.callbackDesc",
              "resultDescValues": [
                  "465042",
                  "1723131",
                  "GO_NEXT",
                  "ARRIVED"
              ],
              "resultDetail": "{\"warehouseCode\":\"DEFAULT\",\"jobId\":465042,\"parentTaskId\":0,\"taskId\":1723131,\"taskType\":\"GO_SOMEWHERE_TO_STAY\",\"taskAction\":\"GO_NEXT\",\"robotId\":100190,\"taskStatus\":\"COMPLETED\",\"instruction\":\"GO_SOMEWHERE\",\"taskPhase\":\"ARRIVED\",\"destCellCode\":\"24851175\",\"waitDir\":-1,\"startTime\":1677206502185,\"doneTime\":1677206509597,\"extProperties\":{},\"lastPath\":true}resp:{\"code\":0,\"msg\":\"success\",\"success\":true}",
              "traceTime": 1677206509597,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeJob.finishJob",
              "resultDesc": "lang.rms.task.monitor.executeJob.finishJobDesc",
              "resultDescValues": [],
              "resultDetail": "jobId:465042, jobType:GO_SOMEWHERE_TO_STAY, robotId:100190, costTime:7467",
              "traceTime": 1677206509652,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.createTask.callback",
              "resultDesc": "lang.rms.task.monitor.createTask.callbackDesc",
              "resultDescValues": [
                  "465042",
                  "GO_SOMEWHERE_TO_STAY",
                  "GO_NEXT"
              ],
              "resultDetail": "",
              "traceTime": 1677206509652,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeJob.completeJob",
              "resultDesc": "lang.rms.task.monitor.executeJob.completeJobDesc",
              "resultDescValues": [
                  "465042",
                  "GO_SOMEWHERE_TO_STAY",
                  "GO_NEXT"
              ],
              "resultDetail": "jobId:465042, costTime:7470",
              "traceTime": 1677206509653,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          },
          {
              "traceTopic": "lang.rms.task.monitor.executeJob.removeJobFromPool",
              "resultDesc": "lang.rms.task.monitor.executeJob.removeJobFromPoolDesc",
              "resultDescValues": [],
              "resultDetail": "jobId:465042",
              "traceTime": 1677206509653,
              "costTime": 0,
              "childTopics": [],
              "expand": null,
              "isSearch": true,
              "logDownloadUrl": null,
              "isGroup": false
          }
      ],
      "expand": null,
      "isSearch": false,
      "logDownloadUrl": null,
      "isGroup": true
  }
]

/**
 * 解析数据，生成树形结构数据
 * @param {*} data 
 * @param {*} treeDataList 
 * @param {*} layer 
 * @returns 
 * 
 */
export const parseFilterItems = (data, treeDataList = [], layer = 0) => {
  if (!data) return

  const listData = [];

  data.forEach(item => {
    const value = `${layer}__${item.traceTopic}`;
    const label = i18n.t(item.traceTopic);
    let dataItem = listData.find(item => item.value === value);

    if (!dataItem && item.isSearch) {
      dataItem = { value, label };
      listData.push(dataItem);
    }
    
    if (item.childTopics && item.isSearch && item.childTopics.length > 0) {
      dataItem.children = [];
      parseFilterItems(item.childTopics, dataItem.children, layer + 1)
    }
  })

  if (listData.length) {
      treeDataList.push(...listData);
  }

  return treeDataList
}

/**
 * 
 * @param {*} treeData 需要筛选的数据
 * @param {*} filterOptions 筛选项目
 * @param {*} isNotFilter 是否默认无筛选项(这里是true的话会去判断筛选数据中的hidden数据)
 * @returns tree数据
 */
export const parseFilterData = (treeData, filterOptions, isNotFilter) => {
    const curLayerFilterList = [];
    const nextLayerFilterList = [];

    filterOptions.forEach(item => {
        if (!item || item.length === 0) return;
        const [curLayer, ...nextLayers] = item;
        curLayerFilterList.push(curLayer.replace(/^\d+__/, ''));
        nextLayerFilterList.push(nextLayers);
    });

    const list = (treeData || []).map(item => {
        const option = { ...item };
        item.childTopics && item.childTopics.length && (option.childTopics = parseFilterData(item.childTopics, nextLayerFilterList, isNotFilter));
        return option;
    }).filter(item => {
        if (isNotFilter) {
            return !item.hidden;
        }

        return curLayerFilterList.includes(item.traceTopic);
    })

    return list;
}