/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { LockFilled, WarningFilled } from "@ant-design/icons";
import { Tooltip, Popover } from "antd";

type PropsOrderData = {
  stationData: stationData;
  setCurrentSelect: (data: any) => void;
};

function OrderStationCurrent(props: PropsOrderData) {
  const { t } = useTranslation();

  const [stationId, setStationId] = useState("");
  const [parkList, setParkList] = useState<Array<any>>([]);
  const [selectData, setSelectData] = useState<any>(null);
  const [errorKeys, setErrorKeys] = useState<any>({});

  // 处理 stationData
  useEffect(() => {
    setErrorKeys({});
    const parkList = props.stationData?.parkList || [];
    if (!parkList.length) {
      setStationId("");
      setParkList([]);
      setSelectData(null);
      props.setCurrentSelect(null);
      return;
    }

    let code: string = props.stationData?.stationId.toString() || "";

    if (code !== stationId) {
      setStationId(code);
      setSelectData(null);
      props.setCurrentSelect(null);
    }

    let list: Array<any> = [];
    let errorObject: any = {};
    for (let i = 0, len = parkList.length; i < len; i++) {
      let item = parkList[i];

      if (item.stationErrors?.length > 0) {
        item.stationErrors.forEach((error: any) => {
          errorObject[error.errorKey] = error.i18nDesc;
        });

        setErrorKeys(errorObject);
      }

      if (item.parkPosition === "LEFT") list.unshift(item);
      else list.push(item);
    }

    setParkList(list);
  }, [props.stationData]);

  // 点击货格列表
  const clickSelect = (lattice: any) => {
    setSelectData(lattice);
    let data = {
      ...lattice,
      latticeCodes: [lattice.latticeCode],
    };
    if (lattice.relateBox?.boxCode) {
      data.type = "box";
      data.boxCode = lattice.relateBoxCode;
    } else {
      data.type = "lattice";
      data.boxCode = "";
    }
    props.setCurrentSelect(data);
  };

  const getRackNode = (rack: any, isGripper: boolean) => {
    const rackLattices: Array<any> = rack?.lattices || [];

    let lattices: Array<any> = [];
    rackLattices.forEach(lattice => {
      let rowIndex = lattice.rowIndex;
      let colIndex = lattice.colIndex;
      if (!lattices[rowIndex]) lattices[rowIndex] = [];
      lattices[rowIndex][colIndex] = lattice;
    });

    const sumLayer = lattices.length;
    if (!sumLayer) return null;

    let nodes: any = [];
    let items: Array<any>, itemsNode: any;
    for (let i = sumLayer - 1; i >= 0; i--) {
      items = lattices[i];
      if (!items) continue;

      itemsNode = (
        <div key={i} className="poppick-list-item">
          {items.map((item: any, j: number) => {
            return !(errorKeys[item?.relateBoxCode] || errorKeys[item?.latticeCode]) ? (
              <span
                key={j}
                className={[
                  item ? "" : "no-data",
                  isGripper ? "item-gripper" : "",
                  item?.relateBoxCode ? "has-box" : "",
                  errorKeys[item?.relateBoxCode] || errorKeys[item.latticeCode] ? "has-error" : "",
                  selectData?.latticeCode === item.latticeCode ? "active" : "",
                  item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1 ? "locked" : "",
                  item.hasBcr || item.hasWeight ? "item-has-child-device" : "",
                ].join(" ")}
                title={item.latticeCode || ""}
                onClick={() => clickSelect(item)}
              >
                {isGripper && <i className="icon-gripper" />}
                <i className="icon-status">
                  {(item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1) && <LockFilled />}
                </i>
                {isGripper && rack.deviceState && rack.deviceState !== "ENABLE" && (
                  <Tooltip className="icon-warnning" title={rack.deviceState}>
                    <WarningFilled />
                  </Tooltip>
                )}
                {item.hasWeight && <i className="geek-icon-weight" />}
                {item.hasBcr && <i className="geek-icon-scanner" />}
                {item?.relateBoxCode ? item.relateBoxCode : item.latticeCode}
              </span>
            ) : (
              <Popover
                key={j}
                placement="top"
                title=""
                content={
                  <div style={{ color: "#ff4d4f" }}>
                    {errorKeys[item?.relateBoxCode] && <p>{t(errorKeys[item?.relateBoxCode])}</p>}
                    {errorKeys[item?.latticeCode] && <p>{t(errorKeys[item?.latticeCode])}</p>}
                  </div>
                }
                trigger="click"
              >
                <span
                  className={[
                    item ? "" : "no-data",
                    isGripper ? "item-gripper" : "",
                    item?.relateBoxCode ? "has-box" : "",
                    errorKeys[item?.relateBoxCode] || errorKeys[item.latticeCode] ? "has-error" : "",
                    selectData?.latticeCode === item.latticeCode ? "active" : "",
                    item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1 ? "locked" : "",
                    item.hasBcr || item.hasWeight ? "item-has-child-device" : "",
                  ].join(" ")}
                  title={item.latticeCode || ""}
                  onClick={() => clickSelect(item)}
                >
                  {isGripper && <i className="icon-gripper" />}
                  <i className="icon-status">
                    {(item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1) && <LockFilled />}
                  </i>
                  {isGripper && rack.deviceState && rack.deviceState !== "ENABLE" && (
                    <Tooltip className="icon-warnning" title={rack.deviceState}>
                      <WarningFilled />
                    </Tooltip>
                  )}
                  {item.hasWeight && <i className="geek-icon-weight" />}
                  {item.hasBcr && <i className="geek-icon-scanner" />}
                  {item?.relateBoxCode ? item.relateBoxCode : item.latticeCode}
                </span>
              </Popover>
            );
          })}
        </div>
      );
      nodes.push(itemsNode);
    }

    return nodes;
  };

  return (
    parkList.length && (
      <>
        <h1 style={{ padding: "10px 0 0", textAlign: "center", fontSize: 16 }}>
          {t("lang.rms.fed.stationId")}:<strong style={{ fontWeight: 600, paddingLeft: 5 }}>{stationId}</strong>
        </h1>
        {parkList.map((park: any, index: number) => {
          if (!park?.deviceCode) return null;
          return (
            <div key={index} style={{ marginTop: 16 }}>
              <h3 className="poppick-title">
                <span className="title-code">
                  {t("parkId")}:<strong>{park?.parkId || ""}</strong>
                </span>
                <span className="title-code">
                  <strong>{park?.busModel || ""}</strong>
                </span>
                <span className="title-code" style={{ paddingRight: 8 }}>
                  <strong>{park?.parkPosition || ""}</strong>
                  {park?.deviceCode && <strong>({park?.deviceCode || ""})</strong>}
                </span>
              </h3>

              {park.gripperRack && (
                <div className="map2d-poppick-list">
                  {getRackNode({ ...park.gripperRack, deviceState: park.deviceState }, true)}
                </div>
              )}
              {park.virtualRack && <div className="map2d-poppick-list">{getRackNode(park.virtualRack, false)}</div>}
            </div>
          );
        })}
      </>
    )
  );
}

export default OrderStationCurrent;
