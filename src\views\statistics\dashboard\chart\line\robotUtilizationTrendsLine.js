import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 机器人利用趋势
 */
export default class RobotUtilizationTrendsLine extends Chart {
  /**
   * 初始化图表 - 机器人平均充电成功率趋势
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人利用趋势";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
        valType: 'yyyy-MM-dd',
        option: {}
      }
    ]
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/robot/snapshot', {
      date: $utils.Tools.formatDate(params?.date || new Date(), "yyyy-MM-dd"),
      cycle : "5",
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData = [];
    const seriesRobotCount = [];
    const seriesRobotWorkingCount = [];
    const totalDataItem = data?.robotSnapshotList || {};
    totalDataItem.filter(item => {
      return item.haveData;
    }).forEach(item => {
      xAxisData.push($utils.Tools.formatDate(item.snapshotTime, "hh:mm:ss"));
      seriesRobotCount.push(item.robotCount);
      seriesRobotWorkingCount.push(item.robotWorkingCount);
    });
    const { tooltipFormatterToHtml } = this;
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      legend: {
        data: ['工作中机器人总数', '机器人总数']
      },
      grid: {
        left: 50,
        right: 20,
        bottom: 30,
        top: 50
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        formatter(params, ticket, callback) {
          const axisValue = params[0].axisValue;
          return tooltipFormatterToHtml(`时间: ${axisValue}`, params)
        },
      },
      series: [
        { data: seriesRobotWorkingCount, type: 'line', name: '工作中机器人总数', },
        { data: seriesRobotCount, type: 'line', name: '机器人总数', },
      ]
    })
  }
}