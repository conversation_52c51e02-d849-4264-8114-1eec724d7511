<template>
  <div class="contentModelItem">
    <gp-card class="box-card" shadow="never">
      <div class="cardMain">
        <div
          class="content"
          @click="$emit('handle')"
          @mouseenter="$emit('mouseenter')"
          @mouseleave="$emit('mouseleave')"
        >
          <canvas width="100%" height="100%" class="canvas" ref="canvasRef"></canvas>
        </div>
        <div class="floor">
          <span class="name">{{ $t(option.modelName) }}</span>
          <span class="type">{{ $t(modelCategoryType) }}</span>

          <template v-if="!option.used">
            <i class="icon-status"></i>
          </template>

          <template v-else>
            <gp-tooltip class="item" effect="dark" :content="$t('lang.rms.fed.modelUsedMsg')" placement="bottom">
              <i class="icon-status used"></i>
            </gp-tooltip>
          </template>

          <gp-popconfirm v-show="!option.used" :title="$t('lang.rms.fed.confirmDelete')" @confirm="$emit('delete')">
            <gp-icon name="gp-icon-delete" class="icon-delete" slot="reference"></gp-icon>
          </gp-popconfirm>
        </div>
      </div>
    </gp-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Viewport from "@/libs/viewport3d/viewport-3d";
import { getVpConfig, getComponentByTypeMap } from "../../../editView/vpConfig";

export default {
  props: {
    option: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      vpInstance: null,
    };
  },
  computed: {
    ...mapState("containerModal", ["containerModelCategoryDict"]),
    modelCategoryType() {
      const { modelCategory, extendJson } = this.option;
      const category = extendJson?.subCategory || modelCategory;
      return this.containerModelCategoryDict.find(item => item.value === category)?.label || category;
    },
  },
  watch: {
    // option() {
    //   debugger
    //   if (this.vpInstance) {
    //     this.vpInstance.destroy();
    //   }
    //   this.$nextTick(() => {
    //     const meshType = getComponentByTypeMap(this.option);
    //     this.vpInstance = new Viewport();
    //     this.vpInstance.init({ dom: this.$refs.canvasRef });
    //     this.vpInstance.initModel({ meshType, config: getVpConfig(meshType, this.option) || {} });
    //   })
    // }
  },
  mounted() {
    try {
      const meshType = getComponentByTypeMap(this.option);
      this.vpInstance = new Viewport();
      this.vpInstance.init({ dom: this.$refs.canvasRef });
      this.vpInstance.initModel({ meshType, config: getVpConfig(meshType, this.option) || {} });
    } catch (error) {}
  },
  beforeDestroy() {
    this.vpInstance && this.vpInstance.destroy();
  },
  // destroyed() {
  //   console.log('执行destroy了')
  //   this.vpInstance.destroy();
  // },
  methods: {},
};
</script>
<style lang="scss" scoped>
.box-card {
  width: 100%;
}

.cardMain {
  position: relative;
}

.floor {
  position: relative;
  height: 20px;
  font-size: 14px;
  display: flex;
  flex-direction: row;
  padding-right: 40px;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #fff;

  .name {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding-right: 10px;
  }

  .type {
    // width: 80px;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .icon-status,
  .icon-delete {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #f56c6c;
  }

  .icon-status {
    right: 20px;
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 12px;
    background-color: #909399;

    &.used {
      background-color: #67c23a;
    }
  }
}

.content {
  min-height: 140px;
  max-height: 280px;
  position: relative;
  .canvas {
    width: 100%;
    height: 100%;
  }
}

.contentModelItem {
  padding: 10px;
}
</style>
<style lang="scss">
.contentModelItem {
  .gp-card__body {
    padding: 5px;
    overflow: hidden;
    padding-bottom: 0;
  }
}
</style>
