<template>
  <div>
    <gp-form ref="searchForm" :inline="true" class="demo-form-inline" label-position="left" :model="searchForm">
      <gp-form-item :label="$t('PLC编号/设备编号')">
        <gp-input v-model="searchForm.boxCode" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.selectDate')">
        <gp-date-picker v-model="searchForm.date" type="date" :placeholder="$t('lang.rms.fed.selectDate')">
        </gp-date-picker>
      </gp-form-item>
      <gp-form-item class="align-bottom">
        <gp-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</gp-button>
        <gp-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</gp-button>
      </gp-form-item>
    </gp-form>

    <gp-table ref="selectStations" v-loading="loading" :data="stationList" style="width: 100%">
      <gp-table-column v-for="item in cloumnList" :key="item.prop" :prop="item.prop" :label="$t(item.label)" />
    </gp-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        date: "",
        boxCode: "",
      },
      loading: false,
      stationList: [],
    };
  },

  computed: {
    cloumnList() {
      return [
        // 业务任务ID
        { label: "业务任务ID", prop: "whJobId" },
        // 设备任务ID
        { label: "设备任务ID", prop: "whStageId" },
        // 容器编号
        { label: "lang.rms.fed.containerId", prop: "containerType" },
        // 设备编号
        { label: "设备编号", prop: "stageType" },
        // PLC编号
        { label: "PLC编号", prop: "stageStatus" },
        // 设备类型
        { label: "设备类型", prop: "jobStatus" },
        // 所属工作站
        { label: "所属工作站", prop: "stageReceivedTime" },
        // 起始点位
        { label: "起始点位", prop: "stageDoneTime" },
        // 目标点位
        { label: "目标点位", prop: "stationId" },
        // 指令类型
        { label: "lang.rms.web.monitor.robot.instruction", prop: "jobSourceType" },
        { label: "lang.rms.fed.createTime", prop: "startContainerLocation" },
        { label: "lang.venus.web.common.taskDetail", prop: "priority" },
        { label: "反馈结果", prop: "businessSequence" },
        { label: "任务耗时", prop: "businessSequence1" },
      ];
    },
  },
  async created() {
    await this.onSubmit();
  },

  methods: {
    resetForm() {
      this.searchForm = { date: "", boxCode: "" };
      this.onSubmit();
    },
    async onSubmit() {
      this.loading = true;
      const params = { ...this.searchForm };
      if (params.date) {
        params.date = $utils.Tools.formatDate(params.date, "yyyy-MM-dd");
      }
      const { data } = await $req.post("/athena/stats/query/job/select/box", this.searchForm, { intercept: false });
      // 目前是要展示所有的字段
      this.loading = false;
      this.stationList = data || [];
    },
  },
};
</script>

<style></style>
