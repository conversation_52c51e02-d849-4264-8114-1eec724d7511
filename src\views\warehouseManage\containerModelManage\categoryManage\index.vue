<template>
  <div class="containerLocation">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="btn-content mt10 mb10">
      <gp-button class="w100" type="primary" @click="handleAddShelfCategory">
        {{ $t("lang.rms.fed.newlyAdded") }}
      </gp-button>
    </div>
    <div class="table-content">
      <!-- <m-table
          :table-item="tableItem"
          :table-data="tableData"
          :page-data="pageData"
          :extend-config="tableExtendConfig"
          @view="handleView"
          @edit="handleEdit"
          @delete="handleDelete"
        ></m-table> -->

      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="pageData"
        @page-change="pageChange"
        @delete="handleDelete"
        @view="handleView"
        @edit="handleEdit"
        style="margin-top: 10px"
      >
        <!-- <template #rowExpand="{ row }">
        <geek-customize-table
          :table-config="expandTableConfig"
          :data="row.stationPoints"
          @expand-row-edit="expandRow => expandRowEdit(expandRow, row)"
        >
          <template #isWorking="{ row }">
            <gp-tag v-if="row.isWorking == 'true'" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</gp-tag>
            <gp-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</gp-tag>
          </template>
        </geek-customize-table>
      </template> -->
        <template #manageStatus="{ row }">
          <gp-tag v-if="row.manageStatus == 1" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</gp-tag>
          <gp-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</gp-tag>
        </template>
      </geek-customize-table>
    </div>
    <category-dialog
      v-if="dialogVisble"
      :mode="dialogMode"
      :visible.sync="dialogVisble"
      :init-row="dialogRow"
      :category-type-dict="categoryTypeDict"
      @saveSuccess="handleSaveSuccess"
    />
  </div>
</template>
<script>
import { getSearchFormItem, getSearchTableItem } from "./config";
import CategoryDialog from "./components/categoryDialog";
export default {
  components: { CategoryDialog },
  data() {
    return {
      formData: {},
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 10,
        recordCount: 0,
      },
      dialogVisble: false,
      dialogRow: {},
      categoryTypeDict: [],
    };
  },
  async created() {
    try {
      this.searchSync(this.formData);
      await this.fetchCategoryTypeDict();
    } catch (e) {}
  },
  computed: {
    formConfig() {
      return {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          categoryName: {
            label: "lang.rms.fed.shelfCategor.name.msg",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          categoryType: {
            label: "lang.rms.fed.shelfCategor.type.msg",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.choose",
            options: this.categoryTypeDict || [],
          },
          categoryNum: {
            label: "lang.rms.fed.shelfCategor.num.msg",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      };
    },

    tableConfig() {
      const { categoryTypeDict } = this;
      return {
        attrs: {
          selection: false,
          "row-key": "id",
          "reserve-selection": false,
          // "row-class-name": ({ row }) => {
          // if (!row?.stationPoints || !row.stationPoints.length) return "no-expand";
          // },
        },
        actions: [],
        columns: [
          { label: "lang.rms.fed.shelfCategor.id", prop: "id", width: "80" },
          { label: "lang.rms.fed.shelfCategor.num.msg", prop: "categoryNum" },
          { label: "lang.rms.fed.shelfCategor.name.msg", prop: "categoryName" },
          { label: "lang.rms.fed.shelfCategor.alias.msg", prop: "categoryAlias" },
          {
            label: "lang.rms.fed.shelfCategor.type.msg",
            prop: "categoryType",
            formatter(row, column) {
              return categoryTypeDict.find(i => String(i.value) === String(row[column]))?.label || row[column];
            },
          },
          { label: "lang.rms.fed.shelfCategor.layer.msg", prop: "layer" },
          { label: "lang.rms.fed.shelfCategor.desc.msg", prop: "categoryDesc" },
          {
            label: "lang.rms.fed.listOperation",
            width: "220",
            fixed: "right",
            align: "center",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "view",
              },
              {
                label: "lang.rms.fed.edit",
                handler: "edit",
              },
              {
                label: "lang.rms.fed.delete",
                handler: "delete",
                confirm: true,
                confirmMessage: "lang.rms.fed.confirmDelete",
              },
            ],
          },
        ],
      };
    },
  },
  methods: {
    async onQuery(val) {
      this.pageData.currentPage = 1;
      this.formData = Object.assign(this.formData, val);
      this.searchSync(this.formData);
    },
    onReset() {
      this.pageData.currentPage = 1;
      this.formData = {};
      this.searchSync(this.formData);
    },
    pageChange(page) {
      this.pageData = page;
      this.searchSync();
    },
    // searchSync
    async searchSync(params) {
      const { code, data } = await $req.post("/athena/shelfCategory/list", params);
      if (code) return;
      this.tableData = data?.recordList || [];
    },
    handleAddShelfCategory() {
      this.dialogMode = "add";
      this.dialogVisble = true;
      this.dialogRow = {};
    },
    handleView(row) {
      this.dialogMode = "view";
      this.dialogVisble = true;
      this.dialogRow = row;
    },
    handleEdit(row) {
      this.dialogMode = "edit";
      this.dialogVisble = true;
      this.dialogRow = row;
    },
    handleSaveSuccess() {
      this.searchSync(this.formData);
    },
    async handleDelete(row) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(async () => {
        const { code } = await $req.get("/athena/shelfCategory/del", { id: row.id });
        if (code) return;
        this.$message.success(this.$t("lang.common.success"));
        this.searchSync(this.formData);
      });
    },
    async fetchCategoryTypeDict() {
      const { code, data } = await $req.get("/athena/shelfCategory/select");
      if (code) return;
      this.categoryTypeDict = Object.keys(data).map(key => ({
        label: this.$t(key),
        value: data[key],
      }));
      return Promise.resolve();
    },
  },
};
</script>
<style lang="less" scoped>
.form-content {
  padding-bottom: 10px;
  border-bottom: 5px solid #eee;
}

.table-content {
  padding-top: 10px;
}
</style>
