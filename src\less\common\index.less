@charset "utf-8";
@import "./_reset-html.less";
@import "./_reset-element.less";
@import "./_reset-form-item.less";

.clear {
  zoom: 1;

  &:before,
  &:after {
    display: table;
    line-height: 0;
    content: "";
  }

  &:after {
    clear: both;
  }
}

.hide {
  display: none !important;
}

.fl {
  float: left !important;
}

.fr {
  float: right !important;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-thumb {
  background-color: #e5e7ea;
  border-radius: 3px;
}

::selection {
  color: #fff;
  background: #1890ff;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
[class*="rms-col-"] {
  box-sizing: border-box;
  float: left;
}
