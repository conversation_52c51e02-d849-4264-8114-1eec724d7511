<template>
  <gp-form :model="ruleForm" :rules="rules" ref="ruleForm">
    <gp-form-item label="名称" prop="name">
      <gp-input v-model="ruleForm.name"></gp-input>
    </gp-form-item>
    <gp-form-item label="类型" prop="type">
      <gp-input v-model="ruleForm.type"></gp-input>
    </gp-form-item>
    <gp-form-item label="时间段" prop="range">
      <gp-input v-model="ruleForm.range"></gp-input>
    </gp-form-item>
    <gp-form-item class="btn-con">
      <gp-button type="primary" @click="ok">确定</gp-button>
      <gp-button @click="cancel">取消</gp-button>
    </gp-form-item>
  </gp-form>
</template>

<script>
export default {
  name: "CreatePlayBackDialog",
  data() {
    return {
      ruleForm: {
        name: "",
        type: "",
        range: "",
      },
      rules: {
        name: { required: true, message: "请输入", trigger: "change" },
        type: { required: true, message: "请输入", trigger: "change" },
        range: { required: true, message: "请输入", trigger: "change" },
      },
    };
  },
  methods: {
    ok() {
      this.$emit("confirm");
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped lang="less">
.input-style {
  width: 200px;
}
</style>
