<template>
  <div class="iframeDiv">
    <div class="selectBox">
      <!-- <gp-select
        v-model="selectValue"
        :popper-append-to-body="true"
        class="selSerTypeSty"
        size="mini"
      >
        <gp-option
          v-for="item in searchOptions"
          :key="item.value"
          :label="$t(item.label)"
          :value="item.value"
        />
      </gp-select> -->
      <gp-input
        v-model.trim="searchVal"
        :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.fed.configs.search.param')}`"
        class="searchIpt"
        @keyup.enter.native="onEnter"
      />
      <!-- <img src="../../../imgs/parameterConfig/search.png" class="searchImg" /> -->
      <div class="btns">
        <gp-button type="primary" @click="onSearch"> {{ $t("lang.rms.fed.query") }}</gp-button>
        <gp-button @click="reset"> {{ $t("lang.rms.fed.reset") }}</gp-button>
      </div>
    </div>
    <div class="line">
      <restartAlert v-if="alertIsShow" @parameterIsShow="parameterIsShow"></restartAlert>
    </div>
    <div :class="[{ 'new-content-box': alertIsShow }, 'content-box']" v-loading="initLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
      <gp-tabs v-model="activeName" tab-position="left" :before-leave="beforeLeave">
        <gp-tab-pane v-for="item in modules" :key="item.id" :label="$t(item.label)" :name="item.code"></gp-tab-pane>
      </gp-tabs>
      <parameter-content
        ref="parameterContent"
        :show="show"
        :isApplySuccess="isApplySuccess"
        :alert-tip-show="alertIsShow"
        :current-name="activeName"
        :modules="tabModules"
        :modules-list="getModules"
        :limitShowObj="limitShowObj"
        @changeData="change"
        @alertShow="alertShow"
        @applySuccess="applySuccess"
        @changeModulesPath="changeModulesPath"
      />
    </div>
    <dialogParamsChange v-if="dialog" @apply="apply" @close="close" @giveUp="giveUp"></dialogParamsChange>
  </div>
</template>

<script>
import {
  searchOptions,
  // modules,
  // mapModules,
  // taskModules,
  // pathModules,
  // systemModules,
  // frontEndModules,
} from "./config";
import parameterContent from "./components/parameterContent";
import restartAlert from "./components/restartAlert";
import dialogParamsChange from "./components/dialogParamsChange";

export default {
  components: {
    parameterContent,
    restartAlert,
    dialogParamsChange,
  },
  props: {
    update: {
      type: Boolean,
    },
    // list: {
    //   type: Object,
    //   default() {
    //     return {};
    //   },
    // },
    // paramsConfigList: {
    //   type: Array,
    //   default() {
    //     return [];
    //   },
    // },
  },
  data() {
    return {
      shelfWeekObj: {
        Mon: "lang.configs.weekday.Mon",
        Tues: "lang.configs.weekday.Tues",
        Wed: "lang.configs.weekday.Wed",
        Thur: "lang.configs.weekday.Thur",
        Fri: "lang.configs.weekday.Fri",
        Sat: "lang.configs.weekday.Sat",
        Sun: "lang.configs.weekday.Sun"
      },
      initLoading: false,
      isView: false,
      isApplySuccess: false,
      limitShowObj: {},
      limitKeyObj: {},
      parameterList: {},
      paramsConfigList: [],
      paramsObj: {},
      language: "",
      iframeUrl: "",
      isCollapse: true,
      selectValue: "",
      searchVal: "",
      searchOptions,
      modules: [],
      mapModules: [],
      taskModules: [],
      pathModules: [],
      systemModules: [],
      frontEndModules: [],
      tabModules: [],
      params: {},
      showStatus: true,
      activeName: "",
      getModules: {},
      tabIndex: "0",
      dialog: false,
      changeData: false,
      show: true,
      alertIsShow: false,
      nextTabIndex: -1,
    };
  },
  watch: {
    update() {
      this.searchVal = "";
      this.getConfigItems();
    },
    activeName(val) {
      console.log("activeName=======",val)
      switch (val) {
        case "map":
          this.tabModules = this.mapModules;
          this.getModules = this.parameterList.map;
          break;
        case "path":
          this.tabModules = this.pathModules;
          this.getModules = this.parameterList.path;
          break;
        case "task":
          this.tabModules = this.taskModules;
          this.getModules = this.parameterList.task;
          break;
        case "system":
          this.tabModules = this.systemModules;
          this.getModules = this.parameterList.system;
          break;
        case "fesystem":
          this.tabModules = this.frontEndModules;
          this.getModules = this.parameterList.fesystem;
          break;
      }
    },
  },
  activated() {
    // http://dev-5259.local.k8s.ops.geekplus.cc
    // this.iframeUrl = "/athena/configs/configs.html?lang=" + $utils.Data.getLocalLang();
  },
  async created() {
    // 默认选择
    // this.selectValue = this.searchOptions[0].value;
    // this.tabModules = this.mapModules;
    // this.getModules = this.list.map;
    this.initLoading = true;
    await this.getConfigItems();
    // this.getModules = this.parameterList.map;
    // this.tabModules = this.mapModules;
  },
  methods: {
    onEnter() {
      this.onSearch();
    },
    async getConfigItems(requestType) {
      const res = await $req.get("/athena/configs/configItems", {}, { intercept: false });
      this.initLoading = false;
      const reg = new RegExp("/", "g");
      this.limitKeyObj = {};
      let firstPathArr = ["map", "path", "task", "system", "fesystem"];
      res.forEach(item => {
        // item.value = item.value === '' ? null : item.value
        item.isShow = true;
        item.errorTipShow = false;
        item.errorTipText = "";
        item.oldPath = item.path;

        if (item.path) {
          const pathArr = item.path.split("/");
          pathArr.shift();
          let firstKey = pathArr[0];
          if (!firstPathArr.includes(firstKey)) {
            item.path = "/system/rms";
            item.oldPath = "";
          }
          if (firstPathArr.includes(firstKey) && pathArr[2]) {
            item.path = `/${pathArr[0]}/${pathArr[1]}`;
            item.section = pathArr[2];
          }
          if (firstPathArr.includes(firstKey) && pathArr[2]) {
            item.path = `/${pathArr[0]}/${pathArr[1]}`;
            item.section = pathArr[2];
          }
        }
        item.path = item.path || "/system/rms";
        item.widgetType = item.widgetType || "text";
        let label = `lang.configs.${item.code}.label`;
        if (this.$t(label) === label) {
          label = item.code;
        }
        item.paramsName = label;
        item.sectionLabel = null;
        if (item.oldPath && item.section) {
          const newPath = item.path.replace(reg, ".");
          item.sectionLabel = `lang.configs${newPath}.${item.section}.label`;
        }

        if (item.code) {
          const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
          if (restrictions && restrictions.enabled) {
            item["limitKey"] = {};
            let limitArr = restrictions.enabled.split("&&");
            limitArr.forEach(element => {
              let cItemArr = element.split("==");
              let val = cItemArr[1].replace(/\'/g, "");
              this.limitKeyObj[cItemArr[0]] = val;
              item["limitKey"][cItemArr[0]] = val;
            });
            // let val = limitArr[1].replace(/\'/g, "");
            // this.limitKeyObj[limitArr[0]] = val;
            // item["limitKey"] = {
            //   [limitArr[0]]: val,
            // };
            item.showCondition = restrictions.enabled;
          }
        }
        if (item.immediate == false && item.value !== item.currentUpdateValue && item.currentUpdateValue !== null) {
          this.alertIsShow = true;
        }
      });
      res.forEach(item => {
        if (this.limitKeyObj[item.code]) {
          // let currentUpdateValue =
          //   item.currentUpdateValue != null ? item.currentUpdateValue : item.value;
          this.limitShowObj[item.code] = item.immediate ? item.value : item.currentUpdateValue;
        }
      });
      let newRes = res.filter(item => item.code !== "charging.forbidTime.forbidTimeRange");
      if (this.searchVal) {
        newRes = newRes.filter(item => {
          let paramsName = this.$t(item.paramsName);
          if (paramsName.includes(this.searchVal) || item.code.toLowerCase().includes(this.searchVal.toLowerCase())) {
            return item;
          }
        });
      }

      // 是否点了非立即生效参数查看按钮
      if (this.isView === true) {
        newRes = newRes.filter(item => {
          if (item.immediate == false && item.value !== item.currentUpdateValue && item.currentUpdateValue !== null) {
            return item;
          }
        });
      }
      this.paramsConfigList = newRes;
      const obj = this.jsonFormat(newRes, requestType);
      this.parameterList = Object.assign({}, obj);
      console.log("111111",this.parameterList, this.paramsConfigList);
    },
    jsonFormat(data, requestType) {
      // console.log(this.limitShowObj, 12314);
      const dataObj = {};
      let mapObj = {};
      let taskObj = {};
      let pathObj = {};
      let systemObj = {};
      let fesystemObj = {};
      // 二级菜单
      let mapModulesObj = {};
      let taskModulesObj = {};
      let pathModulesObj = {};
      let systemModulesObj = {};
      let frontEndModulesObj = {};
      this.mapModules = [];
      this.taskModules = [];
      this.pathModules = [];
      this.systemModules = [];
      this.frontEndModules = [];
      data.forEach(item => {
        const path = item.path;
        if (path) {
          const pathArr = path.split("/");
          pathArr.shift();
          let firstKey = pathArr[0];
          let secondKey = pathArr[1];
          dataObj[firstKey] = null;
          let childObj = {};
          if (item.code) {
            item.errorTipShow = false;
            const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
            // console.log(item.id)
            if (item.limitKey) {
              const keysArr = Object.keys(item.limitKey);
              let isShow = true;
              console.log(this.limitShowObj, item.limitKey);
              keysArr.forEach(element => {
                if (this.limitShowObj[element] != item.limitKey[element]) {
                  isShow = false;
                }
              });
              item.isShow = isShow;
            }
            let currentUpdateValue = item.currentUpdateValue != null ? item.currentUpdateValue : item.value;

            let val = item.value;
            if (item.widgetType === "timeslot" && val) {
              val = JSON.parse(val);
            }

            if (item.widgetType === "multi-select") {
              val = val ? val.split(",") : [];
            }

            let selectList = ["multi-select", "select"].includes(item.widgetType) && restrictions ? restrictions["options"] : [];
            if (item.code === "shelf.dynamicAdjustment.week.days") {
              selectList = selectList.map(item => {
                return {
                  label: this.shelfWeekObj[item],
                  value: item
                }
              })
            }

            childObj[item.code] = {
              ...item,
              i18nCode: item.code,
              label: item.paramsName,
              type: item.widgetType,
              options: {
                componentName: item.widgetType,
                selectList,
                defVal: val,
              },
              restrictions,
            };
            if (item.validValueRange) {
              let limitArr = item.validValueRange.split("~");
              childObj[item.code].options.limitMax = limitArr[1];
              childObj[item.code].options.limitMin = limitArr[0];
            }
          }
          switch (firstKey) {
            case "map":
              mapObj[secondKey] = Object.assign({}, mapObj[secondKey], childObj);
              mapModulesObj[item.path] = item.path;
              break;
            case "task":
              taskObj[secondKey] = Object.assign({}, taskObj[secondKey], childObj);
              taskModulesObj[item.path] = item.path;
              break;
            case "path":
              pathObj[secondKey] = Object.assign({}, pathObj[secondKey], childObj);
              pathModulesObj[item.path] = item.path;
              break;
            case "system":
              systemObj[secondKey] = Object.assign({}, systemObj[secondKey], childObj);
              systemModulesObj[item.path] = item.path;
              break;
            case "fesystem":
              fesystemObj[secondKey] = Object.assign({}, fesystemObj[secondKey], childObj);
              frontEndModulesObj[item.path] = item.path;
              break;
          }
        }
      });
      const obj = {
        // map: mapObj,
        // task: taskObj,
        // path: pathObj,
        // system: systemObj,
        // fesystem: fesystemObj,
      };
      if (this.isNull(mapObj)) {
        for (let key in mapObj) {
          const sections = this.groupBy(Object.values(mapObj[key]), "section");
          mapObj[key] = sections;
        }
        obj.map = mapObj;
      }
      if (this.isNull(pathObj)) {
        for (let key in pathObj) {
          const sections = this.groupBy(Object.values(pathObj[key]), "section");
          pathObj[key] = sections;
        }
        obj.path = pathObj;
      }
      if (this.isNull(taskObj)) {
        for (let key in taskObj) {
          const sections = this.groupBy(Object.values(taskObj[key]), "section");
          taskObj[key] = sections;
        }
        obj.task = taskObj;
      }
      if (this.isNull(systemObj)) {
        for (let key in systemObj) {
          const sections = this.groupBy(Object.values(systemObj[key]), "section");
          systemObj[key] = sections;
        }
        obj.system = systemObj;
      }
      if (this.isNull(fesystemObj)) {
        for (let key in fesystemObj) {
          const sections = this.groupBy(Object.values(fesystemObj[key]), "section");
          fesystemObj[key] = sections;
        }
        obj.fesystem = fesystemObj;
      }
      this.modules = [];
      for (let key in dataObj) {
        switch (key) {
          case "map":
            this.modules.push({ label: `lang.configs.${key}.label`, id: 1, code: "map" });
            break;
          case "path":
            this.modules.push({ label: `lang.configs.${key}.label`, id: 2, code: "path" });
            break;
          case "task":
            this.modules.push({ label: `lang.configs.${key}.label`, id: 3, code: "task" });
            break;
          case "system":
            this.modules.push({ label: `lang.configs.${key}.label`, id: 4, code: "system" });
            break;
          case "fesystem":
            this.modules.push({ label: `lang.configs.${key}.label`, id: 5, code: "fesystem" });
            break;
        }
      }
      for (let key in mapModulesObj) {
        switch (key) {
          case "/map/resolver":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/resolver"),
              id: 1,
              path: "/map/resolver",
            });
            break;
          case "/map/base":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/base"),
              id: 2,
              path: "/map/base",
            });
            break;
          case "/map/station":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/station"),
              id: 3,
              path: "/map/station",
            });
            break;
          case "/map/area":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/area"),
              id: 4,
              path: "/map/area",
            });
            break;
        }
      }
      for (let key in taskModulesObj) {
        switch (key) {
          case "/task/shelfTask":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/shelfTask"),
              id: 1,
              path: "/task/shelfTask",
            });
            break;
          case "/task/box":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/box"),
              id: 2,
              path: "/task/box",
            });
            break;
          case "/task/rsp":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/rsp"),
              id: 3,
              path: "/task/rsp",
            });
            break;
          case "/task/sorting":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/sorting"),
              id: 4,
              path: "/task/sorting",
            });
            break;
          case "/task/charging":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/charging"),
              id: 5,
              path: "/task/charging",
            });
            break;
          case "/task/GoRest":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/GoRest"),
              id: 6,
              path: "/task/GoRest",
            });
            break;
          case "/task/click":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/click"),
              id: 7,
              path: "/task/click",
            });
            break;
          case "/task/inspection":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/inspection"),
              id: 8,
              path: "/task/inspection",
            });
            break;
          case "/task/TaskStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/TaskStrategy"),
              id: 9,
              path: "/task/TaskStrategy",
            });
            break;
          case "/task/allocationStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/allocationStrategy"),
              id: 10,
              path: "/task/allocationStrategy",
            });
            break;
        }
      }
      for (let key in pathModulesObj) {
        switch (key) {
          case "/path/dispatching":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/dispatching"),
              id: 1,
              path: "/path/dispatching",
            });
            break;
          case "/path/planning":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/planning"),
              id: 2,
              path: "/path/planning",
            });
            break;
          case "/path/system":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/system"),
              id: 3,
              path: "/path/system",
            });
            break;
        }
      }
      for (let key in systemModulesObj) {
        switch (key) {
          case "/system/rms":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/rms"),
              id: 1,
              path: "/system/rms",
            });
            break;
          case "/system/api":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/api"),
              id: 2,
              path: "/system/api",
            });
            break;
          case "/system/robot":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/robot"),
              id: 3,
              path: "/system/robot",
            });
            break;
          case "/system/stop":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/stop"),
              id: 4,
              path: "/system/stop",
            });
            break;
          case "/system/monitor":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/monitor"),
              id: 5,
              path: "/system/monitor",
            });
            break;
          case "/system/URL":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/URL"),
              id: 6,
              path: "/system/URL",
            });
            break;
        }
      }
      for (let key in frontEndModulesObj) {
        switch (key) {
          case "/fesystem/hide":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/hide"),
              id: 1,
              path: "/fesystem/hide",
            });
            break;
          case "/fesystem/base":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/base"),
              id: 2,
              path: "/fesystem/base",
            });
            break;
        }
      }
      // && requestType !== 'update'
      if (this.modules.length && requestType !== "update") {
        const key = this.modules[0].code;
        this.getModules = obj[key];
        switch (key) {
          case "map":
            this.tabModules = this.mapModules;
            break;
          case "path":
            this.tabModules = this.pathModules;
            break;
          case "task":
            this.tabModules = this.taskModules;
            break;
          case "system":
            this.tabModules = this.systemModules;
            break;
          case "fesystem":
            this.tabModules = this.frontEndModules;
            break;
        }
        this.activeName = key;
      }
      return obj;
    },
    secondTabLabelFun(path) {
      let secondTabLabel = "";
      const reg = new RegExp("/", "g");
      const newPath = path.replace(reg, ".");
      secondTabLabel = `lang.configs${newPath}.label`;
      return secondTabLabel;
    },
    isNull(data) {
      if (Object.prototype.toString.call(data) === "[object Object]") {
        return Object.keys(data).length;
      }
      return null;
    },
    groupBy(result, group) {
      let obj = {};
      if (Array.isArray(result)) {
        result.forEach(item => {
          if (item[group]) {
            if (obj[item[group]]) {
              obj[item[group]].push(item);
            } else {
              obj[item[group]] = [];
              obj[item[group]].push(item);
            }
          } else {
            if (obj.AAList) {
              obj.AAList.push(item);
            } else {
              obj.AAList = [];
              obj.AAList.push(item);
            }
          }
        });
      }
      let newObj = {};
      let newKey = Object.keys(obj).sort();
      newKey.forEach(item => {
        newObj[item] = obj[item];
      });
      return newObj;
    },
    change(n) {
      this.changeData = n;
      // console.log("this.changeData", this.changeData);
    },
    beforeLeave() {
      // if (this.$refs.parameterContent) {
      //   this.$refs.parameterContent.tabContentReset()
      // }
      this.isApplySuccess = false;
      if (this.changeData === true) {
        this.dialog = true;
        // this.nextTabIndex = index;
        return false;
      }
      return true;
    },

    async applySuccess(modulesPath, arr) {
      console.log(modulesPath, arr);
      this.isApplySuccess = true;
      let pathArr = modulesPath.split("/");
      this.changeData = false;
      // 保存成功重新请求接口
      this.getModules = {};
      this.tabModules = [];
      await this.getConfigItems();
      return;
      await this.getConfigItems("update");
      let obj = {};
      arr.forEach(item => {
        obj[item.code] = item.value;
      });
      if (this.parameterList && Object.keys(this.parameterList).length) {
        let newObj = JSON.parse(JSON.stringify(this.parameterList[pathArr[1]]));
        console.log(this.parameterList, this.getModules);
        const tabArrKeys = Object.keys(newObj);
        // if (!tabArrKeys.includes(pathArr[2])) {
        //   this.getModules = {}
        //   this.tabModules = []
        //   const dataObj = JSON.parse(JSON.stringify(this.paramsConfigList));
        //   const newArr = dataObj.filter(item => {
        //     let paramsName = this.$t(item.paramsName)
        //     if (paramsName.includes(this.searchVal) || item.code.toLowerCase().includes(this.searchVal.toLowerCase())) {
        //       return item;
        //     }
        //   });
        //   // console.log(newArr);
        //   let obj = this.jsonFormat(newArr);

        //   this.parameterList = Object.assign({}, obj);
        //   return
        // }
        for (let key in newObj[pathArr[2]]) {
          newObj[pathArr[2]][key].forEach(item => {
            if (obj[item.code]) {
              let val = item.value;
              if (item.widgetType === "timeslot" && val) {
                val = JSON.parse(val);
              }
              if (item.immediate == false) {
                item.currentUpdateValue = obj[item.code];
              } else {
                item.value = obj[item.code];
                item.options.defVal = val;
              }
            }
            if (item.limitKey) {
              const keysArr = Object.keys(item.limitKey);
              item.isShow = this.limitShowObj[keysArr[0]] == item.limitKey[keysArr[0]];
            }
          });
        }
        this.getModules = Object.assign({}, newObj);
      } else {
        this.tabModules = [];
        this.getModules = JSON.parse(JSON.stringify({}));
      }
    },
    // 应用
    apply() {
      const paramsObj = this.$refs.parameterContent.paramsObj;
      const modulesPath = this.$refs.parameterContent.modulesPath;
      // console.log(paramsObj);
      const arr = [];
      for (let key in paramsObj) {
        arr.push(paramsObj[key]);
      }

      let isFlag = false;
      arr.forEach(item => {
        if (item.errorTipShow) {
          isFlag = true;
        }
      });
      if (isFlag) return;

      $req
        .post(
          "/athena/configs/update",
          arr.map(item => {
            let value = item.value;
            if (value instanceof Array) {
              value = value.join(",");
            }
            return { ...item, value };
          }),
          { intercept: false },
        )
        .then(res => {
          // console.log(res);
          if (res && res.result === "success") {
            this.$message.success(this.$t("lang.common.success"));
            this.dialog = false;
            this.changeData = false;
            // setTimeout(() => {
            //   this.getConfigItems();
            // }, 300);
            this.applySuccess(modulesPath, arr);
          }
        });
    },
    // 取消
    close() {
      this.dialog = false;
    },
    // 放弃
    giveUp() {
      this.changeData = false;
      this.dialog = false;
      this.$refs.parameterContent.giveUp();
      // const paramsObj = this.$refs.parameterContent.paramsObj;
      // for (let key in paramsObj) {
      //   paramsObj[key].errorTipShow = false
      // }
    },
    // 仅展示未生效参数
    async parameterIsShow(n) {
      // this.show = n;
      this.isView = true;
      this.searchVal = "";
      this.getModules = {};
      this.tabModules = [];
      await this.getConfigItems();

      // const dataObj = JSON.parse(JSON.stringify(this.paramsConfigList));
      // const newArr = dataObj.filter(item => {
      //   // let currentUpdateValue = item.currentUpdateValue || ''
      //   // item.value = item.value === null ? '' : item.value
      //   if (item.immediate == false && item.value !== item.currentUpdateValue && item.currentUpdateValue !== null) {
      //     return item;
      //   }
      // });
      // // console.log(newArr);
      // const obj = this.jsonFormat(newArr);
      // this.parameterList = Object.assign({}, obj);
      // console.log(this.parameterList);
    },
    async onSearch() {
      // console.log(this.searchVal);
      this.isApplySuccess = false;
      if (this.changeData === true) {
        this.dialog = true;
        // this.nextTabIndex = index;
        return false;
      }
      this.isView = false;
      this.initLoading = true;
      // this.changeData = false;
      await this.getConfigItems();
      this.getModules = {};
      this.tabModules = [];
      const dataObj = JSON.parse(JSON.stringify(this.paramsConfigList));
      const newArr = dataObj.filter(item => {
        let paramsName = this.$t(item.paramsName);
        if (paramsName.includes(this.searchVal) || item.code.toLowerCase().includes(this.searchVal.toLowerCase())) {
          return item;
        }
      });
      // console.log(newArr);
      let obj = this.jsonFormat(newArr);

      this.parameterList = Object.assign({}, obj);
    },
    async reset() {
      this.isApplySuccess = false;
      if (this.changeData === true) {
        this.dialog = true;
        // this.nextTabIndex = index;
        return false;
      }
      this.isView = false;
      // this.changeData = false;
      // this.selectValue = this.searchOptions[0].value;
      this.searchVal = "";
      this.initLoading = true;
      await this.getConfigItems();
      // const obj = this.jsonFormat(this.paramsConfigList);
      // this.parameterList = Object.assign({}, obj);
    },
    alertShow(n) {
      this.alertIsShow = n;
    },
    changeModulesPath(changModules) {
      Object.keys(changModules).forEach(itemKey => {
        this.getModules[itemKey] = changModules[itemKey];
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.gp-tabs--left .gp-tabs__item:last-child {
  padding-right: 16px !important;
}
.selectBox {
  display: flex;
  position: relative;
  // padding-left: 15px;
  // padding-top: 15px;
  padding: 10px 15px;
  background: #fff;
}
.btns {
  margin-left: 17px;
}

.searchIpt {
  font-weight: 400;
  width: 248px;
  height: 32px;
  font-size: 14px;
  line-height: 32px;
  color: #999ea5;
  position: relative;
}
/deep/.gp-input__suffix {
  margin-right: -7px;
}
.searchImg {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 23px;
  left: 305px;
}
/deep/.gp-tabs__item.is-active {
  background: #f7f8fa;
}
/deep/.gp-tabs__header {
  margin-top: -9px;
}
.content-box {
  flex: 1;
  padding: 8px 0;
  display: flex;
  width: 100%;
  height: calc(100% - 52px);
  background: #fff;
}
.new-content-box {
  height: calc(100% - 108px);
}
.iframeDiv {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  // margin-top: 12px;
  border-top: 8px solid #f7f8fa;
  // border-bottom: 8px solid #f7f8fa;
  background: #f7f8fa;
}
</style>
