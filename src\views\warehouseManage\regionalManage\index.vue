<template>
  <geek-main-structure class="flex flex-col">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset" />
    <div class="flex-1">
      <geek-customize-table :table-config="tableConfig" :data="tableData" @row-edit="rowEdit" />
    </div>
    <EditArea ref="editAreaDialog" @updateMainList="getTableList" />
  </geek-main-structure>
</template>

<script>
import EditArea from "./components/editAreaDialog";
export default {
  components: { EditArea },
  data() {
    const isGuest = this.getEditPermission();
    return {
      listTimmer: null,
      form: {
        logicId: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          logicId: {
            label: "lang.rms.fed.mapArea.id",
            default: "",
            tag: "input",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tableConfig: {
        columns: [
          { label: "lang.rms.fed.mapArea.id", prop: "logicId", width: "120", nowrap: true, disabled: true },
          { label: "lang.rms.fed.carProportion", prop: "robotDemand", nowrap: true },
          { label: "lang.rms.fed.carActualMount", prop: "robotActualNumber", nowrap: true },
          { label: "lang.rms.fed.carMount", prop: "robotNeedNumber", nowrap: true },
          { label: "lang.rms.fed.currentTaskMount", prop: "robotTaskNumber", nowrap: true },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "170",
                operations: [
                  {
                    label: "lang.rms.fed.buttonEdit",
                    handler: "row-edit",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  deactivated() {
    if (this.listTimmer) clearTimeout(this.listTimmer);
    this.listTimmer = null;
  },
  methods: {
    rowEdit(row) {
      this.$refs.editAreaDialog.open(row);
    },
    onQuery(val) {
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getEditPermission() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
    getTableList() {
      const data = { ...this.form };
      if (this.listTimmer) {
        clearTimeout(this.listTimmer);
      }
      $req.get("/athena/area/findById", data).then(res => {
        this.tableData = res?.data || [];
        this.listTimmer = setTimeout(() => {
          this.getTableList();
        }, 6000);
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
