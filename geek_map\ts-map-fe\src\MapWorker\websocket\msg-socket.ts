/* ! <AUTHOR> at 2022/08/25 */
import DataStore from "../store/store";

class MsgSocket {
  private floorList: Array<MWorker.floorList> = null;
  private queryParams: any = null; // {type,code}
  private _queryData: any = null; // 查找的数据结果
  private store: DataStore = new DataStore();

  MapInitResponseMsg(header: any, body: any, cb: MWorker.wsCallback) {
    const initType = header?.ext || "";
    const isInitFinish = initType.indexOf("MapFinished") !== -1;
    if (initType.indexOf("MapInfo") !== -1) {
      this.uninstall();
      const { map, config } = body;
      if (config) this.store.installConfig(config);

      if (!map) throw new Error("[websocket] >>>>> error:: MapInitResponseMsg 没有返回map数据呀");
      if (!map.floorIds) throw new Error("[websocket] >>>>> error:: map数据中没有floorIds呢");
      if (!map.floors) throw new Error("[websocket] >>>>> error:: map数据中没有floors呢");

      const { floorList, floorsData, released } = this.store.installFloors(map);
      let data: any = { isInitFinish, floorsData, released };
      if (!this.floorList) {
        data.floorList = floorList;
        this.floorList = floorList;
      }
      cb && cb("wsInitFloors", data);
      return isInitFinish;
    }

    if (body.config) this.store.installConfig(body.config);
    this.store.installDisplay(isInitFinish, body, cb);

    if (isInitFinish) {
      const config = this.store.getMapConfig();
      const systemWarning = this.store.getSystemWarning();
      cb && cb("wsMapConfig", { config, systemWarning });
    }
  }

  MapUpdateResponseMsg(body: any, cb: MWorker.wsCallback) {
    const systemWarning = this.store.getSystemWarning();

    let config: any = null;
    if (body.config) {
      const updateConfig = this.store.updateMapConfig(body.config);

      if (updateConfig) {
        if (!config) config = {};
        config.config = updateConfig;
      }
    }

    this.store.updateDisplay(body, cb);
    const updateSystemWarning = this.store.getSystemWarning();
    if (updateSystemWarning !== systemWarning) {
      if (!config) config = {};
      config.systemWarning = updateSystemWarning;
    }
    if (config) cb && cb("wsMapConfig", config);

    this.resolveQueryData(cb);
  }

  getSingleQuery(params: { layer: string; code: code }) {
    return this.store.querySingleData(params) || null;
  }

  reqSingleQuery(params: { layer: string; code: code }, cb: MWorker.wsCallback): void {
    this.stopQuery(); // 先清空之前的内容 再查找 我真机智

    if (JSON.stringify(params) !== JSON.stringify(this.queryParams)) this._queryData = null;

    this.queryParams = params;
    this.resolveQueryData(cb);
  }

  getMultiQuery(params: { layer: string; codes: Array<code> }) {
    return this.store.queryMultiData(params) || null;
  }

  stopQuery() {
    this.queryParams = null;
    this._queryData = null;
  }

  uninstall() {
    this.store.uninstall();
    this.queryParams = null;
    this._queryData = null;
  }

  destroy() {
    this.floorList = []; // 总共有多少楼层数组
    this.uninstall();
    this.store.destroy();
    this.store = null;
  }

  private resolveQueryData(cb: MWorker.wsCallback) {
    const params = this.queryParams;
    if (!params) return;

    let data = this.store.querySingleData(params) || null;

    if (!data) {
      this.stopQuery();
      cb && cb("wsQueryData", { params, data });
      return;
    }

    if (data["resourceHashCode"]) delete data["resourceHashCode"];
    if (JSON.stringify(data) == JSON.stringify(this._queryData)) return;

    this._queryData = Object.assign({}, data);
    let wsParams = Object.assign({}, params);

    if (params.layer === "shelf") {
      // 处理shelfType
      let type;
      if (["PPP_SHELF", "PP_SHELF"].includes(data["subModelCategory"])) type = "POP_PICK";
      else type = data["shelfType"] || "SHELF";

      // 根据shelfType 处理 layer
      switch (type) {
        case "POP_PICK":
          wsParams.layer = "poppick";
          break;
        case "X_PALLET": // 单独托盘
        case "X_HOLDER": // 单独底座
        case "X_HOLDER_PALLET": // 底座上面有托盘
        case "X_PALLET_STACK": // 全是托盘的碟盘机
        case "X_HOLDER_PALLET_STACK": // 带底座和托盘的碟盘机
          wsParams.layer = "xShelf";
          break;
      }
    }

    cb && cb("wsQueryData", { params: wsParams, data: this._queryData });
  }
}
export default MsgSocket;
