<template>
  <gp-dialog
    width="70%"
    :footer="false"
    :visible="visible"
    @close="handleCloseDialog"
    :title="$t('lang.rms.fed.taskDetail')"
  >
    <div class="ui-taskDetail__content">
      <!-- 子任务详情 -->
      <p class="mt15 mb10 f16">{{ $t("lang.rms.fed.taskInfo") }}</p>
      <gp-descriptions :column="3" border>
        <!-- 子任务ID -->
        <gp-descriptions-item :label="$t('lang.rms.fed.taskId')">{{ initRow.jobId || "--" }}</gp-descriptions-item>

        <!-- 机器人ID -->
        <gp-descriptions-item :label="$t('lang.mb.robotManage.robotId')">{{
          initRow.robotId || "--"
        }}</gp-descriptions-item>

        <!-- 容器编码 -->
        <gp-descriptions-item :label="$t('lang.rms.web.container.containerCode')">{{
          initRow.container || "--"
        }}</gp-descriptions-item>
        <!-- 任务状态 -->
        <gp-descriptions-item :label="$t('lang.rms.web.monitor.robot.taskState')">{{
          initRow.jobStatus || "--"
        }}</gp-descriptions-item>
        <!-- 任务耗时 -->
        <gp-descriptions-item :label="$t('lang.rms.fed.taskExpense')">{{
          initRow.costTime || "--"
        }}</gp-descriptions-item>
        <!-- 充电桩ID -->
        <gp-descriptions-item :label="$t('lang.rms.fed.chargerId')">{{
          initRow.chargerId || "--"
        }}</gp-descriptions-item>
      </gp-descriptions>
      <!-- 后期可能会加 -->
      <div class="filterSty">
        <p class="title f16">{{ $t("lang.rms.fed.taskDetail") }}</p>
        <gp-dropdown>
          <gp-button type="primary" size="mini">
            {{ $t("lang.rms.fed.logFiltering") }}<gp-icon name="gp-icon-arrow-down gp-icon--right" />
          </gp-button>
          <gp-dropdown-menu slot="dropdown">
            <gp-cascader-panel
              :props="{ multiple: true }"
              :options="filterItemList"
              @change="fliterChange"
            ></gp-cascader-panel>
          </gp-dropdown-menu>
        </gp-dropdown>
        <!-- <gp-button v-if="exportUrl" class="download-btn" type="success" size="mini" @click="exportLog">{{ $t('lang.rms.fed.downloadLog') }}</gp-button> -->
        <gp-button :loading="exportLoading" class="download-btn" type="primary" size="mini" @click="createLog">{{
          $t("lang.rms.fed.exportLog")
        }}</gp-button>
        <gp-button
          :loading="debugModelSearchLoad"
          class="download-btn"
          type="primary"
          size="mini"
          @click="debugTreeData"
          >{{ $t("lang.rms.fed.debugMode") }}</gp-button
        >
      </div>

      <TimelineItem :treeData="filterData" />
    </div>
  </gp-dialog>
</template>
<script>
import { parseFilterItems, parseFilterData, traces } from "./data";
import TimelineItem from "./timelineItem.vue";
/**
 *  lang.rms.fed.taskInfo: 任务信息
 *  lang.rms.fed.taskExpense: 任务耗时
 */
export default {
  props: {
    visible: Boolean,
    initRow: Object,
  },
  data() {
    return {
      filterItems: [],
      treeData: this.initRow.traces || [],
      filterText: "",
      exportUrl: "",
      exportLoading: false,
      debugModelSearchLoad: false,
    };
  },
  computed: {
    filterItemList() {
      return parseFilterItems(this.treeData) || [];
    },
    filterData() {
      const { treeData, filterItems } = this;
      return parseFilterData(treeData, filterItems, !filterItems.length);
    },
  },
  watch: {
    initRow() {
      this.debugModelSearchLoad = false;
      this.treeData = this.initRow.traces || [];
      this.filterItems = [];
    },
  },
  methods: {
    fetchTaskDetail() {},
    handleCloseDialog() {
      this.$emit("update:visible", false);
    },
    formatterTime(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss") || "";
    },
    filterNode(value, data) {
      if (!value) return true;
      return this.$t(data.traceTopic).indexOf(value) !== -1;
    },
    handleSearch() {
      this.$refs.tree.filter(this.filterText);
    },
    fliterChange(values) {
      this.filterItems = values;
    },
    debugTreeData() {
      this.debugModelSearchLoad = true;
      this.$emit("debugModelSearch");
    },
    async createLog() {
      this.exportLoading = true;
      const paramsObj = {
        jobId: this.initRow.jobStageId || this.initRow.jobId,
      };
      if (this.initRow.jobType === "DELIVER_BOX") {
        paramsObj.warehouseJob = true;
      }
      const { code, data } = await $req.postParams(`athena/engine/tools/job/generateJobLog`, paramsObj);
      this.exportLoading = false;
      if (code === 0) {
        this.exportUrl = `${location.origin}/${data.uri}`;
        this.downUrl(this.exportUrl);
      }
      this.$nextTick(() => {
        this.exportUrl = "";
      });
    },
    async exportLog() {
      this.exportUrl = "";
    },
    downUrl(url) {
      // 取url最后一个/后面的字符串作为文件名
      const fileName = url.substring(url.lastIndexOf("/") + 1);
      // 下载url
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      a.target = "_blank";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
  },
  components: { TimelineItem },
};
</script>
<style lang="less" scoped>
.custom-tree-node {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.filterSty {
  margin-top: 20px;
  position: relative;
  text-align: right;
  margin-bottom: 20px;

  .title {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .cascader {
    width: 100%;
    height: 100%;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
