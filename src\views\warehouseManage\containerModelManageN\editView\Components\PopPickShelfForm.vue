<template>
  <!-- 编辑任务 -->
  <gp-form
    class="mform"
    ref="mformRef"
    label-position="right"
    label-width="130px"
    size="mini"
    :model="editTaskData"
    :rules="editTaskRules"
  >
    <gp-form-item :label="$t('lang.rms.fed.shelfModelName')" prop="modelName">
      <gp-input class="w200" v-model="editTaskData.modelName" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 容器类型 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="editDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- sizeType -->
    <gp-form-item prop="sizeTypes" :label="$t('lang.rms.fed.supportedSizeType')">
      <sizeTypeInput :value.sync="editTaskData.sizeTypes" @change="sizeTypesChange" />
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.web.container.supportMove')" prop="move">
      <gp-select class="w200" v-model="editTaskData.move" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option :label="$t('lang.rms.web.container.canNotMove')" :value="0" />
        <gp-option :label="$t('lang.rms.web.container.canMove')" :value="1" />
      </gp-select>
    </gp-form-item>

    <!-- 是否下发给机器人 -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        :placeholder="$t('lang.rms.fed.choose')"
        @change="val => handleNeedSendRobot(val)"
      >
        <gp-option :label="$t('lang.rms.fed.no')" :value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" :value="1" />
      </gp-select>
    </gp-form-item>

    <!-- 下发模型ID -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text">
            <gp-icon name="gp-icon-question" />
          </gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>

    <!-- 货架尺寸 -->
    <p class="modelTitle">{{ $t("lang.rms.fed.shelfSize") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="length"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number v-model="editTaskData.length" :min="1" :max="9999999" size="mini" :step="1" step-strictly />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="width"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number v-model="editTaskData.width" :min="1" :max="9999999" size="mini" :step="1" step-strictly />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="height" :label="$t('lang.rms.fed.textHeight') + '(mm):'" label-width="80px">
          <gp-input-number v-model="editTaskData.height" :min="0" :max="100000" size="mini" :step="1" step-strictly />
        </gp-form-item>
      </gp-col>
    </gp-row>

    <div class="modelTitle">{{ $t("lang.rms.fed.shelfFoot") }}</div>
    <gp-row>
      <gp-col :span="8">
        <gp-form-item prop="legLength" :label="`${$t('lang.rms.fed.length')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.legLength"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="legWidth" :label="`${$t('lang.rms.fed.width')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.legWidth"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="legHeight" :label="`${$t('lang.rms.fed.textHeight')}(mm)`" label-width="110px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.legHeight"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
    </gp-row>

    <!-- 通行 -->
    <p class="modelTitle">{{ $t("lang.rms.fed.container.pass") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="passLength"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passLength"
            :min="0"
            :max="9999999999"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="passWidth"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passWidth"
            :min="0"
            :max="9999999999"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="passHeight" :label="$t('lang.rms.fed.textHeight') + '(mm):'" label-width="80px">
          <gp-input-number
            step-strictly
            v-model="editTaskData.passHeight"
            :min="0"
            :max="9999999999"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>

    <!-- 层列数据 -->
    <p class="modelTitle">{{ $t("lang.fed.rms.layerColumnCoordinates") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 层数 -->
        <gp-form-item prop="extendJson.layoutNum" :label="$t('lang.rms.palletPositionManage.layer')" label-width="80px">
          <gp-input-number
            v-model="editTaskData.extendJson.layoutNum"
            :min="1"
            :max="20"
            :disabled="rowDisabled"
            size="mini"
            :step="1"
            @change="changeLayerNumber"
            step-strictly
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="12">
        <!-- 列数 -->
        <gp-form-item
          prop="extendJson.layerColumnNumber"
          :label="$t('lang.fed.rms.defaultNumberOfColumnsPerLayer')"
          label-width="120px"
        >
          <gp-input-number
            v-model="editTaskData.extendJson.layerColumnNumber"
            :min="1"
            :disabled="rowDisabled"
            :max="5"
            size="mini"
            :step="1"
            @change="changeLayerColumnNumber"
            step-strictly
          />
        </gp-form-item>
      </gp-col>
      <!-- 列数据 -->
      <gp-col v-for="(columnItem, index) in editTaskData.extendJson.xCoordinates" :key="index" :span="8">
        <!-- 层数 -->
        <gp-form-item
          :prop="`extendJson.xCoordinates[${index}]`"
          :label="$t('lang.rms.fed.tierNo') + (index + 1) + $t('lang.fed.rms.columnXCoordinate') + '(mm)'"
          label-width="130px"
        >
          <gp-input-number
            class="xCoordinateInput"
            v-model="editTaskData.extendJson.xCoordinates[index]"
            :min="1"
            :max="9999999"
            :step="1"
            size="mini"
            step-strictly
          />
        </gp-form-item>
      </gp-col>
    </gp-row>

    <!-- card -->
    <p class="modelTitle">
      {{ $t("lang.fed.rms.layerData") }}:
      <span class="warnText">{{ $t("lang.fed.rms.layerDataMsg0") }}</span>
    </p>
    <div class="layerSetBox">
      <gp-button
        v-for="(layoutItem, index) in editTaskData.layoutList"
        class="layoutBtn"
        :key="index"
        type="text"
        @click="setActiveOrientation(layoutItem.orientation)"
      >
        <span :class="{ active: activeOrientation === layoutItem.orientation }">
          {{ layoutItem.orientation + $t("lang.fed.rms.surface") }}
        </span>
      </gp-button>
      <gp-checkbox class="layoutSync" v-model="sync" @change="syncChange">
        {{ $t("lang.fed.rms.synchronizeTo") + syncByOrientationMap[activeOrientation] + $t("lang.fed.rms.surface") }}
      </gp-checkbox>
    </div>
    <div v-for="(layoutItem, i) in editTaskData.layoutList" :key="i">
      <div v-show="activeOrientation === layoutItem.orientation">
        <gp-row
          class="detailRowItem"
          v-for="(item, index) in layoutItem.layout"
          :key="`${layoutItem.orientation}_${index}`"
        >
          <gp-col :span="8">
            <gp-form-item
              :prop="`layoutList[${i}].layout[${index}].curLayerColumnNumber`"
              :label="$t('lang.rms.fed.tierNo') + (index + 1) + $t('lang.rms.fed.latticePage.layer')"
              label-width="100px"
            >
              <gp-input-number
                :value="item.curLayerColumnNumber"
                :min="1"
                :max="5"
                :disabled="rowDisabled"
                :key="`${layoutItem.orientation}_${index}_curLayerColumnNumber`"
                @change="value => changeLayerDataByLayer(layoutItem.orientation, index, 'curLayerColumnNumber', value)"
                step-strictly
              ></gp-input-number>
              {{ $t("lang.rms.fed.latticePage.column") }}
            </gp-form-item>
          </gp-col>
          <gp-col :span="8">
            <gp-form-item
              :prop="`layoutList[${i}].layout[${index}].locationY`"
              :label="'y' + $t('lang.rms.web.monitor.robot.location') + '(mm)'"
              label-width="100px"
            >
              <gp-input-number
                v-model="item.locationY"
                :min="1"
                :max="10000"
                :step="1"
                @change="changeLayerData"
                step-strictly
              ></gp-input-number>
            </gp-form-item>
          </gp-col>
          <gp-col :span="8">
            <gp-form-item
              :prop="`layoutList[${i}].layout[${index}].childModelId`"
              :label="$t('lang.fed.rms.adaptContainer')"
              label-width="100px"
            >
              <gp-select v-model="item.childModelId" :placeholder="$t('lang.rms.fed.choose')" @change="changeLayerData">
                <!--  op.value == 10:: 列和默认列的值不相同 不可选DEFAULT -->
                <gp-option
                  v-for="op in containerModelDict"
                  :key="op.value"
                  :label="$t(op.label)"
                  :value="op.value"
                  :disabled="
                    (op.value == 10 || op.label == 'DEFAULT') &&
                    item.curLayerColumnNumber !== editTaskData.extendJson.layerColumnNumber
                  "
                />
              </gp-select>
            </gp-form-item>
          </gp-col>
        </gp-row>
      </div>
    </div>
  </gp-form>
</template>

<script>
import { mapMutations, mapState, mapActions } from "vuex";
import { getContainerModelData } from "../../api/index";
import sizeTypeInput from "./sizeTypeInput.vue";
const getDefaultData = ({ layoutDefYNumber }) => {
  return {
    layout: 5,
    modelName: "",
    move: 1,
    modelCategory: "PPP_SHELF",
    modelType: "",
    sizeTypes: "",
    width: 1350,
    length: 1280,
    height: 3580,
    legWidth: 40,
    legHeight: 300,
    legLength: 40,
    passLength: 0,
    passWidth: 0,
    passHeight: 0,
    needSendRobot: 0,
    sendModelId: "",
    extendJson: {
      layoutNum: 10,
      layerColumnNumber: 3,
      xCoordinates: [154, 576, 998],
    },
    layoutList: ["F", "B"].map(orientation => {
      return {
        orientation,
        orientationCount: 2, // 写死  双面
        layout: [...new Array(10)].map((item, index) => {
          return {
            layer: index + 1,
            curLayerColumnNumber: 3,
            childModelId: "",
            locationY: layoutDefYNumber[index],
            containerModelDict: [],
          };
        }),
      };
    }),
  };
};

export default {
  data() {
    const layoutDefYNumber = [377, 731, 1086, 1440, 1795, 2149, 2504, 2858, 3213, 3567];
    return {
      sizeTypeParamTip: null,
      // 编辑任务数据
      editTaskData: getDefaultData({ layoutDefYNumber }),
      activeOrientation: "F",
      sync: true,
      // loading
      saveLoading: false,
      syncByOrientationMap: {
        F: "B",
        B: "F",
      },

      containerModelDict: [],
      layoutDefYNumber,
    };
  },
  components: { sizeTypeInput },
  computed: {
    ...mapState("containerModal", ["shelfCategoryDict", "maxModelId", "editData"]),
    editDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    rowDisabled() {
      return this.editData?.used;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    isEdit() {
      return !!this.editData.id;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };

      const rule = {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          requiredRule,
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        // sizeTypes: [
        //   {
        //     // 正则 只允许输入英文，数字，和标点符号，限制15字符之内
        //     pattern: /^[a-zA-Z0-9\-\_]{0,15}$/,
        //     message: this.$t("lang.rms.fed.enter15CharactersOr01"),
        //     trigger: "blur",
        //   },
        // ],
        move: [requiredRule],
        layout: [requiredRule],
        width: [requiredRule],
        length: [requiredRule],
        height: [requiredRule],
        legHeight: [requiredRule],
        legWidth: [requiredRule],
        legLength: [requiredRule],
        passLength: [requiredRule],
        passWidth: [requiredRule],
        passHeight: [requiredRule],
        "extendJson.layoutNum": [requiredRule],
        "extendJson.layerColumnNumber": [requiredRule],
      };

      (this.editTaskData.extendJson.xCoordinates || []).forEach((item, index) => {
        rule[`extendJson.xCoordinates[${index}]`] = [requiredRule];
      });

      this.editTaskData.layoutList.forEach((item, i) => {
        item.layout.forEach((item, index) => {
          rule[`layoutList[${i}].layout[${index}].curLayerColumnNumber`] = [requiredRule];
          rule[`layoutList[${i}].layout[${index}].locationY`] = [requiredRule];
          rule[`layoutList[${i}].layout[${index}].childModelId`] = [requiredRule];
        });
      });

      return rule;
    },
    // maxColumnNumber() {
    //   if (!this.editTaskData.extendJson) return 0;
    //   const layerColumnNumber = this.editTaskData.extendJson.layerColumnNumber;
    //   const layoutItem = this.editTaskData.layoutList[0].layout;
    //   let maxLayerNumber = 0;

    //   // 取出layoutItem中最大的层, 赋值给maxLayerNumber
    //   layoutItem.forEach(item => {
    //     if (item.layer > maxLayerNumber) {
    //       maxLayerNumber = item.layer;
    //     }
    //   });

    //   return maxLayerNumber < layerColumnNumber ? layerColumnNumber : maxLayerNumber;
    // },
  },
  async created() {
    await this.getContainerModelData();

    if (this.editData.id) {
      // 这里如果不脱离store, 编辑会报错
      this.editTaskData = this.parseAttrData();
      const { extendJson } = this.editTaskData || {};
      if ("sync" in extendJson) {
        this.sync = extendJson.sync;
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.$refs.mformRef.clearValidate();
    }, 500);
  },
  watch: {
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
    "editTaskData.sizeTypes"(val) {
      let valList = val ? val.split(",") : [];
      if (val) this.sizeTypesChange(valList);
    },
    "editTaskData.extendJson"() {},
  },
  methods: {
    handleNeedSendRobot(val) {
      if (val) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },
    async validateData() {
      const { containerModelDict, sync } = this;
      try {
        // debugger
        await this.$refs.mformRef.validate();
        // 货格宽=货架宽度/列数，货格高=货架高度/层数，货格深度=货架长度/2
        // 货格的宽大于货箱宽，货格高大于货箱高，货格深度大于货箱长度
        const formData = this.parseFormData();
        const cargoW = formData.width / formData.extendJson.layerColumnNumber;
        const cargoH = formData.height / formData.extendJson.layoutNum;
        const cargoL = formData.length / 2;

        let flag = "";
        formData.layoutList.forEach(layerItem => {
          layerItem.layout.forEach(item => {
            const childModelId = item.childModelId;
            const itemData = containerModelDict.find(containerItem => {
              return containerItem.value === childModelId;
            });
            const { width, height, length } = itemData;
            if (width > cargoW || height > cargoH || length > cargoL) {
              flag = itemData.modelName;
            }
          });
        });

        if (flag) {
          this.$message.error(this.$t("lang.fed.rms.exceSizeCargoCompartmentMsg0", [flag]));
          return false;
        }

        formData.extendJson.sync = sync;

        return formData;
      } catch (error) {
        console.log("validateData::error", error);
        return false;
      }
    },

    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null;
        return;
      }
      const reg = /^[a-zA-Z]{0,15}$/;
      if (data) {
        data.forEach(item => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null;
          } else {
            this.sizeTypeParamTip = this.$t("lang.rms.fed.enterEnglish15Characters");
          }
        });
      }
    },
    parseAttrData() {
      const data = JSON.parse(JSON.stringify(this.editData));
      const { containerLayout, ...editDataConf } = data;
      return {
        ...editDataConf,
        layoutList: (containerLayout || []).map(item => {
          return {
            ...item,
            layout: (item.layout || []).map(layerItem => {
              const { layer, childModelId, layerColumns } = layerItem;
              const locationY = layerColumns[0].locationY;
              return {
                childModelId,
                locationY,
                layer,
                curLayerColumnNumber: layerColumns && layerColumns.length,
                containerModelDict: this.containerModelDict,
              };
            }),
          };
        }),
      };
    },

    parseFormData() {
      const layoutList = this.editTaskData.layoutList;
      const { xCoordinates, layerColumnNumber } = this.editTaskData.extendJson;
      const parseLayoutList = layoutList.map(item => {
        const { layout, ...orientationConf } = item;

        return {
          ...orientationConf,
          layout: layout.map(layerItem => {
            const { locationY, curLayerColumnNumber, ...layerItemConf } = layerItem;
            return {
              ...layerItemConf,
              // curLayerColumnNumber: layerColumnNumber,
              layerColumns: [...new Array(curLayerColumnNumber)].map((_, index) => {
                const locationX = (xCoordinates || [])[index] || 0;
                return {
                  locationX,
                  locationY,
                  columnNum: index + 1,
                };
              }),
            };
          }),
        };
      });
      return {
        ...this.editTaskData,
        layoutList: parseLayoutList,
      };
    },

    setActiveOrientation(orientation) {
      this.activeOrientation = orientation;
    },

    changeLayerNumber(number) {
      const { layoutDefYNumber } = this;
      const { layoutList } = this.editTaskData;
      const defLaterColumnNumber = this.editTaskData.extendJson.layerColumnNumber;
      const xCoordinates = this.editTaskData.extendJson.xCoordinates;
      layoutList.forEach(orientationItem => {
        const { layout } = orientationItem;
        const layoutLen = layout.length;
        if (number > layoutLen) {
          const len = number - layoutLen;
          //新加一行
          const arr = [...new Array(len)].map((item, index) => {
            return {
              layer: layoutLen + index + 1,
              childModelId: "",
              locationY: layoutDefYNumber[layoutLen + index - 1],
              curLayerColumnNumber: defLaterColumnNumber,
            };
          });
          layout.push(...arr);
        } else {
          layout.splice(number);
        }
      });
    },

    //修改默认列
    changeLayerColumnNumber(num) {
      const number = Math.round(num);
      const { xCoordinates } = this.editTaskData.extendJson;
      const { layoutList } = this.editTaskData;

      // 如果列数 number 大于 xCoordinates.length, 则补充, 默认为1
      if (number > xCoordinates.length) {
        const len = number - xCoordinates.length;
        const arr = [...new Array(len)].map(() => 1);
        xCoordinates.push(...arr);
      } else {
        xCoordinates.splice(number);
      }

      this.editTaskData.extendJson.xCoordinates = xCoordinates;

      layoutList.forEach(orientationItem => {
        const { layout } = orientationItem;
        layout.forEach(item => {
          item.curLayerColumnNumber = number;
        });
      });
    },

    changeLayerData() {
      const { layoutList } = this.editTaskData;
      const { sync } = this;
      // 被同步数据
      const thisLayout = layoutList.find(item => item.orientation === this.activeOrientation).layout;

      // 呗同步的数据源
      const syncLayoutList = layoutList.find(item => item.orientation !== this.activeOrientation);

      // 同步数据
      syncLayoutList.layout.forEach((item, index) => {
        const thisItem = thisLayout[index];
        const { childModelId, locationY, layer, curLayerColumnNumber } = thisItem;
        if (sync) {
          item.layer = layer;
          item.curLayerColumnNumber = curLayerColumnNumber;
          item.locationY = locationY;
          item.childModelId = childModelId;
        }
      });

      syncLayoutList.layout = [...syncLayoutList.layout];

      this.setChildModelIdDefault();
    },

    //修改每一行 的 列数
    changeLayerDataByLayer(orientation, index, name, value) {
      this.editTaskData.layoutList.find(item => item.orientation === orientation).layout[index][name] = value;
      this.changeLayerData();
    },

    syncChange(value) {
      value && this.changeLayerData();
    },

    getDefaultChildModelId(editItem) {
      const layerColumnNumber = this.editTaskData.extendJson?.layerColumnNumber || 1;
      if (editItem.childModelId) {
        if (editItem.curLayerColumnNumber === layerColumnNumber || editItem.childModelId !== 10) {
          return editItem.childModelId;
        }
      }

      return (
        this.containerModelDict.find(item => {
          return item.label === "DEFAULT";
        })?.value || ""
      );
    },
    setChildModelIdDefault() {
      this.editTaskData.layoutList.forEach(layerItem => {
        layerItem.layout.forEach(item => {
          item.childModelId = this.getDefaultChildModelId(item);
        });
      });
    },
    async getContainerModelData() {
      const { code, data } = await getContainerModelData({
        pageSize: 3000,
        currentPage: 1,
        modelName: "",
        modelCategory: "BOX",
      });

      if (code === 0) {
        this.containerModelDict = (data?.recordList || []).map(item => {
          return {
            ...item,
            label: item.modelName,
            value: item.id,
          };
        });

        this.setChildModelIdDefault();
      }
    },
  },
};
</script>

<style scoped lang="less">
.modelTitle {
  font-weight: 600;
  height: 26px;
  margin: 5px 0;
  text-align: left;
  font-size: 14px;
  padding: 3px 12px;
  background: #eee;
}

.xCoordinateInput {
  width: 150px;
}

.mform {
  flex: 1;
  position: relative;
  overflow: auto;
  padding-bottom: 10px;
  display: flex;
  flex-direction: column;
}

.detail {
  flex: 1;
  overflow: auto;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.floor {
  text-align: center;
}

:deep(.rms-radio) {
  margin-right: 15px;
}

.detailBoxs {
  padding-top: 20px;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column-reverse;

  .detailRowItem {
    height: 40px;
    min-height: 40px;
  }
}

.warnText {
  color: red;
}

.delLegsItem {
  position: relative;

  .delLegsIcon {
    color: #f56c6c;
    cursor: pointer;
  }
}

.layerSetBox {
  position: relative;
  margin: 10px;

  .layoutSync {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .layoutBtn {
    font-size: 16px;
    color: #66b1ff;

    .active {
      font-weight: 900;
      color: #409eff;
    }
  }
}

.sizeType {
  min-width: 200px;
  max-width: 100%;
}
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}
.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
