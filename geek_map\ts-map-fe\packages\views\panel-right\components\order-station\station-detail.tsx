/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, Divider, Tabs } from "antd";
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import OrderGrid from "../common/order-grid";
import OrderPanelGrid from "../common/order-panel-grid";
import { max } from "moment";

type PropsOrderData = {
  stationData: stationData;
  currentSelect: any;
};
function ShelfDetail(props: PropsOrderData) {
  const { t } = useTranslation();

  const [stationId, setStationId] = useState("");
  const [currentTab, setCurrentTab] = useState("station");
  const [tabItems, setTabItems] = useState<Array<any>>([]);
  const [showAllTask, setShowAllTask] = useState(false);

  // 接收 poppick 数据
  useEffect(() => {
    const data = props.stationData;
    if (!data) {
      setStationId("");
      setTabItems([]);
      setCurrentTab("station");
      return;
    }

    let items: any = [
      {
        label: t("lang.rms.fed.workstation"),
        key: "station",
        children: getShelf(data),
      },
    ];

    if (data.parkList && data.parkList.length > 0) {
      items.push({
        label: t("lang.rms.fed.park"),
        key: "park",
        children: getPark(data.parkList),
      });
    }

    const currentSelect = props.currentSelect;
    if (currentSelect) {
      items.push({
        label: t("lang.rms.fed.lattice"),
        key: "lattice",
        children: getLattice([currentSelect]),
      });

      if (currentSelect.relateBoxCode) {
        items.push({
          label: t("lang.rms.fed.box"),
          key: "box",
          children: getBox(currentSelect.relateBox || {}),
        });
      }
    }
    setTabItems(items);

    let code: string = props.stationData?.stationId.toString() || "";
    if (code !== stationId) {
      setCurrentTab("station");
      setStationId(code);
    }
  }, [props.stationData, props.currentSelect]);

  useEffect(() => {
    const currentSelect = props.currentSelect;
    if (!currentSelect) {
      setCurrentTab("station");
      return;
    }

    if (currentSelect.relateBoxCode) setCurrentTab("box");
    else setCurrentTab("lattice");
  }, [props.currentSelect]);

  const getShelf = (data: any) => {
    return (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.stationId"),
            value: data?.stationId || "--",
          },
          {
            label: t("lang.rms.fed.coordinates"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.fed.stationPlaceDir"),
            value: data?.placeDir || "--",
          },
          {
            label: t("lang.rms.fed.stationIsWorking"),
            value: data.isWorking ? "true" : "false",
          },
          {
            label: t("lang.rms.fed.stationType"),
            value: data.stationType || "--",
          },
        ]}
      />
    );
  };
  const getPark = (parks: any) => {
    return parks.map((park: any, index: number) => {
      return (
        <div key={index} className="park-detail-list">
          <OrderGrid
            items={[
              {
                label: t("lang.rms.fed.stationPoint.parkId"),
                value: park.parkId || "--",
              },
              {
                label: t("lang.rms.fed.coordinates"),
                value: park.index || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceType"),
                value: park.deviceType || "--",
              },
              {
                label: t("lang.rms.fed.stationPlaceDir"),
                value: park?.placeDir || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.isWorking"),
                value: park.isWorking ? t("lang.rms.fed.chargerNormal") : t("lang.rms.fed.stopStatus") || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.busModel"),
                value: park.busModel || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceCode"),
                value: park.subDeviceCode || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceStatus"),
                value: park.deviceState || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceJobId"), //设备作业
                value: park.deviceJobId || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceJobStatus"), //设备状态
                value: park.deviceJobStatus || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceJobPhase"), //设备阶段
                value: park.deviceJobPhase || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceTaskId"), //设备任务
                value: park.deviceTaskId || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceTaskStatus"), //任务状态
                value: park.deviceTaskStatus || "--",
              },
              {
                label: t("lang.rms.fed.stationPoint.deviceTaskPhase"), //任务阶段
                value: park.deviceTaskPhase || "--",
              },
            ]}
          />
        </div>
      );
    });
  };

  const getLattice = (lattices: any) => {
    return lattices.map((lattice: any, index: number) => {
      return (
        <div key={index} className="lattice-detail-list">
          <OrderGrid
            items={[
              {
                label: t("lang.rms.fed.latticeCode"),
                value: lattice?.latticeCode || "--",
              },
              {
                label: t("lang.rms.fed.latticeStatus"),
                value: lattice?.latticeStatus || "--",
              },
              {
                label: t("lang.rms.fed.latticeAvailableStatus"),
                value: lattice?.latticeFlag || "--",
              },
              {
                label: t("lang.rms.fed.layer"),
                value: lattice?.layer || "--",
              },
              {
                label: t("lang.rms.fed.height"),
                value: lattice?.height || "--",
              },
              {
                label: t("lang.rms.fed.fetchDirs"),
                value: lattice?.fetchDirs || "--",
              },
              {
                label: t("lang.rms.fed.locationCode"),
                value: lattice?.locationCode || "--",
              },
              {
                label: t("lang.rms.fed.latticePage.latticeType"),
                value: lattice?.latticeType || "--",
              },
              {
                label: t("lang.rms.fed.weight"),
                value: lattice?.weight || lattice?.weight === 0 ? lattice?.weight : "--",
              },
            ]}
          />
        </div>
      );
    });
  };

  const getJobId2DestShelfMap = (jobId2DestShelfMap: any) => {
    const taskIds = Object.keys(jobId2DestShelfMap);
    const len = taskIds.length;
    if (len === 0) return "--";
    let resultStr = "";

    for (let j = 0; j < len; j++) {
      const taskList = jobId2DestShelfMap[taskIds[j]];
      for (let i = 0; i < taskList.length; i++) {
        const task = taskList[i];
        let str = "shelf:";
        const { destShelfCode, destShelfSide, destLatticeCodes } = task;

        if (destShelfCode) {
          str += destShelfCode;
        }
        if (destShelfSide) {
          str += "," + destShelfSide;
        }
        if (destLatticeCodes) {
          str += "," + JSON.stringify(destLatticeCodes);
        }

        resultStr += "\n" + str;
        str = "";
      }
    }
    return resultStr;
  };

  const getJobId2ParkIdsMap = (getJobId2ParkIdsMap: any) => {
    const taskIds = Object.keys(getJobId2ParkIdsMap);
    const len = taskIds.length;
    if (len === 0) return "--";
    let resultStr = "";

    for (let j = 0; j < len; j++) {
      const task = getJobId2ParkIdsMap[taskIds[j]];
      let str = "park:" + task;

      resultStr += "\n" + str;
      str = "";
    }
    return resultStr;
  };

  const getBox = (box: any) => {
    return (
      <div>
        <OrderGrid
          items={[
            {
              label: t("lang.rms.fed.boxCode"),
              value: box?.boxCode || "--",
            },
            {
              label: t("lang.rms.fed.boxStatus"),
              value: box?.boxStatus || "--",
            },
            {
              label: t("lang.rms.fed.boxCurrentLocation"),
              value: box?.location || "--",
            },
            {
              label: t("lang.rms.fed.placeLatticeCode"),
              value: box?.placeLatticeCode || "--",
            },
            {
              label: t("lang.rms.fed.currentLatticeCode"),
              value: box?.currentLatticeCode || "--",
            },
            {
              label: t("lang.rms.fed.currentLatticeType"),
              value: box?.currentLatticeType || "--",
            },
            {
              label: t("lang.rms.fed.robotId"),
              value: box?.robotId || "--",
            },
            {
              label: t("lang.rms.fed.locationCode"),
              value: box?.locationCode || "--",
            },
            {
              label: t("lang.rms.fed.boxHoldingTaskId"),
              value: box?.jobIds || "--",
            },

            {
              label: t("lang.rms.fed.stageIds"),
              value: box?.stageIds || "--",
            },
            {
              label: t("lang.rms.fed.boxWeight"),
              value: box?.weight || box?.weight === 0 ? box?.weight + "g" : "--",
            },
            {
              label: t("lang.rms.fed.taskDestination"),
              value: box?.jobId2ParkIdsMap ? getJobId2ParkIdsMap(box?.jobId2ParkIdsMap || {}) : "--",
            },
          ]}
        />

        <OrderPanelGrid
          style={{ borderLeft: 0, borderRight: 0, marginBottom: 0 }}
          items={[
            {
              label: t("lang.rms.fed.taskDestination"),
              node:
                box?.jobId2DestShelfMap && Object.keys(box?.jobId2DestShelfMap).length > 0 ? (
                  <div style={{ position: "relative", paddingBottom: "20px" }}>
                    <div style={{ height: showAllTask ? "auto" : "30px", overflow: "hidden" }}>
                      {getJobId2DestShelfMap(box?.jobId2DestShelfMap || {})}
                    </div>

                    <Button
                      style={{
                        position: "absolute",
                        bottom: 0,
                        right: 0,
                        background: "#fff",
                        width: "100%",
                        height: "20px",
                        minHeight: "20px",
                        padding: 0,
                      }}
                      type="link"
                      onClick={() => {
                        setShowAllTask(!showAllTask);
                      }}
                    >
                      {showAllTask ? <CaretUpOutlined /> : <CaretDownOutlined />}
                    </Button>
                  </div>
                ) : null,
            },
          ]}
        />
      </div>
    );
  };

  return (
    !!props.stationData && (
      <Tabs
        activeKey={currentTab}
        onTabClick={(key: any) => setCurrentTab(key)}
        className="component-operate-detail"
        items={tabItems}
      />
    )
  );
}

export default ShelfDetail;
