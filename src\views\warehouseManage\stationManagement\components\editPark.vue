<template>
  <gp-dialog
    :title="$t('lang.rms.fed.buttonEdit')"
    class="parkFormDialog"
    border
    :visible.sync="dialogVisible"
    width="520px"
  >
    <geek-customize-form ref="parkForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <gp-button @click="close" plain>{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </gp-button>
    </span>
  </gp-dialog>
</template>

<script>
export default {
  name: "EditParkDialog",
  props: ["robotTypes"],
  data() {
    return {
      dialogVisible: false,
      stationId: "",
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "180px",
          labelPosition: "right",
        },
        configs: {
          isWorking: {
            label: "lang.rms.fed.status",
            default: false,
            tag: "switch",
          },
          place: {
            label: "lang.rms.fed.WorkStationFace",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "east",
                label: "lang.rms.fed.east",
              },
              {
                value: "south",
                label: "lang.rms.fed.south",
              },
              {
                value: "west",
                label: "lang.rms.fed.west",
              },
              {
                value: "north",
                label: "lang.rms.fed.north",
              },
            ],
          },
          maxQueueSize: {
            label: "lang.rms.fed.maxQueueNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 停靠机器人类型
          robotTypes: {
            label: "lang.rms.fed.WorkStationAllowedRobotTypes",
            multiple: true,
            default: [],
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.robotTypes,
          },
          // 优先级
          priority: {
            label: "lang.rms.web.monitor.robot.robotPriority",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 排队等待点数量
          waitCellNumber: {
            label: "lang.rms.fed.WorkStationWaitCellNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 负载顶升高度
          loadHeight: {
            label: "lang.rms.fed.WorkStationLoadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
          // 空载顶升高度
          unloadHeight: {
            label: "lang.rms.fed.WorkStationUnloadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
          // 货架是否等待还箱
          waitingReturnBox: {
            label: "lang.rms.fed.waitingReturnBox",
            default: 0,
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
          // 提前移动抓手到还箱点
          moveReturnLattice: {
            // isHidden: true,
            label: "lang.rms.fed.moveToReturnLattice",
            default: 0,
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
          // 提前移动抓手到取箱点
          moveFetchLattice: {
            // isHidden: true,
            label: "lang.rms.fed.moveToFetchLattice",
            default: 1,
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
        },
      },
    };
  },
  watch: {
    robotTypes(arr) {
      this.formConfig.configs.robotTypes.options = arr;
    },
  },
  methods: {
    open(data, stationId) {
      const params = {
        isWorking: data.isWorking == "true" || data.isWorking == true ? true : false,
        place: data.place || "",
        maxQueueSize: data.maxQueueSize || 0,
        robotTypes: data.robotTypes || [],
        priority: data.priority || 0,
        waitCellNumber: data.waitCellNumber || 0,
        loadHeight: data.loadHeight || 0,
        unloadHeight: data.unloadHeight || 0,
        waitingReturnBox: data.waitingReturnBox || 0,
        moveReturnLattice: data.moveReturnLattice,
        moveFetchLattice: data.moveFetchLattice,
      };
      this.rowData = data;
      this.stationId = stationId;
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.parkForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      const formData = this.$refs.parkForm.getData();

      const params = Object.assign({}, formData, {
        stationId: this.stationId,
        parkId: this.rowData.parkId,
        isWorking: formData.isWorking.toString(),
      });
      $req.post("/athena/station/management/updateStationPark", params).then(res => {
        this.$success();
        this.dialogVisible = false;
        this.$emit("updateMainList");
      });
    },
  },
};
</script>
<style lang="less" scoped>
.parkFormDialog {
  ::v-deep .gp-input-number {
    width: 100%;
  }
}
</style>
