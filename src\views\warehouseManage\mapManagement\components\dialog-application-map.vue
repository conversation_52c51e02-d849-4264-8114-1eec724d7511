<template>
  <div>
    <gp-dialog
      :title="$t(dialogApplicationMap.title)"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form geek-dialog-application-map"
      width="700px"
      border
      @close="close"
    >
      <gp-link v-if="activeType != 2" type="primary" :underline="false" class="tips">
        {{ $t("lang.rms.web.map.version.mapApplicationTips") }}
      </gp-link>
      <gp-link
        v-if="[4, 5, 6].includes(rowData.status) && activeType != 2"
        type="danger"
        :underline="false"
        class="danger"
      >
        {{ $t("lang.rms.web.map.version.nonReleasedMapApplicationWarning") }}
      </gp-link>
      <gp-form label-position="right" label-width="120px">
        <gp-form-item :label="$t('lang.rms.web.map.version.cuurentMap')">
          <gp-input type="text" :value="currentMapName" disabled />
        </gp-form-item>
        <gp-form-item :label="$t('lang.rms.web.map.version.mapsToBeApplied')">
          <gp-input type="text" :value="rowData.name" disabled />
        </gp-form-item>
        <gp-form-item :label="$t('lang.rms.web.map.version.mapChange')">
          <gp-input v-model="remark" type="textarea" />
        </gp-form-item>
        <gp-form-item>
          <gp-radio-group v-model="activeType" size="small">
            <gp-radio :label="0" border>
              {{ $t("lang.rms.web.map.version.takeEffectAfterRestart") }}
            </gp-radio>
            <gp-radio :label="1" border>
              {{ $t("lang.rms.web.map.version.immediateEffect") }}
            </gp-radio>
            <gp-radio :label="2" border>
              {{ $t("lang.rms.fed.reloadFuncCellAndArea") }}
            </gp-radio>
          </gp-radio-group>
        </gp-form-item>
        <gp-form-item class="form-tips">
          <gp-link v-if="activeType == 0" type="primary" :underline="false" class="tips">
            {{ $t("lang.rms.web.map.version.takeEffectAfterRestartTip") }}
          </gp-link>
          <gp-link v-if="activeType == 1" type="danger" :underline="false" class="danger">
            {{ $t("lang.rms.web.map.version.ChooseEffectImmediately") }}
          </gp-link>
          <gp-link v-if="activeType == 2" type="danger" :underline="false" class="danger">
            {{ $t("lang.rms.fed.reloadFuncCellAndAreaInfo") }}
          </gp-link>
          <!-- <gp-link v-if="activeType == 2" type="primary" :underline="false" class="tips">
            {{ $t("lang.rms.web.map.version.takeEffectAfterRestartTip") }}
          </gp-link>
          <gp-link v-else type="danger" :underline="false" class="danger">
            {{ $t("lang.rms.web.map.version.ChooseEffectImmediately") }}
          </gp-link> -->
        </gp-form-item>
      </gp-form>

      <div slot="footer">
        <gp-button @click="visible = false" plain>{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" :disabled="disable" @click="showAlert">
          {{ $t("lang.rms.fed.application") }}
        </gp-button>
      </div>
    </gp-dialog>

    <gp-dialog :title="$t('lang.rms.fed.replay.tip')" :visible.sync="alertVisable" border>
      <gp-alert
        :title="$t('lang.rms.web.map.version.nonReleasedMapApplicationWarning')"
        type="error"
        :closable="false"
      ></gp-alert>
      <gp-alert
        v-if="activeType == 0"
        :title="$t('lang.rms.web.map.version.takeEffectAfterRestartTip')"
        type="error"
        :closable="false"
      ></gp-alert>
      <gp-alert
        v-if="activeType == 1"
        :title="$t('lang.rms.web.map.version.ChooseEffectImmediately')"
        type="error"
        :closable="false"
      ></gp-alert>
      <gp-alert
        v-if="activeType == 2"
        :title="$t('lang.rms.fed.reloadFuncCellAndAreaInfo')"
        type="error"
        :closable="false"
      ></gp-alert>
      <span slot="footer" class="dialog-footer">
        <gp-button @click="alertVisable = false" plain>{{ $t("lang.common.cancel") }}</gp-button>
        <gp-button type="primary" @click="submit">{{ $t("lang.rms.fed.confirm") }}</gp-button>
      </span>
    </gp-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

export default {
  name: "DialogApplicationMap",
  data() {
    return {
      currentMapName: "",
      remark: "",
      disable: false,
      activeType: 0,
      alertVisable: false,
    };
  },
  computed: {
    ...mapState("mapManagement", ["dialogApplicationMap"]),
    visible: {
      get() {
        return this.dialogApplicationMap.visible;
      },
      set(val) {
        const { visible } = this.dialogApplicationMap;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogApplicationMap.rowData;
    },
  },
  activated() {
    this.getCurrentMap();
  },
  methods: {
    ...mapMutations("mapManagement", ["hideDialog"]),
    close() {
      this.remark = "";
    },
    showAlert() {
      this.alertVisable = true;
    },
    submit() {
      this.alertVisable = false;
      this.disable = true;
      $req
        .post("/athena/map/version/active", {
          mapId: this.rowData.id,
          activeType: this.activeType,
        })
        .then(res => {
          this.reqSuccess(res.msg);
        })
        .catch(e => {
          this.disable = false;
        });
    },
    reqSuccess(msg) {
      this.disable = false;
      this.visible = false;
      this.$emit("refreshList");
      msg = $utils.Tools.transMsgLang(msg);
      this.$success(msg);
    },
    getCurrentMap() {
      $req.post("/athena/map/version/current").then(res => {
        const data = res.data;
        this.currentMapName = data.name;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.tips {
  margin-bottom: 10px;
  cursor: default;

  &:hover {
    color: #409eff;
  }
}

.danger {
  margin-bottom: 15px;
  cursor: default;

  &:hover {
    color: #f56c6c;
  }
}

.form-tips {
  .gp-link {
    line-height: 20px;
  }

  .tips,
  .danger {
    margin-bottom: 0;
  }
}
.geek-dialog-application-map {
  .gp-radio {
    margin-right: 10px !important;
    margin-left: 0px !important;
    margin-bottom: 10px !important;
  }
}
</style>
