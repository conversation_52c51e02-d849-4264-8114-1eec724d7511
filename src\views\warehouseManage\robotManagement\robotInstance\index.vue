<template>
  <section class="robot-manage-panel-wrap">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" class="instance-search-form">
      <template #robotId="{}">
        <gp-input
          v-model="robotId"
          type="number"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
          clearable
          onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
        />
      </template>
    </geek-customize-form>
    <div class="robot-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-view="rowView"
        @row-add="rowAdd"
        @row-edit="rowEdit"
        @row-del="rowDel"
      >
      </geek-customize-table>
    </div>
    <EditDialog ref="editDialog" :robotTypes="robotTypes" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "RobotInstance",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      robotTypes: [],
      robotId: "",
      form: {
        robotId: "",
        hostCode: "",
        robotModelId: "",
        manageStatus: "",
        workStatus: "",
        controlStatus: "",
        commStatus: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          robotId: {
            label: "lang.mb.robotManage.robotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotID",
            slotName: "robotId",
            // rules: [
            //   {
            //     trigger: "change",
            //     validator: (rule, value, callback) => {
            //       if (!value) callback();
            //       else if (!/^[1-9]\d*$/.test(value)) {
            //         callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
            //       } else callback();
            //     },
            //   },
            // ],
          },
          hostCode: {
            label: "lang.rms.api.result.warehouse.robotAlias",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotAlias",
          },
          robotModelId: {
            label: "lang.rms.api.result.warehouse.robotModel",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
          manageStatus: {
            label: "lang.rms.api.result.warehouse.robotManageStatus",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              { value: "", label: "lang.rms.fed.whole" },
              { value: "UNREGISTERED", label: "lang.rms.api.result.warehouse.unRegister" },
              { value: "REGISTERED", label: "lang.rms.api.result.warehouse.register" },
              { value: "BLOCKED", label: "lang.rms.api.result.warehouse.stop" },
            ],
          },
          workStatus: {
            label: "lang.rms.api.result.warehouse.robotWorkStatus",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              { value: "", label: "lang.rms.fed.whole" },
              { value: "NORMAL", label: "lang.rms.api.result.warehouse.normalPresence" },
              { value: "SLEEPING", label: "lang.rms.api.result.warehouse.sleep" },
              { value: "REMOVE_FROM_SYSTEM", label: "lang.rms.api.result.warehouse.departure" },
            ],
          },
          controlStatus: {
            label: "lang.rms.api.result.warehouse.lockedStatus",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              { value: "", label: "lang.rms.fed.whole" },
              { value: "NORMAL", label: "lang.rms.api.result.warehouse.normal" },
              { value: "STOP", label: "lang.rms.api.result.warehouse.robotStop" },
              { value: "SUSPEND", label: "lang.rms.api.result.warehouse.robotStopAndStopTask" },
              { value: "LOCK", label: "lang.rms.api.result.warehouse.robotLock" },
            ],
          },
          commStatus: {
            label: "lang.rms.api.result.warehouse.connectionStatus",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              { value: "", label: "lang.rms.fed.whole" },
              { value: "NORMAL", label: "lang.venus.common.dict.onlineStatus.online" },
              { value: "OFFLINE", label: "lang.venus.common.dict.onlineStatus.dropLine" },
              { value: "DISCONNECTED", label: "lang.venus.common.dict.onlineStatus.reconecting" },
            ],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {},
        // actions: [
        //   {
        //     label: "lang.rms.api.result.warehouse.createRobotModelInstance",
        //     type: "primary",
        //     handler: "row-add",
        //   },
        // ],
        columns: [
          { label: "lang.mb.robotManage.robotId", prop: "robotId" },
          {
            label: "lang.rms.api.result.warehouse.robotAlias",
            prop: "hostCode",
          },
          {
            label: "lang.rms.api.result.warehouse.robotModel",
            prop: "robotModelEntity",
            formatter: (row, column) => {
              const cellValue = row[column];
              return cellValue?.product;
            },
          },
          {
            label: "lang.rms.api.result.warehouse.robotManageStatus",
            prop: "manageStatus",
            formatter: (row, column) => {
              const cellValue = row[column];
              switch (cellValue) {
                case "UNREGISTERED":
                  return this.$t("lang.rms.api.result.warehouse.unRegister");
                case "REGISTERED":
                  return this.$t("lang.rms.api.result.warehouse.register");
                case "BLOCKED":
                  return this.$t("lang.rms.api.result.warehouse.stop");
                default:
                  return "--";
              }
            },
          },
          {
            label: "lang.rms.api.result.warehouse.robotWorkStatus",
            prop: "workStatus",
            formatter: (row, column) => {
              const cellValue = row[column];
              switch (cellValue) {
                case "NORMAL":
                  return this.$t("lang.rms.api.result.warehouse.normalPresence");
                case "SLEEPING":
                  return this.$t("lang.rms.api.result.warehouse.sleep");
                case "REMOVE_FROM_SYSTEM":
                  return this.$t("lang.rms.api.result.warehouse.departure");
                default:
                  return "--";
              }
            },
          },
          {
            label: "lang.rms.api.result.warehouse.lockedStatus",
            prop: "controlStatus",
            formatter: (row, column) => {
              const cellValue = row[column];
              switch (cellValue) {
                case "NORMAL":
                  return this.$t("lang.rms.api.result.warehouse.normal");
                case "STOP":
                  return this.$t("lang.rms.api.result.warehouse.robotStop");
                case "SUSPEND":
                  return this.$t("lang.rms.api.result.warehouse.robotStopAndStopTask");
                case "LOCK":
                  return this.$t("lang.rms.api.result.warehouse.robotLock");
                default:
                  return "--";
              }
            },
          },
          {
            label: "lang.rms.api.result.warehouse.connectionStatus",
            prop: "commStatus",
            formatter: (row, column) => {
              const cellValue = row[column];
              switch (cellValue) {
                case "NORMAL":
                  return this.$t("lang.venus.common.dict.onlineStatus.online");
                case "OFFLINE":
                  return this.$t("lang.venus.common.dict.onlineStatus.dropLine");
                case "DISCONNECTED":
                  return this.$t("lang.venus.common.dict.onlineStatus.reconecting");
                default:
                  return "--";
              }
            },
          },
          { label: "lang.rms.api.result.warehouse.robotFirmwareVersion", prop: "firmwareVersion" },
          { label: "lang.rms.api.result.warehouse.productionBatch", prop: "productionBatch" },
          {
            label: "lang.rms.api.result.warehouse.lastChargingTime",
            prop: "lastChargeTime",
            align: "center",
            width: "130",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
              else return "--";
            },
          },
          {
            label: "lang.rms.api.result.warehouse.powerOnTime",
            prop: "uptime",
            align: "center",
            width: "130",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
              else return "--";
            },
          },
          {
            label: "lang.rms.api.result.warehouse.offTime",
            prop: "lastCommTime",
            align: "center",
            width: "130",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
              else return "--";
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "200",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
                type: "danger",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getRobotTypes();
    this.getTableList();
  },
  methods: {
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowAdd() {
      this.$refs.editDialog.open("add");
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/robotDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.form.robotId = this.robotId;
      this.getTableList();
    },
    onReset(val) {
      this.robotId = "";
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/robotPageList?pageSize=${pageSize}&currentPage=${currentPage}`;

      $req.post(url, this.form).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          total: result.recordCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    getRobotTypes() {
      $req.post("/athena/map/version/findRobotType").then(res => {
        const data = res?.data || [];
        const robotTypes = data.map(item => {
          return { label: item.displayName, value: item.robotModelId };
        });
        this.formConfig.configs.robotModelId.options = robotTypes;
        this.robotTypes = robotTypes;
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
  :deep(.el-form-item) {
    width: 146px;
  }
}
</style>
