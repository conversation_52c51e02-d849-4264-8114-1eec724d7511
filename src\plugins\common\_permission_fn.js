/* ! <AUTHOR> at 2021/03 */

export default {
  permission(ButtonCodeTranslations, code) {
    const value = ButtonCodeTranslations[code];
    if (!value) return true;

    return $app.$store.state.btnAuth.indexOf(value) !== -1;
  },

  noPermission(ConfigButtonCodeTranslations, code) {
    const RMSConfig = $utils.Data.getRMSConfig();
    let key = "";

    switch (code) {
      case "MonitorRobotGoSomeWhere":
        key = ConfigButtonCodeTranslations["disableGoSomewhere"];
        return !RMSConfig[key];
      case "MonitorRobotGoCharging":
        key = ConfigButtonCodeTranslations["disableGoCharging"];
        return !RMSConfig[key];
      case "MonitorRobotUpdateFloor":
        key = ConfigButtonCodeTranslations["disableUpdateFloor"];
        return !RMSConfig[key];
      case "MonitorRobotJoin":
        key = ConfigButtonCodeTranslations["disableRecoverRobot"];
        return !RMSConfig[key];
      case "MonitorRobotRemove":
        key = ConfigButtonCodeTranslations["disableGoRemoveRobot"];
        return !RMSConfig[key];
      case "MonitorRobotRestart":
        key = ConfigButtonCodeTranslations["disableRobotRestart"];
        return !RMSConfig[key];
      case "MonitorRobotUnlock":
        key = ConfigButtonCodeTranslations["disableRobotUnlock"];
        return !RMSConfig[key];
      case "MonitorShelfUpdate":
        key = ConfigButtonCodeTranslations["disableChangeShelf"];
        return !RMSConfig[key];
      case "MonitorShelfMove":
        key = ConfigButtonCodeTranslations["disableMoveShelf"];
        return !RMSConfig[key];
      case "MonitorShelfUpdateAngel":
        key = ConfigButtonCodeTranslations["disableChangeShelfAngle"];
        return !RMSConfig[key];
      case "MonitorChargeDisable":
        key = ConfigButtonCodeTranslations["disableChargeDisable"];
        return !RMSConfig[key];
      case "MonitorShelfReturnPlacement":
        key = ConfigButtonCodeTranslations["disableReturnPlacementShelf"];
        return !RMSConfig[key];
      case "MonitorRobotResume":
          key = ConfigButtonCodeTranslations["disableResumeRobot"];
          return !RMSConfig[key];
      default:
        key = ConfigButtonCodeTranslations[code];
        if (key === undefined) return true;
        return RMSConfig[key];
    }
  },
};
