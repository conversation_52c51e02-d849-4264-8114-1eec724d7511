<template>
  <div class="paramsChange">
    <gp-button type="text" @click="dialogVisible = true">点击打开 Dialog</gp-button>
    <gp-dialog title="系统参数配置" :visible.sync="dialogVisible" width="30%">
      <gp-alert title="这是一段消息提示通知" type="warning" show-icon :closable="false"> </gp-alert>
      <p class="content">有未提交的配置变化，要应用变化吗？</p>
      <span slot="footer" class="dialog-footer">
        <gp-button type="primary" @click="apply">应用</gp-button>
        <gp-button @click="dialogVisible = false">取 消</gp-button>
      </span>
    </gp-dialog>
  </div>
</template>

<script>
export default {
  name: "DialogParamsChange",
  data() {
    return {
      dialogVisible: false,
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="less" scoped>
.paramsChange :deep(.gp-dialog__body) {
  padding: 0px;
  margin-top: 10px;
  overflow: hidden;
}
.paramsChange :deep(.gp-dialog) {
  border-radius: 12px;
}
:deep(.gp-dialog__title) {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #323334;
}
:deep(.gp-alert__title) {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #313234;
}
:deep(.gp-alert) {
  background: #fffbe6;
}
.content {
  margin: 26px 26px;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #323334;
}
</style>
