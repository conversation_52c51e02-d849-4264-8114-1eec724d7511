<template>
  <div class="geek-form-table-con shelf-manage-panel-wrap">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="total-number-box">
      <span class="total-number-item"
        ><strong>{{ $t("lang.rms.fed.shelfFointQuery.totalPoints") }}</strong
        >：<strong>{{ totalCount }}</strong></span
      >
      <span class="total-number-item"
        ><strong>{{ $t("lang.rms.fed.shelfFointQuery.normalPoints") }}</strong
        >：<strong>{{ commonCount }}</strong></span
      >
      <span class="total-number-item"
        ><strong>{{ $t("lang.rms.fed.shelfFointQuery.totalOccupyPoints") }}</strong
        >：<strong>{{ occupyCount }}</strong></span
      >
      <span class="total-number-item"
        ><strong>{{ $t("lang.rms.fed.shelfFointQuery.normalOccupyPoints") }}</strong
        >：<strong>{{ commonOccupyCount }}</strong></span
      >
      <span class="total-number-item"
        ><strong>{{ $t("lang.rms.fed.shelfFointQuery.lockedPoints") }}</strong
        >：<strong>{{ lockedCount }}</strong></span
      >
    </div>
    <div class="table-content shelf-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 搜索条件
      form: {
        latticeCode: "",
        boxCode: "",
        rackCode: "",
        latticeType: "",
        latticeFlag: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          // 货位编码
          latticeCode: {
            label: "lang.rms.fed.latticePage.latticeCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          // 货箱编码
          boxCode: {
            label: "lang.rms.fed.latticePage.boxCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          // 所属货架
          rackCode: {
            label: "lang.rms.fed.latticePage.belongShelf",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          // 货位类型
          latticeType: {
            label: "lang.rms.fed.latticePage.latticeType",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.choose", //请选择
              },
              {
                value: "TRANSFER",
                label: "lang.rms.fed.lattice.type.transfer", //缓存货位
              },
              {
                value: "PICK",
                label: "lang.rms.fed.lattice.type.pick", //拣选货位
              },
              {
                value: "COMMON",
                label: "lang.rms.fed.lattice.type.common", //普通货位
              },
              {
                value: "STATION_TRANSFER",
                label: "lang.rms.fed.lattice.type.station.transfer", //工位缓存货拉
              },
              {
                value: "GRIPPER",
                label: "lang.rms.fed.lattice.type.station.gripper", //提升机货位
              },
              {
                value: "PUT",
                label: "lang.rms.fed.lattice.type.put", //放料货位
              },
            ],
          },
          // 货位状态
          latticeFlag: {
            label: "lang.rms.fed.latticePage.latticeStatus",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.choose", //请选择
              },
              {
                value: "NORMAL",
                label: "lang.rms.fed.latticePage.latticeStatus.normal", //正常
              },
              {
                value: "LOCKED",
                label: "lang.rms.fed.latticePage.latticeStatus.locked", //锁定
              },
            ],
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      value: null,
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fed.latticePage.latticeCode",
            prop: "latticeCode",
            width: "100",
          },
          {
            label: "lang.rms.fed.latticePage.boxCode",
            prop: "boxCode",
          },
          {
            label: "lang.rms.fed.latticePage.latticeStatus",
            prop: "latticeFlag",
          },
          {
            label: "lang.rms.fed.latticePage.latticeType",
            prop: "latticeType",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          {
            label: "lang.rms.fed.latticePage.belongShelf",
            prop: "rackCode",
            width: "110",
          },
          {
            label: "lang.rms.fed.latticePage.shelfSide",
            prop: "shelfSide",
          },
          {
            label: "lang.rms.fed.latticePage.layer",
            prop: "layer",
            width: "80",
          },
          {
            label: "lang.rms.fed.latticePage.column",
            prop: "layerColumnNum",
            width: "80",
          },
        ]
      },
      tableData: [], // 设置table的数据
      total: 0, // 当前数据总数
      totalCount: 0,
      occupyCount: 0,
      commonCount: 0,
      commonOccupyCount: 0,
      lockedCount: 0,
    };
  },
  computed: {
    tableList() {
      let obj = {
        TRANSFER: "lang.rms.fed.lattice.type.transfer",
        PICK: "lang.rms.fed.lattice.type.pick",
        COMMON: "lang.rms.fed.lattice.type.common",
        STATION_TRANSFER: "lang.rms.fed.lattice.type.station.transfer",
        GRIPPER: "lang.rms.fed.lattice.type.station.gripper",
        PUT: "lang.rms.fed.lattice.type.put",
      };
      let keyWord = key => {
        return obj[key];
      };

      return this.tableData.map(item => {
        const nItem = item;
        if (nItem.latticeFlag === "NORMAL") {
          nItem.latticeFlag = this.$t("lang.rms.fed.latticePage.latticeStatus.normal");
        } else {
          nItem.latticeFlag = this.$t("lang.rms.fed.latticePage.latticeStatus.locked");
        }
        nItem.latticeType = keyWord(item.latticeType);
        return nItem;
      });
    },
  },
  activated() {
    this.getTableList();
  },
  methods: {
    pageChange(page) {
      this.tablePage = Object.assign({}, this.tablePage, page);
      this.getTableList();
    },
    // 分页
    currentPageChange(val) {
      this.tablePage.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.tablePage.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      if (!this.form.latticeFlag) this.form.latticeFlag = "";
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const params = this.form;
      params.currentPage = this.tablePage.currentPage;
      params.pageSize = this.tablePage.pageSize;
      $req.post("/athena/lattice/findAll", params).then(res => {
        console.log("res", res);
        let result = res.data;
        if (result != null) {
          console.log("result", result);
          this.totalCount = result.totalCount;
          this.occupyCount = result.occupyCount;
          this.commonCount = result.commonCount;
          this.commonOccupyCount = result.commonOccupyCount;
          this.lockedCount = result.lockedCount;
          let list = result.page.recordList;
          this.tableData = list;
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.page.currentPage || 1,
            total: result.page.recordCount || 0,
          });
          this.total = result.page.recordCount;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.total-number-box {
  padding: 0 10px;
  margin-bottom: 10px;
  border-bottom: 3px solid #efefef;
  height: 40px;
  line-height: 40px;

  .total-number-item {
    margin-right: 10px;

    strong {
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
