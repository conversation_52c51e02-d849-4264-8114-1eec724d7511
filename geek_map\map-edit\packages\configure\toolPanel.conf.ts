import { ToolPanelType } from "@packages/type/editUiType";
import * as ADD from "./panelModel/add";
import * as GRID from "./panelModel/grid";
import * as TOPOGY from "./panelModel/topology";
import * as AREA from "./panelModel/area";
import * as VISIBLE from "./panelModel/visible";
/**
 * EditMap文案
 */
const TITLE_CONF: ToolPanelType = {
  option: {
    title: "Edit Map",
    name: "title",
    className: "editMapToolTitleStyle",
  },
};

/**
 * EditDevice文案(PPP 工作站设备编辑)
 */
const DEVICE_TITLE_CONF: ToolPanelType = {
  option: {
    title: "Edit Device",
    name: "title",
    className: "editMapToolTitleStyle",
  },
};

/**
 * 居中
 */
const CENTER_CONF: ToolPanelType = {
  option: {
    icon: "map-font-zhongxin",
    title: "lang.rms.fed.center",
    name: "center",
    describe: "lang.rms.fed.mapCenter",
    eventName: "map:center",
  },
};

/**
 * 重置
 */
const RESET_CONF: ToolPanelType = {
  option: {
    icon: "map-font-chushihua",
    title: "lang.rms.fed.reset",
    name: "center",
    describe: "lang.rms.fed.resetCurrentOperation",
    eventName: "map:reset",
    disabled() {
      return false;
    },
  },
};

/**
 * 添加元素
 */
const ADD_CONF: ToolPanelType = {
  option: {
    icon: "map-font-tianjia",
    name: "add",
    title: "lang.rms.fed.addTo1",
    eventName: "map:defAult",
    active: true,
  },
  config: {
    // group: true,
  },
  children: [
    ADD.ADD_CELL,
    ADD.ADD_BUMADIAN,
    ADD.ADD_SHELF,
    ADD.ADD_BLOCK,
    ADD.ADD_TRAYRACK,
    ADD.ADD_CELLBATCH,
    ADD.FORMAT_BRUSH,
  ],
};

/**
 * 添加设备
 */
const ADD_DEVICE: ToolPanelType = {
  option: {
    icon: "map-font-shebei1",
    name: "addDevice",
    title: "lang.rms.config.group.dmp",
    eventName: "map:defAult",
  },
  config: {
    // group: true,
  },
  children: [ADD.ADD_STATION, ADD.ADD_CHARGING, ADD.ADD_REFLECTOR, ADD.ADD_ELEVATOR, ADD.ADD_SAFE],
  // children: [ADD.ADD_STATION, ADD.ADD_CHARGING, ADD.ADD_REFLECTOR,ADD.ADD_ELEVATOR],
};

// 栅格
const ADD_GRID: ToolPanelType = {
  option: {
    icon: "map-font-zhage1",
    name: "addGrid",
    title: "lang.rms.fed.grid",
    eventName: "map:defAult",
  },
  config: {
    // group: true,
  },
  children: [
    GRID.ADD_GRID_WEST,
    GRID.ADD_GRID_EAST,
    GRID.ADD_GRID_NORTH,
    GRID.ADD_GRID_SOUTH,
    GRID.ADD_GRID_WEST_NORTH,
    GRID.ADD_GRID_WEST_SOUTH,
    GRID.ADD_GRID_EAST_SOUTH,
    GRID.ADD_GRID_EAST_NORTH,
    GRID.ADD_GRID_EAST_WEST,
    GRID.ADD_GRID_EAST_NORTH,
    GRID.ADD_GRID_SOUTH_NORTH,
    GRID.ADD_GRID_EAST_WEST_NORTH,
    GRID.ADD_GRID_EAST_WEST_SOUTH,
    GRID.ADD_GRID_WEST_NORTH_SOUTH,
    GRID.ADD_GRID_EAST_SOUTH_NORTH,
    GRID.ADD_GRID_EAST_WEST_SOUTH_NORTH,
    GRID.ADD_GRID_NONE,
  ],
};

// 拓扑
const ADD_TOPOLOGY: ToolPanelType = {
  option: {
    icon: "map-font-tuopu",
    name: "addTopology",
    title: "lang.rms.fed.topology",
    eventName: "map:defAult",
  },
  config: {
    // group: true,
  },
  children: [TOPOGY.ADD_CURVE, TOPOGY.ADD_LINE, TOPOGY.ADD_ARC_CLOCKWISE, TOPOGY.ADD_ARC_ANTI_CLOCKWISE],
};

// 区域
export const ADD_AREA: ToolPanelType = {
  option: {
    icon: "map-font-polygon",
    name: "addAreaMain",
    title: "lang.rms.fed.area",
    eventName: "map:defAult",
  },
  config: {
    // group: true,
  },
  children: [
    // AREA.AREA_ADD
    AREA.ADD_TRAFFIC_LIGHT_AREA,
    AREA.ADD_STOP_AREA,
    AREA.ADD_TRAFFIC_CONTROL_AREA,
    AREA.ADD_HIGHWAY_AREA,
    AREA.ADD_ROBOT_AREA,
    AREA.ADD_SHELF_AREA,
    AREA.ADD_TASK_CONTROL_AREA,
    AREA.ADD_NO_STAY_AREA,
    AREA.ADD_SORTING_AREA,
    // AREA.ADD_AREA_EMPTYING,
    AREA.ADD_AREA_CLEAR_ROBOT,
    AREA.ADD_RESTRICT_BIG_ARC_AREA,
    AREA.ADD_BLOCK_AREA,
    AREA.ADD_ONEWAY_STREET,
    AREA.ADD_GATHERING_AREA,
    AREA.ADD_STATIC_SPEED_LIMIT_AREA,
    AREA.ADD_REAL_TIME_SPEED_LIMIT_AREA,
    AREA.ADD_OBSTACLE_AVOIDANCE_AREA,
    // AREA.ADD_PLC_LIMIT_AREA,
    // AREA.ADD_HIGH_ALTITUDE_OBSTACLE_AREA,
    AREA.ADD_SLAM_NAVIGATION_AREA,
    AREA.ADD_CLOSE_OBSTACLE_AVOIDANCE_AREA,
    AREA.ADD_CUSTOM,
    AREA.ADD_RESOURCE_ISOLATION_AREA,
  ],
  // areaOptions:[
  //   AREA.ADD_TRAFFIC_LIGHT_AREA,
  //   AREA.ADD_STOP_AREA,
  //   AREA.ADD_TRAFFIC_CONTROL_AREA,
  //   AREA.ADD_HIGHWAY_AREA,
  //   AREA.ADD_ROBOT_AREA,
  //   AREA.ADD_SHELF_AREA,
  //   AREA.ADD_TASK_CONTROL_AREA,
  //   AREA.ADD_NO_STAY_AREA,
  //   AREA.ADD_SORTING_AREA,
  //   AREA.ADD_AREA_EMPTYING,
  //   AREA.ADD_BLOCK_AREA,
  //   AREA.ADD_ONEWAY_STREET,
  //   AREA.ADD_RESTRICT_BIG_ARC_AREA,
  //   AREA.ADD_CUSTOM
  // ]
};

/**
 * 顶部按钮组数据
 */
export const toolPanelList: ToolPanelType = {
  config: {
    border: true,
    defActive: true,
  },
  children: [TITLE_CONF, ADD_CONF, ADD_DEVICE, ADD_GRID, ADD_TOPOLOGY, ADD_AREA, CENTER_CONF, RESET_CONF],
};

/**
 * 左侧按钮组数据
 */
export const showPanelList: ToolPanelType = {
  config: {
    align: "left",
    model: "popper",
    border: true,
  },
  children: [
    VISIBLE.VISIBLE_BOXSELECTION,
    VISIBLE.VISIBLE_ROAD_BOTHLOAD,
    VISIBLE.VISIBLE_ROAD_UNLOAD,
    VISIBLE.VISIBLE_ROAD_LOAD,
    VISIBLE.VISIBLE_ROBOT,
    VISIBLE.VISIBLE_REFLECTOR,
    VISIBLE.VISIBLE_GUIDE,
    VISIBLE.VISIBLE_REVOKE,
    VISIBLE.VISIBLE_REVOKE_CANCEL,
    VISIBLE.VISIBLE_SAVE,
  ],
};
