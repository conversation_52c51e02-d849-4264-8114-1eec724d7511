<template>
  <gp-dialog
    :title="$t('lang.rms.fed.buttonEdit')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    border
    width="520px"
    class="stationFormDialog"
  >
    <geek-customize-form ref="stationForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <gp-button @click="close" plain>{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </gp-button>
    </span>
  </gp-dialog>
</template>

<script>
export default {
  name: "EditStationDialog",
  props: ["robotTypes"],
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "150px",
          labelPosition: "right",
        },
        configs: {
          place: {
            label: "lang.rms.fed.WorkStationFace",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "east",
                label: "lang.rms.fed.east",
              },
              {
                value: "south",
                label: "lang.rms.fed.south",
              },
              {
                value: "west",
                label: "lang.rms.fed.west",
              },
              {
                value: "north",
                label: "lang.rms.fed.north",
              },
            ],
          },
          maxRobotQueueSize: {
            label: "lang.rms.fed.maxQueueNumber",
            default: "",
            disabled: false,
            tag: "input-number",
            "controls-position": "right",
          },
          // 允许机器人类型
          allowedRobotType: {
            label: "lang.rms.fed.WorkStationAllowedRobotTypes",
            default: [],
            multiple: true,
            tag: "select",
            options: this.robotTypes,
          },
          // 是否开启提前呼叫机器人
          // preDeployRobot: {
          //   label: "lang.rms.fed.isPreDeployRobot",
          //   default: 0,
          //   tag: "select",
          //   placeholder: "lang.rms.fed.pleaseChoose",
          //   options: [
          //     {
          //       value: 0,
          //       label: "lang.rms.fed.no",
          //     },
          //     {
          //       value: 1,
          //       label: "lang.rms.fed.yes",
          //     },
          //   ],
          // },
          // 排队等待点数量
          waitCellNumber: {
            label: "lang.rms.fed.WorkStationWaitCellNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 负载顶升高度
          loadHeight: {
            label: "lang.rms.fed.WorkStationLoadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
          // 空载顶升高度
          unloadHeight: {
            label: "lang.rms.fed.WorkStationUnloadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
          // 货架是否等待还箱
          waitingReturnBox: {
            isHidden: true,
            label: "lang.rms.fed.waitingReturnBox",
            default: 0,
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
              {
                value: null,
                label: "--",
              },
            ],
          },
        },
      },
    };
  },
  watch: {
    robotTypes(arr) {
      this.formConfig.configs.allowedRobotType.options = arr;
    },
  },
  methods: {
    open(data) {
      const params = {
        place: data.place || "",
        maxRobotQueueSize: data.maxRobotQueueSize || 0,
        allowedRobotType: data?.allowedRobotType ? data.allowedRobotType.split(",") : [],
        preDeployRobot: data.preDeployRobot || 0,
        waitCellNumber: data.waitCellNumber || 0,
        loadHeight: data.loadHeight || 0,
        unloadHeight: data.unloadHeight || 0,
        waitingReturnBox: data.waitingReturnBox,
      };
      this.rowData = data;
      if (data.type === 7) {
        this.formConfig.configs.waitingReturnBox.isHidden = false;
      } else {
        this.formConfig.configs.waitingReturnBox.isHidden = true;
      }

      if (data?.stationPoints && data.stationPoints.length > 0) {
        this.formConfig.configs.maxRobotQueueSize.disabled = true;
      } else {
        this.formConfig.configs.maxRobotQueueSize.disabled = false;
      }
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.stationForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      const formData = this.$refs.stationForm.getData();
      let params = Object.assign({}, formData, {
        stationId: this.rowData.stationId,
        allowedRobotType: formData.allowedRobotType.join(","),
      });

      if (this.rowData.type !== 7) {
        delete params["waitingReturnBox"];
      }

      $req.post("/athena/station/management/updateStation", params).then(res => {
        this.$success();
        this.dialogVisible = false;
        this.$emit("updateMainList");
      });
    },
  },
};
</script>

<style lang="less" scoped>
.stationFormDialog {
  ::v-deep .gp-input-number {
    width: 100%;
  }
}
</style>
