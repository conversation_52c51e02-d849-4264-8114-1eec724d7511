<template>
  <geek-main-structure class="task-manage-operation flex flex-col">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" @on-export="onExport" />
    </div>
    <div v-if="tabList.length" class="tabs-content mt10 mt10">
      <gp-tabs v-model="activeTab" @tab-click="handleTabClick">
        <gp-tab-pane v-for="tab in tabList" :key="tab" :label="tab" :name="tab"></gp-tab-pane>
      </gp-tabs>
    </div>
    <div class="table-content flex-1">
      <gp-rich-table
        ref="myTable"
        :data="tableData"
        row-key="uuid"
        style="width: 100%"
        lazy
        :load="loadChildTask"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :virtualized="false"
        :page="page"
        :show-setting="false"
        @pageChange="pageChange"
        :columns="columns"
        @expand-change="recordExpandChange"
      >
        <template #exceptionInfo="scope">
          <template v-if="scope.row.excetpionInfo">
            <gp-tooltip class="itme" effect="dark" placement="left">
              <template #content>
                <div>
                  <pre class="ui-preBlock">
                    {{ JSON.stringify(JSON.parse(scope.row.excetpionInfo), null, 2) }}
                  </pre>
                </div>
              </template>
              <gp-tag class="ell w100">{{ scope.row.excetpionInfo }}</gp-tag>
            </gp-tooltip>
          </template>
        </template>
        <template #operateBth="scope" v-if="!isRoleGuest">
          <gp-popover
            v-if="!scope.row.jobStageId && String(scope.row.cancelOper) === '1'"
            placement="top"
            :title="$t('lang.rms.fed.isSure', [$t('lang.rms.fed.cancelTask')])"
            footer
            iconClass="gp-icon-warning"
            iconColor="#FF7F08"
            :on-ok="() => handlerClose(scope)"
          >
            <gp-link slot="reference" type="danger" size="small">{{ $t("lang.rms.fed.cancel") }}</gp-link>
          </gp-popover>
          <gp-popover
            v-if="!scope.row.jobStageId && String(scope.row.deleteOper) === '1'"
            placement="top"
            :title="$t('lang.rms.fed.isSure', [$t('lang.rms.fed.deleteTask')])"
            footer
            iconClass="gp-icon-warning"
            iconColor="#FF7F08"
            :on-ok="() => handlerDelete(scope)"
          >
            <gp-link slot="reference" type="danger" size="small">{{ $t("lang.rms.fed.delete") }}</gp-link>
          </gp-popover>
          <template v-if="activeTab !== 'DELIVER_BOX'">
            <gp-link size="small" type="primary" @click="() => handleTaskDetail(scope.row)">
              {{ $t("lang.rms.fed.taskDetail") }}
            </gp-link>
            <gp-link size="small" type="primary" @click="() => exportLog(scope.row)">
              {{ $t("lang.rms.fed.exportLog") }}
            </gp-link>
          </template>
          <template v-else>
            <gp-link v-if="!scope.row.jobId" size="small" type="primary" @click="() => handleTaskDetail(scope.row)">
              {{ $t("lang.rms.fed.taskDetail") }}
            </gp-link>
            <gp-link v-if="!scope.row.jobId" size="small" type="primary" @click="() => exportLog(scope.row)">
              {{ $t("lang.rms.fed.exportLog") }}
            </gp-link>
          </template>
        </template>
      </gp-rich-table>
    </div>
    <task-detail
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :initRow="dialogInitRow"
      @debugModelSearch="debugModelSearch"
    ></task-detail>
  </geek-main-structure>
</template>

<script>
// lang.rms.fed.isSure 是否确定{0}
// lang.rms.fed.cancelTask 取消任务
// lang.rms.fed.deleteTask 删除任务

// 为了避免 不同地方时间显示格式不同 统一处理 已考虑当地时区
const formatter = new Intl.DateTimeFormat("en-US", { year: "numeric", month: "2-digit", day: "2-digit" });
const parts = formatter.formatToParts(new Date());
const dateString = `${parts.find(p => p.type === "month").value}/${parts.find(p => p.type === "day").value}/${
  parts.find(p => p.type === "year").value
}`;
const defaultData = new Date(dateString);

import taskDetail from "./components/taskDetailV2.vue";
import { taskManageColumns } from "./config";
export default {
  components: { taskDetail },
  name: "TaskManage",
  data() {
    return {
      form: {
        stationId: "",
        taskId: "",
        taskState: "",
        hostTaskId: null,
        createTime: [defaultData, defaultData],
        updateTime: [defaultData, defaultData],
        jobType: "DELIVER_SHELF",
      },
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
          "show-message": false, // 林林期望变更，防止报错文案压到下一行
        },
        configs: {
          // 任务iD
          jobId: {
            label: "lang.rms.fed.taskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 任务类型
          jobType: {
            label: "lang.rms.fed.taskType",
            default: ["DELIVER_SHELF", "DELIVER_BOX"],
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
            multiple: true,
            collapseTags: true,
            filterable: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.choose"), trigger: "change" }],
          },
          // 任务状态
          jobState: {
            label: "lang.rms.web.monitor.robot.taskState",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
          // 机器人ID
          robotId: {
            label: "lang.rms.fed.listRobotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 货架ID
          shelfCode: {
            label: "lang.rms.fed.containerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 任务指令
          // jobInstruction: {
          //   label: "lang.rms.fed.taskInstruction",
          //   default: "",
          //   tag: "input",
          //   placeholder: "lang.rms.web.placeHolder",
          // },
          // 充电站ID
          chargeStationId: {
            label: "lang.rms.web.charger.chargerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 工作站ID
          stationId: {
            label: "lang.rms.fed.stationId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 上游任务ID
          hostTaskId: {
            label: "lang.rms.fed.parentTaskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 创建时间
          createTime: {
            label: "lang.rms.web.monitor.robot.taskCreateTime",
            default: [defaultData, defaultData],
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
          // 更新时间
          updateTime: {
            label: "lang.rms.fed.updateTime",
            default: [defaultData, defaultData],
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
          // {
          //   label: "lang.rms.fed.buttonExport",
          //   handler: "on-export",
          //   type: "success",
          // },
        ],
      },
      tableData: [],
      taskDialogList: [],
      detailDialogInfo: null,
      dayTime: 24 * 60 * 60 * 1000 - 1000, // 到当前0点
      cacheRowExpanison: [], // 保存当前页面打开的rowExpansion;
      tabList: ["DELIVER_SHELF", "DELIVER_BOX"],
      activeTab: "DELIVER_SHELF", // 激活的tab
      dialogVisible: false, // 任务详情弹框
      dialogInitRow: {}, // 任务详情内容。
      curTimer: 0,
    };
  },
  computed: {
    columns() {
      return taskManageColumns();
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {
    isRoleGuest(val) {
      if ($utils || $utils.Data) val && this.columns.splice(this.columns.length - 1, 1);
    },
  },
  activated() {
    this.getSearchConditionValue();
    this.getTableList();
  },
  methods: {
    handleTabClick() {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, { jobType: this.activeTab });
      this.getTableList();
    },
    recordExpandChange(row, status) {
      if (status) {
        this.cacheRowExpanison = [...new Set(this.cacheRowExpanison.concat(row.uuid))];
      } else {
        const set = new Set(this.cacheRowExpanison);
        set.delete(row.uuid);
        this.cacheRowExpanison = [...set];
      }
    },
    async handlerDelete({ row }) {
      try {
        const { code } = await $req.post("/athena/task/monitor/conditionsNew/del", {
          jobId: row.jobId,
          jobType: row.jobType,
        });
        if (!code) {
          this.getTableList();
          this.$message.success(this.$t("lang.common.success"));
        }
      } catch (e) {
        return Promise.reject();
      }
    },
    async handlerClose({ row }) {
      try {
        const { code } = await $req.post("/athena/task/monitor/conditionsNew/cancel", {
          jobId: row.jobId,
          jobType: row.jobType,
        });
        if (!code) {
          this.getTableList();
          this.$message.success(this.$t("lang.common.success"));
        }
        return Promise.resolve();
      } catch (e) {
        return Promise.reject();
      }
    },
    loadChildTask(row, treeNode, resolve) {
      const { createTime, updateTime, jobType, ...others } = this.form;

      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      let temporaryUpdateTime = updateTime ? updateTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      if (temporaryUpdateTime && temporaryUpdateTime[1]) temporaryUpdateTime[1] += this.dayTime;
      $req
        .get("/athena/task/monitor/conditionsNew/jobStage", {
          ...(this.form || {}),
          jobId: row.jobId,
          createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
          updateTime: temporaryUpdateTime ? temporaryUpdateTime.join("-") : "",
        })
        .then(res => {
          if (!res.data || !res.data.recordList) return resolve([]);
          return resolve(res.data.recordList.map(i => ({ ...i, uuid: `task-${i.jobStageId}` })));
        });
    },
    getTableList() {
      const { createTime, updateTime, jobType, ...others } = this.form;

      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      let temporaryUpdateTime = updateTime ? updateTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      if (temporaryUpdateTime && temporaryUpdateTime[1]) temporaryUpdateTime[1] += this.dayTime;
      const data = {
        ...others,
        createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
        updateTime: temporaryUpdateTime ? temporaryUpdateTime.join("-") : "",
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage,
        jobType: jobType || "DELIVER_SHELF",
      };

      // if (jobType === "DELIVER_BOX") {
      //   data.warehouseJob = true
      // }

      $req.get("/athena/task/monitor/conditionsNew", data).then(res => {
        let result = res.data;
        if (result != null) {
          let list = result.recordList;
          list.forEach(item => {
            item.uuid = `parent-${item.jobId}`;
            item.detailLoad = false;
            item.exportLoading = false;
            if (item.jobType !== "DELIVER_BOX") return; // 只有这种类型显示子类型；
            item.children = [];
            item.hasChildren = true;
          });
          this.tableData = list;
          this.page.currentPage = result.currentPage || 1;
          this.page.total = result.recordCount || 1;
          this.curTimer = +new Date();
          this.$nextTick(() => this.checkDefaultRowExpand());
        }
      });
    },
    checkDefaultRowExpand() {
      const cache = this.cacheRowExpanison;
      if (!cache.length) return;
      for (let i = 0, item; (item = this.tableData[i++]); ) {
        cache.includes(item.uuid) && this.$refs.myTable.toggleRowExpansion(item, false);
      }
    },
    pageChange(page) {
      this.page = Object.assign({}, this.page, page);
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;
      const { jobType } = val;
      this.tabList = jobType;
      if (!jobType.includes(this.activeTab)) {
        this.activeTab = jobType[0];
      }
      this.form = Object.assign(this.form, val, { jobType: this.activeTab });
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val, { jobType: this.activeTab });
      this.getTableList();
    },
    onExport() {
      const { boxCode, stationId, taskId, taskState, createTime } = this.form;
      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      const data = {
        taskId,
        taskState,
        boxCode,
        stationId,
        createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
      };
      $req.get("/athena/task/monitor/exportTaskExcel", data).then(res => {
        if (res.code === 0) {
          if ($req.isDev) {
            window.open($req.API_URL + res.data);
          } else {
            window.open(window.location.origin + res.data);
          }
        }
      });
    },
    getRowKey(row) {
      let rowID = row.jobStageId || row.jobId;
      return String(rowID);
    },
    async exportLog(row) {
      row.exportLoading = true;
      const params = {
        jobId: row.jobStageId || row.jobId,
      };
      if (row.jobType === "DELIVER_BOX") {
        params.warehouseJob = true;
      }
      const { code, data } = await $req.postParams(`athena/engine/tools/job/generateJobLog`, params);
      row.exportLoading = false;
      if (code === 0) {
        const exportUrl = `${location.origin}/${data.uri}`;
        // 取url最后一个/后面的字符串作为文件名
        const fileName = exportUrl.substring(exportUrl.lastIndexOf("/") + 1);
        // 下载url
        const a = document.createElement("a");
        a.href = exportUrl;
        a.download = fileName;
        a.target = "_blank";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },
    getSearchConditionValue() {
      $req.get("/athena/task/monitor/querySearchConditionValue").then(res => {
        const jobTypeList = [];
        const jobStateList = [];
        res.data.jobType.forEach(item => {
          jobTypeList.push({
            label: item,
            value: item,
          });
        });
        res.data.jobState.forEach(item => {
          jobStateList.push({
            label: item,
            value: item,
          });
        });

        this.formConfig.configs.jobType.options = jobTypeList;
        this.formConfig.configs.jobState.options = jobStateList;
      });
    },

    async handleTaskDetail(row, params = {}) {
      row.detailLoad = true;
      const paramsObj = {
        jobId: row.jobStageId || row.jobId,
      };
      if (["DELIVER_BOX", "DELIVER_CONTAINER"].includes(row.jobType)) {
        paramsObj.warehouseJob = true;
      }

      const { data } = await $req.get("/athena/engine/tools/job/showJobDetail", {
        ...paramsObj,
        ...params,
      });
      row.detailLoad = false;
      this.dialogVisible = true;
      this.dialogInitRow = data ? { ...row, ...data } : { ...row };
    },

    debugModelSearch() {
      this.handleTaskDetail(this.dialogInitRow, {
        traceLevel: "debug",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.task-state {
  display: block;
}

.ui-preBlock {
  padding: 15px;
  background: #2d2d2d;
  text-align: left;
  color: #fff;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow: auto;
}

.icon-span {
  position: absolute;
  color: #ff0000;
  right: 0;
  top: 12px;
  transform: rotate(30deg);
  font-size: 14px;
  border: 1px dashed #ff0000;
  border-radius: 5px;
  background: rgba(255, 0, 0, 0.05);
}
</style>
