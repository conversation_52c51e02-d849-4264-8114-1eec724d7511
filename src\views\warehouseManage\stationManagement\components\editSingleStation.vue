<template>
  <gp-dialog :title="$t('lang.rms.fed.buttonEdit')" :visible.sync="dialogVisible" border width="520px">
    <geek-customize-form ref="stationForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <gp-button @click="close" plain>{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </gp-button>
    </span>
  </gp-dialog>
</template>

<script>
export default {
  name: "EditSingleStationDialog",
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "138px",
          labelPosition: "right",
        },
        configs: {
          maxRobotQueueSize: {
            label: "lang.rms.fed.maxQueueNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
        },
      },
    };
  },
  methods: {
    open(data) {
      const params = {
        maxRobotQueueSize: data.maxRobotQueueSize || 0,
      };
      this.rowData = data;
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.stationForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      const formData = this.$refs.stationForm.getData();

      const params = Object.assign({}, formData, {
        mapId: this.rowData.mapId,
        stationId: this.rowData.stationId,
      });
      $req.post("/athena/station/updateMaxRobotQueueSize", params).then(res => {
        this.$success();
        this.dialogVisible = false;
        this.$emit("updateMainList");
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
