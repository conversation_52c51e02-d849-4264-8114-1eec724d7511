<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      v-loading="loading"
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
    >
      <template #operation="{ row }">
        <gp-button type="text" @click="showDetail(row)">{{$t('lang.venus.web.common.detail')}}</gp-button>
      </template>
    </geek-customize-table>
  </geek-main-structure>
</template>

<script>
import {getDevicePage,getCurrentMapInfo} from './api'
import { cloneDeep } from "lodash";
import {mapMutations} from 'vuex'
export default {
  name: "deviceInstance",
  components:{},
  data() {
    return {
      loading:false,
      mapInfo:{},
      searchData:{},
      addDeviceDialog:false,
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.venus.web.common.modelName",
            default: "",
            tag: "input",
          },
          code: {
            label: "lang.venus.web.common.modelCode",
            default: "",
            tag: "input",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          index:true
        },
        actions: [
          // {
          //   label: "新增设备模型",
          //   type: "primary",
          //   handler: "addDevice",
          // },
        ],
        columns: [
          { label: "lang.venus.web.common.deviceName", prop: "deviceName"},
          { label: "lang.venus.web.common.deviceCode", prop: "deviceCode"},
          { label: "lang.rms.fed.replay.type", prop: "deviceType"},
          { label: "lang.venus.web.common.deviceStatus", prop: "deviceState"},
          { label: "lang.rms.api.result.warehouse.agreementName", prop: "protocolName"},
          { label: "lang.rms.fed.mapID", prop: "mapId"},
          { label: "lang.rms.fed.FloorID", prop: "floorId"},
          {
            label: "lang.rms.fed.listOperation",
            prop: "operation",
            slotName: "operation",
            width: "200",
            className: "operation-btn",
          },
        ],
      },
    }
  },
  // created() {
  //   this.getMapInfo()
  // },
  mounted() {
    this.getList()
  },
  methods:{
    ...mapMutations("device", ["setDeviceModelInfo"]),
    async getCurrentMapId() {
      const res = await getCurrentMapInfo()
      const {code,data} = res
      return data?.id || ''
      // this.mapInfo = data
      // console.log(this.mapInfo)
    },
    addDevice() {
      this.addDeviceDialog = true
    },
    editTrigger() {
      this.$confirm('是否进入编辑状态?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push({ name: 'deviceModelDetail', params: { }})
      })
    },
    dialogClose() {
      this.addDeviceDialog = false
    },
    //获取列表
    async getList() {
      this.loading = true
      const mapId = await this.getCurrentMapId()
      // const mapId = 2
      if(!mapId) {
        this.loading = false
        return
      };
      const {currentPage,pageCount,pageSize} = this.tablePage
      const params = Object.assign({ currentPage, pageSize, mapId},this.searchData)
      const res = await getDevicePage(params)
      this.loading = false
      const {code,data} = res
      if(code === 0){
        const {recordList} = data
        this.tableData = recordList
        console.log(this.tableData)
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getList();
    },
    showDetail(row) {
      const {deviceCode} = row
      this.$router.push({ name: 'deviceInstanceDetail', query: { deviceCode }})
    },
    onQuery(ops) {
      this.searchData = Object.assign(this.searchData,ops)
      this.getList()
    },
    onReset() {
      this.searchData = {}
      this.getList()
    }
  }
};
</script>

<style scoped lang="less">
.edit-item{
  line-height: 30px;
  display: flex;
  .edit-input{
    flex: 0 0 160px;
  }
  i{
    font-size: 16px;
    color: #3a8ee6;
    line-height: 30px;
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
