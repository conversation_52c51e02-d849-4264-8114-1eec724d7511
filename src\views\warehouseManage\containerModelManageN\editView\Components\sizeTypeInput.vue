<template>
  <gp-select
    class="w200"
    v-model="valList"
    multiple
    filterable
    allow-create
    default-first-option
    :disabled="disabled"
    size="mini"
    @change="$emit('change', valList)"
  >
    <gp-option v-for="valItem in valList" :key="valItem" :label="valItem" :value="valItem" />
  </gp-select>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: () => "",
    },
    disabled: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    const { value } = this;
    const valList = value ? value.split(",") : [];
    return { valList };
  },
  computed: {},
  watch: {
    value(val) {
      this.valList = val ? val.split(",") : [];
    },

    valList(val) {
      const { value } = this;
      const newValue = val.join(",");
      if (value !== newValue) {
        this.$emit("update:value", newValue);
      }
    },
  },
};
</script>

<style scoped lang="less"></style>
