import Chart, { requestCache } from "../common";

/**
 * 2.1.7任务完成率
 */
export default class StartLocationByCredGrop extends Chart {
  /**
   * 初始化图表 - 2.1.7任务完成率
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('group', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || ["lang.rms.stats.show.startLocationOnTransfer", "lang.rms.stats.show.startLocationOnLattice", "lang.rms.stats.show.startLocationOnRobot"];
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        valType: 'yyyy-MM-dd',
        option: {}
      }
    ]
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/dashboard/count/', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      showCount: true,
      showCountGroup: true,
      ...params
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const { startLocationOnTransfer, startLocationOnLattice, startLocationOnRobot } = data.countMap;
    return {
      id: 'startLocation',
      title: 'lang.rms.stats.show.startLocation',
      group: [
        { type: 'cred', title: this.title[0], number: startLocationOnTransfer || 0},
        { type: 'cred', title: this.title[1], number: startLocationOnLattice || 0},
        { type: 'cred', title: this.title[2], number: startLocationOnRobot || 0}
      ]
    }
  }
}