/* ! <AUTHOR> at 2023/06/09 */

export default {
  async rmsInitConfig() {
    const RMSConfigRes = await $req.reqRMSConfig();
    const reqJanusInfo = await $req.reqJanusInfo();
    const RMSConfig = RMSConfigRes.data;
    const newRMSConfig = reqJanusInfo.data || {};
    const openPermissionSystem = newRMSConfig.openPermissionSystem === "true";

    if (openPermissionSystem !== $utils.Data.getRMSPermission()) {
      $utils.Data.removeAllStorage();
    }
    $utils.Data.setLogoutUrl(newRMSConfig.logoutUrl || "");
    $utils.Data.setUserInfo(newRMSConfig.userName || "guest");
    $utils.Data.setRMSPermission(openPermissionSystem);
    $utils.Data.setRMSConfig(JSON.stringify(RMSConfig));

    const feStaticConfig = await $req.reqStaticConfig();
    $utils._staticConfig = feStaticConfig;
    $utils.Tools.setFavIcon();

    // 获取sso菜单列表;

    if (!$utils._needInitMenuList || !openPermissionSystem) return;

    const res = await $req.get(
      "/athena/api/coreresource/auth/getModuleMenus/v1",
      { subsystemCode: "RMS" },
      { intercept: false },
    );
    if (res?.coode === 2 || res.msg === "toLogin") {
      const redirectUrl = window.location.origin + window.location.pathname + "#/dashboard";
      window.location.replace(`/athena/auth/index.html#/login?redirect_url=${redirectUrl}`);
      return;
    }

    const { userName, systemIdPermissionMap } = res.data.user;
    const menuList = systemIdPermissionMap.RMS["PC-menu"];
    const currentCred = res.data.sessionId;
    $utils.Data.loginResetStorage();
    $utils.Data.setToken(currentCred);
    $utils.Data.setUserInfo(userName);
    if (menuList && menuList.length > 0) {
      $utils.Data.setMenuList(JSON.stringify(menuList));
    }
  },
};
