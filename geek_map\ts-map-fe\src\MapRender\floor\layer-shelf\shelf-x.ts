/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

// 四向车
class LayerX implements MRender.Layer {
  private floorId: floorId;
  private mapCore: any;
  private container: PIXI.Container;
  private shaderMap: any;
  private fragment: number;
  private meshList: any = [];
  private geometries: { [key in MRender.shelfXIcon]?: Array<any> } = {};
  private data: { [key in MRender.shelfXIcon]?: Array<mShelfData> } = {};
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "xShelf";
    container.zIndex = utils.getLayerZIndex("xShelf");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");

    this.shaderMap = {
      X_HOLDER: utils.getShader("iconColor", utils.getResources("X_HOLDER")),
      X_PALLET: utils.getShader("iconColor", utils.getResources("X_PALLET")),
      X_HOLDER_PALLET: utils.getShader("iconColor", utils.getResources("X_HOLDER_PALLET")),
      X_PALLET_STACK: utils.getShader("iconColor", utils.getResources("X_PALLET_STACK")),
      X_HOLDER_PALLET_STACK: utils.getShader("iconColor", utils.getResources("X_HOLDER_PALLET_STACK")),
    };
    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(): void {
    const _this = this;
    const typeGeometries = _this.geometries;
    const typeData = _this.data;

    const mapCore = _this.mapCore;
    const utils = mapCore.utils;
    const fragment = _this.fragment;
    const shaderMap = _this.shaderMap;

    let type: MRender.shelfXIcon;
    for (type in typeGeometries) {
      const geometries = typeGeometries[type];
      if (!geometries || !geometries.length) continue;

      const data = typeData[type];
      for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
        const arr = geometries.slice(i * fragment, i * fragment + fragment);
        if (!arr.length) return;

        const sliceData = data.slice(i * fragment, i * fragment + fragment);

        let meshKey = sliceData[0].code;
        let mesh = utils.createMesh(arr, shaderMap[type]);
        mesh.name = "xShelf";
        mesh.name = meshKey;
        mesh.mapType = "xShelf";
        mesh.interactive = mesh.buttonMode = true;
        mapCore.meshData.xShelf.setData(meshKey, sliceData);

        _this.meshList.push(mesh);
        _this.container.addChild(mesh);
      }
    }

    this.geometries = {};
    this.data = {};
  }

  setGeometry(options: mShelfData, geometry: any): void {
    const _this = this;
    const mapCore = _this.mapCore;
    const shelfType = options["shelfType"] as MRender.shelfXIcon;

    mapCore.mapEvent.updateSelect("xShelf", options);
    mapCore.mapData.shelf.setData(options["code"], options);
    if (!_this.data[shelfType]) _this.data[shelfType] = [];
    _this.data[shelfType].push(options);
    if (!_this.geometries[shelfType]) _this.geometries[shelfType] = [];
    _this.geometries[shelfType].push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(type?: "heat"): void {
    if (!type) {
      this.mapCore.meshData.xShelf.delByFloorId(this.floorId);
    }
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = {};
    this.data = {};
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.shaderMap = null;
    this.meshList = null;
    this.mapCore = null;
    this.data = null;
    this.floorId = undefined;
  }
}
export default LayerX;
