<template>
  <geek-main-structure class="parameterConfig">
    <gp-tabs v-model="activeName" class="tab-class">
      <gp-tab-pane v-if="tabNamePerssion['params']" :label="$t('lang.rms.fed.ParameterConfiguration')" name="params">
        <parameter-config-iframe />
      </gp-tab-pane>
      <gp-tab-pane v-if="tabNamePerssion['library']" :label="$t('lang.rms.fed.configurationLibrary')" name="library">
        <parameter-library />
      </gp-tab-pane>
    </gp-tabs>
  </geek-main-structure>
</template>

<script>
import parameterConfigIframe from "./parameterConfig_iframe";
import parameterLibrary from "./parameterLibrary";
export default {
  components: {
    parameterLibrary,
    parameterConfigIframe,
  },
  data() {
    return {
      tabNamePerssion: {
        params: this.getTabPermission("TabParamsConfigureParamPage", "paramsConfigure"),
        library: this.getTabPermission("TabParamsConfigureDemoPage", "paramsConfigure"),
      },
      defaultActive: "params",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  created() {},
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(this.defaultActive, this.tabNamePerssion);
  },
  methods: {},
};
</script>
<style scoped>
.tab-class {
  width: 100%;
  height: 100%;
}

.tab-class /deep/ .gp-tabs__content,
.gp-tab-pane {
  width: 100%;
  height: calc(100% - 45px);
}
</style>
