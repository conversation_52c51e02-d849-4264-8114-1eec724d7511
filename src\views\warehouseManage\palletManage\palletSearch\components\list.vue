<template>
  <geek-main-structure class="user-list">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <div class="table-content_head">
        <div class="table-title">
          {{ $t("lang.rms.palletSearchManage.palletList") }}
        </div>
        <!-- <div class="handle-btn">
          <gp-button type="primary" size="small" @click="onAdd">
            {{ $t("lang.rms.fed.add") }}
          </gp-button>
        </div> -->
      </div>
      <customize-table :table-config="tableConfig">
        <template #areaId="{ column }">
          <gp-table-column v-bind="column">
            <template #default="{ row }">
              {{ row.extendJson ? row.extendJson.logicId : "" }}
            </template>
          </gp-table-column>
        </template>
        <template #manageStatus="{ column }">
          <gp-table-column v-bind="column">
            <template #default="{ row }">
              {{ row.manageStatus ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </template>
          </gp-table-column>
        </template>
        <template #palletSize="{ column }">
          <gp-table-column v-bind="column">
            <template #default="{ row }"> {{ row.length }}/{{ row.width }}/{{ row.height }} </template>
          </gp-table-column>
        </template>

        <template #robotOrTask="{ column }">
          <gp-table-column v-bind="column">
            <template #default="{ row }">
              {{ row.robotId ? row.robotId + "/" : "" }}
              {{ row.extendJson ? row.extendJson.taskIds : "" }}
            </template>
          </gp-table-column>
        </template>
      </customize-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="totalPage"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
import CustomizeTable from "./customize-table";
export default {
  name: "PalletPositionManage",
  components: {
    CustomizeTable,
  },
  data() {
    return {
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      newPassword: "",
      recordCount: 0,
      totalPage: 0,
      popoverVisible: false,
      createUserFlag: false,
      editUserFlag: false,
      list: [],
      recordList: [],
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
    tableConfig() {
      return {
        attrs: {},
        isPagination: false,
        columns: [
          {
            label: this.$t("lang.rms.fed.listSerialNumber"),
            type: "index",
            width: "50px",
          },
          {
            label: this.$t("lang.rms.palletSearchManage.palletCode"),
            "show-overflow-tooltip": true,
            prop: "code",
          },
          {
            label: this.$t("lang.rms.fed.region"),
            "show-overflow-tooltip": true,
            slot: "areaId",
          },
          {
            label: this.$t("lang.rms.fed.lock"),
            "show-overflow-tooltip": true,
            slot: "manageStatus",
          },
          {
            label: `${this.$t("lang.rms.fed.length")}/${this.$t("lang.rms.fed.width")}/${this.$t(
              "lang.rms.palletPositionManage.height",
            )}`,
            "show-overflow-tooltip": true,
            slot: "palletSize",
          },
          {
            label: `${this.$t("lang.mb.robotManage.robotId")}/${this.$t("lang.venus.web.common.dcsTaskId")}`,
            "show-overflow-tooltip": true,
            slot: "robotOrTask",
          },
          {
            label: this.$t("lang.rms.palletSearchManage.palletStatus"),
            "show-overflow-tooltip": true,
            prop: "workStatus",
          },
          {
            label: this.$t("lang.rms.palletPositionManage.palletRackCode"),
            "show-overflow-tooltip": true,
            prop: "parentCode",
          },
          {
            label: this.$t("lang.rms.palletPositionManage.palletLatticeCode"),
            "show-overflow-tooltip": true,
            prop: "locationLatticeCode",
          },
          {
            label: this.$t("lang.rms.palletSearchManage.palletOfLayer"),
            "show-overflow-tooltip": true,
            prop: "layer",
          },
          {
            label: this.$t("lang.rms.palletSearchManage.palletLocation"),
            "show-overflow-tooltip": true,
            prop: "locationCode",
          },
          // {
          //   label: this.$t("lang.rms.palletSearchManage.roadwayDepth"),
          //   "show-overflow-tooltip": true,
          //   prop: "",//开发未提供 先注释
          // },
        ],
        data: this.recordList || [],
        pageSize: this.page.pageSize,
        total: this.recordCount || 0,
      };
    },
    formConfig() {
      return {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          palletCode: {
            label: this.$t("lang.rms.palletSearchManage.palletCode"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterContent"),
          },
          logicId: {
            label: this.$t("lang.rms.fed.region"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterContent"),
          },
          manageStatus: {
            label: this.$t("lang.rms.fed.lock"),
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: this.$t("lang.rms.api.result.warehouse.all"),
              },
              {
                value: 0,
                label: this.$t("lang.rms.fed.no"),
              },
              {
                value: 1,
                label: this.$t("lang.rms.fed.yes"),
              },
            ],
          },
        },
        rules: [],
        operations: [
          {
            label: this.$t("lang.rms.fed.query"),
            handler: "on-query",
            type: "primary",
          },
          {
            label: this.$t("lang.rms.fed.reset"),
            handler: "on-reset",
            type: "default",
          },
        ],
      };
    },
  },
  async created() {
    this.params = {
      pageSize: this.page.pageSize,
      currentPage: this.page.currentPage,
    };
    await this.getPalletLatticeList(this.params);
  },
  methods: {
    // 列表接口请求
    async getPalletLatticeList(params) {
      const res = await $req.get("/athena/pallet/findList", {
        ...params,
        params: true,
      });

      const data = res.data;
      this.page.currentPage = data.currentPage || 1;
      this.recordList = data.recordList;
      this.totalPage = data.pageCount;
      this.recordCount = data.recordCount;
    },

    // 分页
    currentPageChange(page) {
      this.page.currentPage = page;
      this.params = Object.assign(this.params, this.page);
      this.getPalletLatticeList(this.params);
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.params = Object.assign(this.params, this.page);
      this.getPalletLatticeList(this.params);
    },
    onQuery(val) {
      console.log(val);
      this.page.currentPage = 1;
      this.params = Object.assign(val, this.page);
      this.getPalletLatticeList(this.params);
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.params = Object.assign({}, val, this.page);
      this.getPalletLatticeList(this.params);
    },
  },
};
</script>

<style lang="less">
.user-list {
  .table-content {
    padding-top: 10px;
  }
  .table-content_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .table-title {
    margin-bottom: 15px;

    &::before {
      content: "";
      display: inline-block;
      height: 21px;
      width: 4px;
      border-radius: 4px;
      background: #409eff;
      margin-right: 10px;
      vertical-align: text-bottom;
    }
  }
  .handle-btn {
    padding-bottom: 20px;
    text-align: right;

    .gp-button {
      width: 120px;
    }
  }
}
.change-pwd {
  .rowSpace {
    margin: 0px 0px 16px 0px;
  }
  .vText {
    line-height: 32px;
  }
  .cSpace {
    text-align: center;
  }
  .customInput {
    width: 150px;
    height: 20px;
  }
}
</style>
