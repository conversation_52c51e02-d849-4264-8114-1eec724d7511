<template>
  <gp-header class="geek-header" logoDes="RMS" mode="system">
    <geek-header-menu />
    <!-- 动画滚动 -->
    <!-- <div class="error-msg">
      <div class="animate">{{ errorMsg }}</div>
    </div> -->
    <template #menu>
      <gp-tooltip :content="errorMsg" placement="bottom" effect="light">
        <div class="error-msg1">{{ errorMsg }}</div>
      </gp-tooltip>
      <div v-if="errorMsg" class="license-btn" @click="onLink">{{ $t("auth.rms.fed.licenseManage") }}</div>
      <geek-error-log />
      <geek-lang-selection icon="text" />
      <geek-version-info v-if="isShowGeekIcon" />
      <geek-user-info />
    </template>
  </gp-header>
</template>

<script>
import GeekHeaderMenu from "./geek-header-menu";
import GeekErrorLog from "./geek-error-log";
import GeekUserInfo from "./geek-user-info";
import GeekVersionInfo from "./geek-version-info";

export default {
  name: "GeekHeader",
  components: { GeekHeaderMenu, GeekErrorLog, GeekUserInfo, GeekVersionInfo },
  props: ["isShowGeekIcon"],
  data() {
    return {
      errorMsg: "",
    };
  },
  created() {
    this.$EventBus.$on("checkLicense", data => {
      this.checkLicense();
    });
    this.checkLicense();
  },
  methods: {
    checkLicense() {
      localStorage.setItem("errorMsg", "");
      $req.get("/athena/license/checkLicense", {}, { intercept: false }).then(res => {
        if (res.code === 0) {
        } else {
          const errorMsg = $utils.Tools.transMsgLang(res.msg);
          this.errorMsg = errorMsg;
          setTimeout(() => {
            localStorage.setItem("errorMsg", errorMsg);
          }, 60);
          this.$error(errorMsg);
        }
      });
    },
    onLink() {
      if (this.$route.name !== "licenseManage") {
        this.$router.push({
          name: "licenseManage",
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@nav-title-max-width: @g-slide-max-width;
@nav-title-min-width: @g-slide-min-width;
@nav-height: @g-header-height;
@nav-bg: @g-nav-bg;
.geek-header {
  background: #1a232f;
}

.error-msg1 {
  max-width: 180px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: red;
  font-size: 14px;
}
.error-msg {
  width: 180px;
  overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  color: red;
  font-size: 14px;
}

.license-btn {
  margin-left: 8px;
  cursor: pointer;
  color: #0fff;
  font-size: 14px;
}
.animate {
  padding-left: 20px;
  width: 100%;
  font-weight: 800;
  display: inline-block;
  white-space: nowrap;
  animation: 5s wordsLoop linear infinite normal;
}

@keyframes wordsLoop {
  0% {
    transform: translateX(100%);
    -webkit-transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
  }
}
.header-lang-select {
  display: flex !important;
  align-content: center;
  justify-content: center;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 8px;
  :deep(img) {
    display: block;
  }
}
// .geek-header .gp-header__menu-item div.lang-selection {
//   margin: 0 !important;
// }
</style>
