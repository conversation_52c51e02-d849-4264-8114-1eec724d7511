<template>
  <section class="rack-manage-panel-wrap">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset">
      <template #robotId="{}">
        <gp-input
          v-model="robotId"
          type="number"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
          clearable
          onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
        />
      </template>
    </geek-customize-form>
    <div class="rack-manage-panel-wrap__handle">
      <gp-button type="primary" :disabled="returnArr.length === 0" @click="onReturn">{{
        $t("lang.rms.box.returnAllLoadedBox")
      }}</gp-button>
      <div class="total-number-box">
        <span class="total-number-item">
          <strong>{{ $t("lang.rms.box.total.count") }} </strong>： <strong>{{ totalCount }}</strong>
        </span>
        <span class="total-number-item">
          <strong>{{ $t("lang.rms.box.locked.count") }} </strong>：
          <strong>{{ lockedCount }}</strong>
        </span>
      </div>
    </div>

    <div class="rack-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @selectionChange="handleSelection"
        @page-change="pageChange"
      >
        <template #operations="{ row }">
          <div v-if="row.boxStatus === 'POSITION_CONFIRMING'">
            <gp-link type="primary" :underline="false" class="btn-opt" @click="itemSave(row)">
              {{ $t("lang.rms.fed.confirm") }}
            </gp-link>
            <gp-link type="primary" :underline="false" class="btn-opt" @click="itemChange(row)">
              {{ $t("lang.rms.box.changeBtn") }}
            </gp-link>
          </div>
        </template>
      </geek-customize-table>
    </div>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";

export default {
  name: "PackQueryIndex",
  components: { EditDialog },
  data() {
    return {
      totalCount: 0,
      lockedCount: 0,
      // 搜索条件
      robotId: "",
      form: {
        robotId: "",
        boxStatus: "",
        boxCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          robotId: {
            label: "lang.rms.fed.inputRobotId",
            default: "",
            tag: "input",
            slotName: "robotId",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          boxStatus: {
            label: "lang.rms.box.boxStatus",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
          boxCode: {
            label: "lang.rms.fed.boxCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.web.boxCode.input",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
          // {
          //   label: "lang.rms.box.returnAllLoadedBox",
          //   handler: "on-return",
          //   type: "success",
          // },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
          index: true,
          "row-key": "boxCode",
          "reserve-selection": true,
        },
        columns: [
          {
            label: "lang.rms.fed.boxCode",
            prop: "boxCode",
          },
          {
            label: "lang.rms.box.boxStatus",
            prop: "boxStatusCode",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          {
            label: "lang.rms.box.locked.flag",
            prop: "boxLockState",
            formatter: (row, column) => {
              if (row[column] === "LOCKED") return this.$t("lang.rms.box.locked.flag.yes");
              else return this.$t("lang.rms.box.locked.flag.no");
            },
          },
          { label: "lang.rms.fed.listRobotId", prop: "robotId" },
          { label: "lang.rms.box.robotLayer", prop: "robotLayer" },
          { label: "lang.rms.box.current.latticeCode", prop: "currentLatticeCode" },
          {
            label: "lang.rms.box.boxPlaceRackCode",
            prop: "location",
            formatter: (row, column) => {
              if (!row[column]) return "--";
              return `X:${row[column].x},Y:${row[column].y}`;
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "150",
            fixed: "right",
            slotName: "operations",
          },
        ],
      },
      selectList: [], // 可进行一键还箱
    };
  },
  computed: {
    returnArr() {
      let arr = [];
      this.tableData.forEach(item => {
        if (item.boxStatus === "LOADED") {
          arr.push(item.boxCode);
        }
      });
      return arr;
    },
  },
  // watch: {
  //   returnArr(v) {
  //     let formConfig = this.formConfig;
  //     formConfig.operations[1].disabled = v.length === 0;
  //     this.formConfig = Object.assign({}, formConfig);
  //   },
  // },
  activated() {
    this.getTableList();
    this.getBoxStatus();
  },
  methods: {
    itemSave(row) {
      $req.post("/athena/box/confirmBoxStatus", { boxCode: row.boxCode }).then(res => {
        if (res.code === 0) {
          this.$success(this.$t("lang.common.success"));
          this.getTableList();
        }
      });
    },
    itemChange(row) {
      this.$refs.editDialog.open(row);
    },
    handleSelection(selection) {
      this.selectList = selection.map(i => i.boxCode);
    },
    // 归还
    async onReturn() {
      if (this.selectList && this.selectList.length) {
        const title = this.$t("lang.rms.fed.region");
        const tips = this.$t("lang.rms.fed.containerGoReturnArea");
        const that = this;
        this.$prompt(title, tips, {
          closeOnClickModal: false,
          inputValidator(val) {
            if (!val) return true;
            if (!/^[+]{0,1}(\d+)$/.test(val)) return that.$t("lang.rms.fed.pleaseEnterAnNumber");
            return true;
          },
          async beforeClose(action, instance, done) {
            if (action !== "confirm") return done();
            instance.confirmButtonLoading = true;
            const areaId = instance.inputValue || "";
            try {
              const { code, msg } = await $req.post("/athena/box/returnAllLoadedBox", {
                boxCodes: that.selectList,
                areaId,
              });
              instance.confirmButtonLoading = false;
              if (!code) {
                that.$message.success(that.$t("lang.common.success"));
                done();
                that.getTableList();
              }
            } catch (e) {
              instance.confirmButtonLoading = false;
            }
          },
        });
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.form.robotId = this.robotId;
      this.getTableList();
    },
    onReset(val) {
      this.robotId = "";
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getBoxStatus() {
      $req.get("/athena/box/listBoxStatus").then(res => {
        let list = res.data || [];
        this.formConfig.configs.boxStatus.options = list.map(item => {
          return {
            value: item.status,
            label: this.$t(item.statusCode),
          };
        });
      });
    },
    getTableList() {
      const params = {
        robotId: this.form.robotId || null,
        boxStatus: this.form.boxStatus || null,
        boxCode: this.form.boxCode || null,
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      $req.get("/athena/box/list", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.totalCount = result.totalCount;
        this.lockedCount = result.lockedCount;
        this.tableData = result?.page?.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result?.page?.currentPage || 1,
          total: result?.page?.recordCount || 0,
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.total-number-box {
  padding: 0 10px;
  // margin-bottom: 10px;
  height: 40px;
  line-height: 40px;
  text-align: right;

  .total-number-item {
    margin-right: 10px;

    strong {
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
