import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * RS机器人工作状态
 */
export default class RobotChargeNumBar extends Chart {
  /**
   * 初始化图表 - RS机器人工作状态
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "RS机器人工作状态";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      },
    ]
  }

  async request(params) {
    const { data } = await requestCache('/athena/stats/query/robot/snapshot', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      cycle : "5",
      ...params
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData = data.xAxis || [];
    const seriesData = data.day2Data?.['-1'] || [];
      return parseEchartOption({
        title: { text: this.title || '' },
        xAxis: { type: 'category', data: xAxisData.map(item => {
          return $utils.Tools.formatDate(item, "yyyy-MM-dd")
        }) },
        yAxis: { type: 'value' },
        tooltip: { show: true, trigger: 'axis' },
        series: [{ data: seriesData, type: 'bar' }]
      })
    }
}