<template>
  <gp-dialog
    :title="$t('lang.rms.email.config.editNotice')"
    :visible.sync="showDialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    :footer="false"
    width="28%"
  >
    <gp-form ref="editForm" :model="formData" :rules="rules" label-width="100px">
      <gp-form-item :label="$t('lang.rms.email.config.inputAppName')" prop="appName">
        <gp-input v-model="formData.appName"></gp-input>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.email.config.sender.address')" prop="sendMail">
        <gp-input v-model="formData.sendMail">
          <template slot="append">{{ sendMailSuffix }}</template>
        </gp-input>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.email.config.inputSenderPassword')" prop="sendMailPassword">
        <gp-input v-model="formData.sendMailPassword" :show-password="true" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.email.config.receiver.address')" prop="receiveMail">
        <gp-select
          style="width: 100%"
          v-model="formData.receiveMail"
          multiple
          :multiple-limit="9"
          filterable
          allow-create
          default-first-option
          :placeholder="$t('lang.rms.email.config.inputInboxAddress')"
          @change="receiveMailChange"
        >
          <gp-option v-for="item in formData.receiveMail" :key="item" :label="item" :value="item"> </gp-option>
        </gp-select>
      </gp-form-item>
      <!-- <gp-form-item label="SMTP" prop="senderSmtp">
        <gp-input v-model="formData.senderSmtp">
        </gp-input>
      </gp-form-item> -->
      <gp-form-item :label="$t('lang.rms.email.config.notice.frequency')" prop="rate">
        <gp-input type="number" v-model="formData.rate">
          <template slot="append">min</template>
        </gp-input>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.email.config.isNotice')">
        <gp-switch v-model="formData.isNotify"></gp-switch>
      </gp-form-item>
      <gp-form-item>
        <gp-button type="primary" @click="submit">{{ $t("lang.rms.fed.confirm") }}</gp-button>
        <gp-button @click="closeDialog">{{ $t("lang.rms.fed.cancel") }}</gp-button>
      </gp-form-item>
    </gp-form>
  </gp-dialog>
</template>

<script>
export default {
  name: "editDialog",
  props: {
    showDialog: {
      type: Boolean,
      default: false,
    },
    dialogData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  mounted() {
    this.formData = { ...this.dialogData };
  },
  data() {
    //通知频率校验
    const rateValidator = (rule, value, callback) => {
      if (value > 0) {
        callback();
      } else {
        callback(new Error(this.$t("lang.rms.email.config.inputNaturalNumbers")));
      }
    };
    return {
      dialogVisible: false,
      //发送邮件后缀
      sendMailSuffix: "@geekplus.com",
      formData: {
        senderSmtp: "",
        appName: "",
        sendMail: "",
        sendMailPassword: "",
        receiveMail: [],
        rate: 30,
        isNotify: true,
      },
      //检验规则
      rules: {
        appName: [{ required: true, message: this.$t("lang.rms.fed.replay.projectName"), trigger: "blur" }],
        sendMail: [
          { required: true, message: this.$t("lang.rms.email.config.inputSenderAddress"), trigger: "blur" },
          // { pattern: /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i, message: '请输入正确邮箱地址', trigger: ['blur', 'change']}
        ],
        sendMailPassword: [{ required: true, message: this.$t("lang.auth.UserAPI.item0181"), trigger: "blur" }],
        receiveMail: [
          {
            type: "array",
            required: true,
            message: this.$t("lang.rms.email.config.inputReceiverAddress"),
            trigger: "blur",
          },
        ],
        rate: [{ type: "number", required: true, trigger: "blur", validator: rateValidator }],
      },
    };
  },
  methods: {
    //关闭提示
    closeDialog() {
      this.$emit("closeDialog");
    },
    receiveMailChange(data) {
      const len = data.length;
      if (!len) return;
      //邮箱校验
      const emailRegExp = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
      const inputEmail = data[len - 1];
      const ok = emailRegExp.test(inputEmail);
      if (!ok) {
        this.$message({
          message: this.$t("lang.rms.email.config.inputRightEmail"),
          type: "warning",
        });
        this.formData.receiveMail.pop();
      }
    },
    //数据提交
    submit() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const { sendMail } = this.formData;
          const sendData = {
            ...this.formData,
            sendMail: sendMail + this.sendMailSuffix,
          };
          this.$emit("submit", sendData);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.input-width {
  width: 200px;
}
</style>
