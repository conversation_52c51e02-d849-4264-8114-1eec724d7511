<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @addDevice="addDevice"
      @page-change="pageChange"
    >
      <template #operation="{ row }">
        <gp-button type="text" @click="showDetail(row)">{{$t('lang.venus.web.common.detail')}}</gp-button>
      </template>
    </geek-customize-table>
    <gp-dialog
      title="新增设备模型"
      :visible.sync="addDeviceDialog"
      @closed="dialogClose"
      width="20%"
    >
      <add-device
        v-if="addDeviceDialog"
        @editTrigger="editTrigger"
        @closed="dialogClose"
      >

      </add-device>
    </gp-dialog>
  </geek-main-structure>
</template>

<script>
import {getDeviceModelList} from './api'
import addDevice from './components/addDevice'
import { cloneDeep } from "lodash";
import {mapMutations} from 'vuex'
export default {
  name: "deviceModel",
  components:{addDevice},
  data() {
    return {
      searchData:{},
      addDeviceDialog:false,
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.venus.web.common.modelName",
            default: "",
            tag: "input",
          },
          code: {
            label: "lang.venus.web.common.modelCode",
            default: "",
            tag: "input",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [
        {
          num:1,
          name:'tttt',
          code:'code',
          r:'what fuck'
        }
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          index:true
        },
        actions: [
          // {
          //   label: "新增设备模型",
          //   type: "primary",
          //   handler: "addDevice",
          // },
        ],
        columns: [
          { label: "lang.venus.web.common.modelName", prop: "modelName"},
          { label: "lang.venus.web.common.modelCode", prop: "modelCode"},
          { label: "lang.rms.fed.type", prop: "modelType",},
          { label: "lang.rms.api.result.warehouse.agreementName", prop: "protocolName", },
          {
            label: "lang.rms.fed.listOperation",
            prop: "operation",
            slotName: "operation",
            width: "200",
            className: "operation-btn",
          },
        ],
      },
    }
  },
  computed:{
    showEditInput() {
      return (row,field) => {
        const {builtIn} = row
        const isEdit = row[`isEdit${field}`]
        return isEdit && !builtIn
      }
    },
    showEditBtn() {
      return (row,field) => {
        const {builtIn} = row
        const isEdit = row[`isEdit${field}`]
        return !builtIn && isEdit === false
      }
    },
    showCheckBtn() {
      return (row,field) => {
        const {builtIn} = row
        const isEdit = row[`isEdit${field}`]
        return isEdit && !builtIn
      }
    },
    showCancelBtn() {
      return (row,field) => {
        const {builtIn} = row
        const isEdit = row[`isEdit${field}`]
        return isEdit && !builtIn
      }
    },
  },
  mounted() {
    this.getList()
  },
  methods:{
    ...mapMutations("device", ["setDeviceModelInfo"]),
    addDevice() {
      this.addDeviceDialog = true
    },
    editTrigger() {
      this.$confirm('是否进入编辑状态?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$router.push({ name: 'deviceModelDetail', params: { }})
      })
    },
    dialogClose() {
      this.addDeviceDialog = false
    },
    //获取列表
    async getList() {
      const {currentPage,pageCount,pageSize} = this.tablePage
      const params = Object.assign({ currentPage, pageSize},this.searchData)
      const res = await getDeviceModelList(params)
      const {code,data} = res
      if(code === 0){
        const {recordList} = data
        this.tableData = recordList
        console.log(this.tableData)
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getList();
    },
    editStatus(row,field) {
      const {id} = row
      const tableData = cloneDeep(this.tableData)
      const index = tableData.findIndex(item => item.id === id)
      const item = tableData[index]
      item[`isEdit${field}`] = !item[`isEdit${field}`]
      this.tableData = tableData
    },
    showDetail(row) {
      const {id,modelCode,modelName} = row
      this.$router.push({ name: 'deviceModelDetail', query: { id }})
    },
    //同步
    sync() {
      this.$confirm('是否删除接口中程序过程?', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

      }).catch(() => {

      })
    },
    deleteFn() {

    },
    editFn(row) {
      const {id,builtIn} = row
      this.$router.push({ name: 'deviceModelDetail', query: { id,builtIn }})
    },
    onQuery(ops) {
      this.searchData = Object.assign(this.searchData,ops)
      this.getList()
    },
    onReset() {
      this.searchData = {}
      this.getList()
    }
  }
};
</script>

<style scoped lang="less">
.edit-item{
  line-height: 30px;
  display: flex;
  .edit-input{
    flex: 0 0 160px;
  }
  i{
    font-size: 16px;
    color: #3a8ee6;
    line-height: 30px;
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
