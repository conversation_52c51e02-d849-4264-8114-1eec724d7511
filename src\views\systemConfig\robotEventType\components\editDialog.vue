<template>
  <gp-dialog
    :title="$t('lang.rms.fed.robotEventType')"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    :before-close="close"
    border
    :close-on-click-modal="false"
    width="520px"
  >
    <gp-form ref="dataForm" :model="formData" :rules="rules" label-position="right" label-width="140px">
      <gp-row>
        <gp-col :span="24">
          <gp-form-item :label="$t('lang.rms.fed.eventGroupName')" prop="eventGroupNameI18n">
            <gp-input v-model="formData.eventGroupNameI18n" :disabled="pageType === 'edit'" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item :label="$t('lang.rms.fed.eventGroupValue')" prop="eventGroup">
            <gp-input v-model="formData.eventGroup" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item :label="$t('lang.rms.fed.eventTypeName')" prop="eventTypeNameI18n">
            <gp-input v-model="formData.eventTypeNameI18n" :disabled="pageType === 'edit'" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item :label="$t('lang.rms.fed.eventTypeValue')" prop="eventType">
            <gp-input v-model="formData.eventType" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item :label="$t('lang.rms.fed.eventDes')" prop="eventDesc">
            <gp-input v-model="formData.eventDesc" type="textarea" :rows="2" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item v-show="!isEdit" :label="$t('lang.rms.fed.enable.saveDatabase')" prop="enableSaveDatabase">
            <gp-switch v-model="formData.enableSaveDatabase" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item v-show="!isEdit" :label="$t('lang.rms.fed.enable.publish')" prop="enablePublish">
            <gp-switch v-model="formData.enablePublish" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="24">
          <gp-form-item v-show="!isEdit" :label="$t('lang.rms.fed.robot.event.type.enableCallback')" prop="enableCallback">
            <gp-switch v-model="formData.enableCallback" />
          </gp-form-item>
        </gp-col>
      </gp-row>`
    </gp-form>
    <span slot="footer" class="dialog-footer">
      <gp-button @click="close" plain> {{ $t("lang.rms.fed.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.confirm") }}</gp-button>
    </span>
  </gp-dialog>
</template>

<script>
// TODO 后续优化统一风格
export default {
  name: "RobotEventTypeEditDialog",
  data() {
    return {
      dialogVisible: false,

      formList: {
        id: null,
        robotId: null, // 需要注册到系统的机器人id
        robotType: null, // 机器人类型
        sizeTypes: null, // 机器人能在哪些尺寸的单元格上行走，多值可以用逗号分隔：A,B
        classCodes: null, // 机器人能搬运哪些类别的货架，多值用逗号分隔：A,B
        warehouseId: null, // 机器人所在物理仓库id
        status: 0, // 记录状态
      },
      formData: {
        enableCallback: false,
        enablePublish: false,
        enableSaveDatabase: false,
        eventDesc: "",
        eventGroup: "",
        eventGroupNameI18n: "",
        eventType: "",
        eventTypeNameI18n: "",
      },
      rules: {
        // eventGroupNameI18n: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        // ],
        eventGroup: [
          { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
          { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        ],
        // eventTypeNameI18n: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        // ],
        eventType: [
          { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
          { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        ],
        // eventDesc: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 256, message: this.$t("lang.rms.fed.lengthLimit256"), trigger: "blur" },
        // ],
        enableSaveDatabase: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
        enablePublish: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
        enableCallback: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
      },

      pageType: "add",
    };
  },
  computed: {
    isEdit() {
      return this.pageType === "edit";
    },
  },
  methods: {
    open(data) {
      if (data) {
        this.pageType = "edit";
        this.formData = JSON.parse(JSON.stringify(data));
        this.formData.enableSaveDatabase = Boolean(this.formData.enableSaveDatabase);
        this.formData.enablePublish = Boolean(this.formData.enablePublish);
        this.formData.enableCallback = Boolean(this.formData.enableCallback);
      } else {
        this.pageType = "add";
        this.formData = {
          enableCallback: false,
          enablePublish: false,
          enableSaveDatabase: false,
          eventDesc: "",
          eventGroup: "",
          eventGroupNameI18n: "",
          eventType: "",
          eventTypeNameI18n: "",
        };
      }
      this.dialogVisible = true;
    },
    close() {
      this.$refs.dataForm.resetFields();
      this.dialogVisible = false;
    },
    // 保存
    save() {
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.submit();
        } else {
          return false;
        }
      });
    },
    submit() {
      const param = this.formData;
      const data = {
        enableCallback: param.enableCallback,
        enablePublish: param.enablePublish,
        enableSaveDatabase: param.enableSaveDatabase,
        eventDesc: param.eventDesc,
        eventGroup: param.eventGroup,
        eventGroupNameI18n: param.eventGroupNameI18n,
        eventType: param.eventType,
        eventTypeNameI18n: param.eventTypeNameI18n,
      };
      if (param.id) data.id = param.id;

      $req.post("/athena/robot/robotEventType/saveOrUpDate", data).then(res => {
        if (res.code !== 0) return;
        this.$emit("updateTableList");
        this.$success(this.$t(res.msg));
        this.dialogVisible = false;
      });
    },
  },
};
</script>

<style lang="less" scoped>
// :deep(.gp-form-item) {
//   width: 22%;
// }

// :deep(.gp-form-item__label) {
//   padding-bottom: 0;
//   font-size: 13px;
//   font-weight: 800;
// }
</style>
