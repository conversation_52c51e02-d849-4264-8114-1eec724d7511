import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 搬运距离统计
 */
export default class SumTransportDataDistanceByBar extends Chart {
  /**
   * 初始化图表 - 搬运距离统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "搬运距离统计";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      },
      {
        label: '工作站',
        prop: 'stationIds',
        type: 'gpSelect',
        optionList: [],
        defaultValue: [],
        option: {
          multiple: true
        }
      },
    ]
  }

  async getStationList() {
    const { data } = await requestCache("/athena/station/findAll");
    const propItem = this.filterList.find(item => item.prop === 'stationIds');
    this.stationList = (data || []).map(item => {
      return {
        label: item.id,
        value: item.id
      }
    })
    propItem.optionList = this.stationList;
  }

  // 数据准备
  async preDataReady() {
    await this.getStationList();
  }

  async request(params) {
    const date = params?.date || [];

    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);
    let stationIds = [];
    
    if (params.stationIds?.length) {
      stationIds = params.stationIds;
    }

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/job/sumTransportDataByStationIds', {
      startTime,
      endTime,
      statType: 'TRANSPORT_DISTANCE',
      stationIds,
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData =  data.xAxis || [];
    const type2Data = data.type2Data || {};
    const { averageSum, deliverStage, fetchStage, returnStage } = type2Data;

    const option = {
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData.map(item => `工作站${item}`) },
      yAxis: { type: 'value' },
      legend: {
        data: ['平均任务距离', '平均取货架距离', '平均送货架距离', '平均还货架距离']
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      grid: {
        left: 50,
        right: 20,
        bottom: 30,
        top: 50
      },
      series: [
        { data: averageSum.map(item => (Number(item.toFixed(2)) || 0)), type: 'bar', stack: 'Total', name: '平均任务距离', },
        { data: fetchStage.map(item => (Number(item.toFixed(2)) || 0)), type: 'bar', stack: 'Total', name: '平均取货架距离', },
        { data: deliverStage.map(item => (Number(item.toFixed(2)) || 0)), type: 'bar', stack: 'Total', name: '平均送货架距离', },
        { data: returnStage.map(item => (Number(item.toFixed(2)) || 0)), type: 'bar', stack: 'Total', name: '平均还货架距离', },
      ]
    }

    return parseEchartOption(option);
  }
}