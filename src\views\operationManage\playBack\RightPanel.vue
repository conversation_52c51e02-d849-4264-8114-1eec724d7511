<template>
  <div class="right-panel-con">
    <gp-tabs v-model="selectType">
      <gp-tab-pane v-for="(item, index) in tabArr" :key="index" :label="$t(item.label)" :name="item.name"></gp-tab-pane>
      <div class="search-con">
        <!--        <div class="search-icon">-->
        <!--          <gp-icon name="gp-icon-search" />-->
        <!--        </div>-->
        <gp-select
          class="search-bar"
          v-model="searchVal"
          filterable
          remote
          :placeholder="$t(searchPlaceholder) + 'ID'"
          clearable
          :remote-method="remoteMethod"
          @change="handleSelect"
          @clear="clearSearch"
        >
          <gp-option v-for="item in searchListInner" :key="item" :label="item" :value="item"> </gp-option>
        </gp-select>
      </div>
      <p v-if="!info" class="warning">{{ $t("lang.rms.fed.selectElementOnMapByClick") }}</p>
      <section v-else class="info-con">
        <div v-for="(item, index) in info" class="info-item" v-if="isShowAttr(item.value)">
          <div class="attr-name">{{ $t(item.label) }}：</div>
          <div class="val click_value" v-if="item.attr === 'taskId'" @click="showTaskDetail(item.value)">
            {{ item.value }}
          </div>
          <div class="val" v-else-if="item.attr === 'jobIds'">
            <span>[</span>
            <span
              class="click_value_item"
              v-for="(item2, index2) in item.value"
              :key="index2"
              @click="showTaskDetail(item2)"
            >
              {{ item2 }}
            </span>
            <span>]</span>
          </div>
          <!--            <div class="val click_value"-->
          <!--                 v-if="enableClickAttr(item)"-->
          <!--                 @click="showTaskDetail(item)"-->
          <!--            >-->
          <!--              {{item.value}}-->
          <!--            </div>-->
          <div class="val" v-else>{{ item.value }}</div>
        </div>
      </section>
    </gp-tabs>
  </div>
</template>

<script>
export default {
  props: {
    selectInfo: {
      type: Object,
      default: () => null,
    },
    searchList: {
      type: Array,
      default: () => [],
    },
  },
  name: "RightPanel",
  data() {
    return {
      // testJobIds:[1,2,3],
      searchVal: "",
      selectType: "robot",
      tabArr: [
        {
          label: "lang.rms.fed.robot",
          name: "robot",
          infoArr: [
            {
              attr: "id",
              text: "lang.rms.fed.robotId",
            },
            {
              attr: "robotType",
              text: "lang.rms.fed.type",
            },
            {
              attr: "locationCell",
              text: "lang.rms.fed.textNodeCode",
            },
            {
              attr: "location",
              text: "lang.rms.web.monitor.robot.location",
            },
            {
              attr: "robotPathMode",
              text: "lang.rms.web.monitor.robot.robotPathMode",
            },
            {
              attr: "powerPercent",
              text: "lang.rms.fed.power",
            },
            {
              attr: "mechanismAngle",
              text: "lang.rms.fed.mechanismAngle",
            },
            {
              attr: "taskId",
              text: "lang.rms.fed.taskId",
            },
            {
              attr: "jobIds",
              text: "lang.rms.fed.boxHoldingTaskId",
            },
            {
              attr: "waitForRobots",
              text: "障碍机器人列表",
            },
            {
              attr: "waitForContainers",
              text: "障碍货架列表",
            },
          ],
        },
        {
          label: "lang.rms.fed.cell",
          name: "cell",
          //显示字段
          infoArr: [
            {
              attr: "cellCode",
              text: "lang.rms.fed.textNodeCode",
            },
            {
              attr: "qrCode",
              text: "lang.rms.fed.qrCodeValue",
            },
            {
              attr: "index",
              text: "lang.rms.fed.textIndexCoordinates",
            },
            {
              attr: "location",
              text: "lang.rms.fed.textAbsoluteCoordinate",
            },
            {
              attr: "cellType",
              text: "lang.rms.fed.textNodeType",
            },
            {
              attr: "cellStatus",
              text: "lang.rms.fed.textNodeStatus",
            },
            {
              attr: "loadDirs",
              text: "lang.rms.fed.textLoadDirectionMatrix",
            },
            {
              attr: "unloadDirs",
              text: "lang.rms.fed.textUnloadDirectionMatrix",
            },
            {
              attr: "length",
              text: "lang.rms.fed.textLength",
            },
            {
              attr: "width",
              text: "lang.rms.fed.textWidth",
            },
            {
              attr: "cellFlag",
              text: "lang.rms.fed.textLockStatus",
            },
            {
              attr: "startBounds",
              text: "lang.rms.fed.textOriginNode",
            },
          ],
        },
        {
          label: "lang.rms.fed.optionShelf",
          name: "shelf",
          infoArr: [
            {
              attr: "shelfCode",
              text: "lang.rms.fed.shelfCoding",
            },
            {
              attr: "shelfType",
              text: "lang.rms.fed.type",
            },
            {
              attr: "shelfStatus",
              text: "lang.rms.fed.state",
            },
            {
              attr: "locationCell",
              text: "lang.rms.fed.textNodeCode",
            },

            {
              attr: "location",
              text: "lang.rms.fed.textAbsoluteCoordinate",
            },
            {
              attr: "locationIndex",
              text: "lang.rms.fed.textIndexCoordinates",
            },
            {
              attr: "radAngle",
              text: "lang.rms.fed.angle",
            },
            {
              attr: "robotId",
              text: "lang.rms.fed.robotId",
            },
            {
              attr: "placementCell",
              text: "lang.rms.fed.placementCode",
            },
          ],
        },
        // {
        //   label:"工作站",
        //   name:"station"
        // },
        // {
        //   label:"充电桩",
        //   name:"charger"
        // },
      ],
    };
  },
  computed: {
    //是否展示该数组
    isShowAttr() {
      return value => {
        let flag = true;
        const isArray = Object.prototype.toString.call(value) === "[object Array]";
        if (isArray && !value.length) flag = false;
        return flag;
      };
    },
    info() {
      const f = this.tabArr.filter(item => item.name === this.selectType);
      const showAttr = f[0].infoArr;
      let info = null;
      this.searchVal = "";
      if (this.selectInfo) {
        info = [];
        showAttr.forEach(item => {
          const { attr, text } = item;
          const value = this.selectInfo[attr];
          if (this.selectInfo[attr]) {
            //判断是否为数组
            // const isArray = Object.prototype.toString.call(this.selectInfo[attr]) === "[object Array]"
            // if(isArray && this.selectInfo[attr].length){
            //
            // }
            const item = { label: text, value, attr };
            info.push(item);
          }
        });
        const { selectInfo } = this;
        this.searchVal = selectInfo["id"] || selectInfo["cellCode"] || selectInfo["shelfCode"];
      }
      return info;
    },
    searchListInner() {
      return this.searchList;
    },
    //允许点击的属性
    enableClickAttr() {
      return item => {
        return ["jobIds", "taskId"].includes(item.attr);
      };
    },
    searchPlaceholder() {
      const f = this.tabArr.filter(item => item.name === this.selectType);
      const text = f[0].label;
      return text;
    },
  },
  watch: {
    selectType(type) {
      this.$emit("typeChange", type);
    },
  },
  methods: {
    showTaskDetail(taskId) {
      this.$emit("showTaskDetail", taskId);
    },
    remoteMethod(query) {
      const { selectType } = this;
      const searchParams = { selectType, searchVal: query };
      this.$emit("getSearchData", searchParams);
    },
    clearSearch() {
      this.remoteMethod("");
    },
    handleSelect() {
      const { selectType, searchVal } = this;
      this.$emit("selectMap", { selectType, searchVal });
    },
  },
};
</script>

<style scoped lang="less">
.right-panel-con {
  position: absolute;
  padding: 10px;
  right: 0;
  top: 60px;
  width: 280px;
  z-index: 1000;
  background-color: #ffffff;
  & .warning {
  }
  & .info-con {
    width: 100%;
    .info-item {
      width: 100%;
      display: flex;
      font-size: 14px;
      line-height: 20px;
      margin-top: 10px;
      .attr-name {
        border-left: 3px solid #4693e8;
        padding-left: 8px;
        width: 120px;
        overflow: hidden;
      }
      .val {
        flex: 1;
        font-size: 12px;
      }
      .click_value {
        color: #4693e8;
        text-decoration: underline #4693e8;
        font-weight: bolder;
        cursor: pointer;
      }
      .click_value_item {
        color: #4693e8;
        text-decoration: underline #4693e8;
        font-weight: bolder;
        cursor: pointer;
        padding: 5px;
      }
    }
  }
  & .search-con {
    width: 100%;
    display: flex;
    margin-bottom: 10px;
    .search-icon {
      flex: 0 0 32px;
      line-height: 32px;
      text-align: center;
      font-size: 24px;
      background-color: #f5f7fa;
      color: #909399;
    }
    .search-bar {
      flex: 1;
    }
  }
}
</style>
