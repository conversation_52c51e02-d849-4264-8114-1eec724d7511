/* ! <AUTHOR> at 2022/09 */
import "nprogress/nprogress.css";
import NProgress from "nprogress"; // 进度条
import RouteLogic from "./js/route-logic";

NProgress.configure({ showSpinner: false });
/**
 * 其他项目iframe套rms时，之前的无权限模式改为通过传sessionId来校验
 * 开启权限 && 有sessionId ，走新的check接口，否则还是之前逻辑
 */
export default router => {
  /* 必须调用 `next` */
  router.beforeEach(async (to, from, next) => {
    //阻止本身的login跳转
    if (to.path === "/login") {
      next(false);
      // 跳转到集成的登录系统
      const redirectUrl = window.location.origin + window.location.pathname + "#/dashboard";
      window.location.replace(`/athena/auth/index.html#/login?redirect_url=${encodeURIComponent(redirectUrl)}`);
      return;
    }
    if (!$utils._sessionIframePath) $utils._sessionIframePath = to;
    NProgress.start();
    try {
      // 获取当前权限模式：true需登录，false无需登录
      const RMSPermission = $utils.Data.getRMSPermission();
      // const sessionId = $utils._sessionId;
      if (to.path !== "/" && to.path !== "/login" && $utils._needInitMenuList) {
        if (RMSPermission) {
          // if (sessionId) await RouteLogic.ssoCheck(sessionId);
          RouteLogic.resolvePermissionMenuList(router);
        } else RouteLogic.resolveNoPermissionMenuList(router);
      }
      if (!RMSPermission) await RouteLogic.noPermissionRouter(to, next);
      else await RouteLogic.permissionRouter(to, next);
    } catch (e) {
      if (to.path === "/" || to.path === "/login" || to.path === "/404") next();
      else next("/login");
      const { warn } = console;
      warn(">>>beforeEach catch::", e);
    }
  });

  /* 必须调用 `next` */
  router.beforeResolve((to, from, next) => {
    next();
  });

  router.afterEach((to, from) => {
    NProgress.done();
    RouteLogic.resolveHeaderTabs(to);
    RouteLogic.licenseErrorTip(to);

    if (to.path !== "/" && to.path !== "/login" && to.path !== "/404" && $utils._needInitMenuList) {
      $utils._needInitMenuList = false;
    }
  });
};
