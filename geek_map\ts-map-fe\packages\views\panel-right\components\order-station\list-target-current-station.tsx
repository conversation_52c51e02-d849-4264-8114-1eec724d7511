/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { LockFilled, WarningFilled } from "@ant-design/icons";
import { Button, Input, Select } from "antd";
import { getMap2D } from "../../../../singleton";
import OrderGrid from "../common/order-grid";
const { Option } = Select;

type PropsOrderData = {
  stationData: stationData;
  currentSelect: { type: "box" | "lattice"; latticeCodes: Array<code>; boxCode: code; boxSide: string };
  onClear: () => void;
  onCancel: () => void;
};

function OrderStationCurrent(props: PropsOrderData) {
  const { t } = useTranslation();

  const [stationId, setStationId] = useState("");
  const [parkList, setParkList] = useState<Array<any>>([]);
  const [selectData, setSelectData] = useState<any>(null);
  const [boxSide, setBoxSide] = useState<code>("");
  const [latticeCode, setLatticeCode] = useState<code>("");
  //latticeCode

  // 处理 stationData
  useEffect(() => {
    const parkList = props.stationData?.parkList || [];
    if (!parkList.length) {
      setStationId("");
      setParkList([]);
      setSelectData(null);
      return;
    }

    let code: string = props.stationData?.stationId.toString() || "";

    if (code !== stationId) {
      setStationId(code);
      setSelectData(null);
    }

    let list: Array<any> = [];
    for (let i = 0, len = parkList.length; i < len; i++) {
      let item = parkList[i];
      if (item.parkPosition === "LEFT") list.unshift(item);
      else list.push(item);
    }

    setParkList(list);
  }, [props.stationData]);

  // 点击货格列表
  const clickSelect = (lattice: any) => {
    setSelectData(lattice);
    let data = {
      ...lattice,
      latticeCodes: [lattice.latticeCode],
    };
    if (lattice.relateBox?.boxCode) {
      data.type = "box";
      data.boxCode = lattice.relateBoxCode;
    } else {
      data.type = "lattice";
      data.boxCode = "";
    }
  };

  const getRackNode = (rack: any, isGripper: boolean) => {
    const rackLattices: Array<any> = rack?.lattices || [];

    let lattices: Array<any> = [];
    rackLattices.forEach(lattice => {
      let rowIndex = lattice.rowIndex;
      let colIndex = lattice.colIndex;
      if (!lattices[rowIndex]) lattices[rowIndex] = [];
      lattices[rowIndex][colIndex] = lattice;
    });

    const sumLayer = lattices.length;
    if (!sumLayer) return null;

    let nodes: any = [];
    let items: Array<any>, itemsNode: any;
    for (let i = sumLayer - 1; i >= 0; i--) {
      items = lattices[i];
      if (!items) continue;

      itemsNode = (
        <div key={i} className="poppick-list-item">
          {items.map((item: any, j: number) => {
            return (
              <span
                key={j}
                className={[
                  item ? "" : "no-data",
                  selectData?.latticeCode === item.latticeCode &&
                  !(item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1 || item?.relateBoxCode)
                    ? "active"
                    : "",
                  item?.latticeFlag !== "NORMAL" || item?.relateBox?.lockState === 1 || item?.relateBoxCode
                    ? "locked"
                    : "",
                ].join(" ")}
                title={item.latticeCode || ""}
                onClick={() => clickSelect(item)}
              >
                {item?.relateBoxCode ? item.relateBoxCode : item.latticeCode}
              </span>
            );
          })}
        </div>
      );
      nodes.push(itemsNode);
    }

    return nodes;
  };

  const controlHandler = () => {
    const boxCode = props.currentSelect?.boxCode;
    const latticeCode = selectData?.latticeCode;

    if (!boxCode || !latticeCode) return;

    const reqMsg = "BoxInstructionRequestMsg";
    const resMsg = "BoxInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        instruction: "UPDATE_BOX_LOCATION",
        latticeCode,
        boxCode,
        boxSide,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    handleCancel();
  };

  const handleCancel = () => {
    // setShelfCode("");
    props.onClear();
    props.onCancel();
  };

  return (
    <div>
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.stationId"),
            value: props?.stationData?.stationId || "--",
          },
        ]}
      />

      {parkList.length && (
        <>
          {parkList.map((park: any, index: number) => {
            if (!park?.deviceCode) return null;
            return (
              <div className="map2d-box-station-to-station" key={index} style={{ marginTop: 16 }}>
                {park.virtualRack && <div className="map2d-poppick-list">{getRackNode(park.virtualRack, false)}</div>}
              </div>
            );
          })}
        </>
      )}
      <div>
        <p style={{ fontSize: "14px", paddingTop: 3, paddingBottom: 3 }}>{t("lang.rms.fed.boxSides")}：</p>
        <Select allowClear style={{ width: "100%" }} value={boxSide} onChange={value => setBoxSide(value)}>
          <Option value="B">B</Option>
          <Option value="F">F</Option>
          <Option value="L">L</Option>
          <Option value="R">R</Option>
        </Select>
      </div>
      <div style={{ paddingTop: 6, display: "flex", justifyContent: "flex-end" }}>
        <Button type="primary" disabled={false} size="small" onClick={controlHandler}>
          {t("lang.rms.fed.confirm")}
        </Button>
        <Button disabled={false} size="small" onClick={handleCancel} style={{ marginLeft: 8 }}>
          {t("lang.rms.fed.cancel")}
        </Button>
      </div>
    </div>
  );
}

export default OrderStationCurrent;
