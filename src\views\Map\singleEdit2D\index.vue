<template>
  <geek-main-structure :space="false">
    <iframe id="J_Edit2D" :src="url" class="single-map" @load="iframeLoad"></iframe>
  </geek-main-structure>
</template>

<script>
export default {
  name: "singleEdit2D",
  data() {
    return {
      url: "",
      isLangDataReady: false,
      timer: null,
    };
  },
  activated() {
    const query = this.$route.query || {};
    const params = `${__rms_iframe_chunk_id}&mapId=${query.mapId}&floorId=${query.floorId}`;
    let url = window.location.origin + window.location.pathname + `/singleEdit2D?${params}`;
    if ($req && $req.isDev) url = `${window.location.origin}/singleEdit2D?${params}`;
    this.url = url;
  },
  mounted() {},
  destroyed() {
    window.removeEventListener("message", this.receiveIframeMessage, false);
    $utils.destroyIframe();
  },
  methods: {
    iframeLoad() {
      window.addEventListener("message", this.receiveIframeMessage, false);
    },
    receiveIframeMessage(event) {
      console.log("message >>>> ", event);
      const msg = event.data || {};
      switch (msg.type) {
        case "event":
          if (msg.body.eventName === "goBgEdit") {
            this.$router.push({
              path: "/warehouseManage/editMapFloorBg",
              query: this.$route.query,
            });
          }
          break;
        case "noLogin":
          $utils.Data.removeAllStorage();
          $utils.Tools.toLogin();
          break;
        case "ready":
          const query = this.$route.query || {};
          $utils.postMessage("singleEdit2D", {
            type: "option",
            body: {
              mapId: query.mapId,
              floorId: query.floorId,
              language: localStorage.getItem("curLanguage"),
            },
          });
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.single-map {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  right: 0;
  overflow: hidden;
  user-select: none;
}
</style>
