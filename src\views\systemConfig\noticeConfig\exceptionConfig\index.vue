<template>
  <section class="notice-config-log-panel-wrap">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset" />
    <div class="notice-config-log-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-edit="rowEdit"
      />
    </div>

    <editDialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>
<script>
import editDialog from "./components/editDialog";

export default {
  name: "FaultConfiguration",
  components: { editDialog },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      form: {
        systemCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          systemCode: {
            label: "lang.rms.fault.exception.code",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fault.exception.code",
            prop: "systemCode",
            disabled: true,
          },
          {
            label: "lang.rms.web.monitor.exception.info",
            prop: "exception",
            width: "180",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          {
            label: "lang.rms.fed.chargerSolution",
            prop: "solution",
            width: "200",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          {
            label: "lang.rms.fed.showEnableOrDisable",
            prop: "status",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue == 1) return this.$t("lang.rms.fed.chargerEnable");
              else return this.$t("lang.venus.common.dict.disable");
            },
          },
          {
            label: "lang.rms.fed.callbackEnableOrDisable",
            prop: "callbackDisable",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return this.$t("lang.rms.fed.chargerEnable");
              else return this.$t("lang.venus.common.dict.disable");
            },
          },
          {
            label: "lang.rms.fed.callbackDuration",
            prop: "callbackDuration",
          },
          {
            label: "lang.rms.fed.todolistDuration",
            prop: "todolistDuration",
          },
          {
            label: "lang.rms.backlog.fault.isBacklog",
            prop: "messageGroup",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue == 2) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.fed.voicePromptEnable",
            prop: "voicePromptEnable",
            formatter: (row, column) => {
              const voicePromptEnable = row[column];
              if (voicePromptEnable) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.backlog.fault.isEmailNotice",
            prop: "isSendEmail",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.backlog.fault.isMaintenance",
            prop: "isMaintenance",
            formatter: (row, column) => {
              const cellValue = row[column];
              if (cellValue) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "100",
                fixed: "right",
                operations: [
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    // 编辑
    rowEdit(rowData) {
      this.$refs.editDialog.open(rowData);
    },

    pageChange(page) {
      this.tablePage = Object.assign({}, this.tablePage, page);
      this.getTableList();
    },

    // 重置搜索参数
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 查询
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    getTableList() {
      const params = Object.assign({}, this.form, {
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });
      console.log(params);
      $req.get("/athena/fault/message/getFaultMessages", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result?.recordList || [];

        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result?.currentPage || 1,
          total: result?.recordCount || 0,
        });
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scope>
.config-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
