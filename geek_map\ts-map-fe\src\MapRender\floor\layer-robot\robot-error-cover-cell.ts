/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerRobotErrorCoverCell implements MRender.Layer {
  private container: PIXI.Container;
  private fragment: number;
  private lineStyle: any = new PIXI.LineStyle();
  private geometries: any = [];
  private meshList: any = [];
  private mapCore: MRender.MainCore;
  init(mapCore: MRender.MainCore): void {
    this.mapCore = mapCore;
    const utils = mapCore.utils;
    let container = new PIXI.Container();
    container.name = "robotErrorCoverCell";
    container.zIndex = utils.getLayerZIndex("robotOccupy");
    container.interactiveChildren = false;
    container.visible = true;
    container.alpha = 0.7;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;
   
    geometries.forEach((graphics: any) => {
      _this.meshList.push(graphics);
      _this.container.addChild(graphics);
    });

    _this.geometries = [];
  }

   drawGeometry(options: mRobotData) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData;
      const errorCoverCells = options["errorCoverCells"];
      const color = options["color"];
    let item, fillStyle,lineStyle;

    fillStyle = new PIXI.FillStyle();
    fillStyle.color = color;
    fillStyle.visible = true;
    
    lineStyle = new PIXI.LineStyle();
    lineStyle.color = color;
    lineStyle.visible = true;
    
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = errorCoverCells.length;

    let cellOptions, nsPosition, rect;
    for (let i = 0, len = errorCoverCells.length; i < len; i++) {
      cellOptions = mapData.cell.getData(errorCoverCells[i]);
      if (!cellOptions) continue;
      nsPosition = cellOptions["nsPosition"];

      rect = new PIXI.Polygon(...nsPosition);
      graphicsGeometry.drawShape(rect, fillStyle, lineStyle);
    }
    
    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = options['code'];
    graphics.mapType = "errorCoverCell";
    graphics.visible = true;
    // graphics.interactive = graphics.buttonMode = false;
     
    _this.geometries.push(graphics);
    
  }

  toggle(isShow: boolean): void {
    this.container.visible = true;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
   
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    // this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.lineStyle = null;
    // this.geometries = null;
    this.meshList = null;
  }
}
export default LayerRobotErrorCoverCell;
