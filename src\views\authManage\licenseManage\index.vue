<template>
  <geek-main-structure class="license-manage">
    <div class="import-content">
      <div class="title">
        {{ $t("lang.rms.fed.importInfo") }}
      </div>
      <div class="content">
        <geek-customize-form ref="importForm" :form-config="formConfig" @on-import="onImport" @on-reset="onReset" />
      </div>
    </div>
    <div class="basic-content">
      <div class="title">
        {{ $t("lang.rms.palletPositionManage.baseinfo") }}
      </div>
      <div class="content">
        <gp-form ref="form" :model="basicForm" size="mini" :inline="true" label-width="80px">
          <gp-row>
            <gp-form-item :label="$t('lang.rms.map.dmp.deviceCode')">
              <span>{{ deviceInfo }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.clientCode')">
              <span>{{ basicForm.customerId }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.warehouseCode')">
              <span>{{ basicForm.instanceId }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.startDate')">
              <span>{{ basicForm.effectiveDate }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.endData')">
              <span>{{ basicForm.expiryDate }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.effectiveDays')">
              <span>{{ basicForm.effectiveDays }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.remainingDays')">
              <span>{{ basicForm.remainingDays }}</span>
            </gp-form-item>
          </gp-row>
          <gp-row>
            <gp-form-item :label="$t('lang.rms.fed.preAlertDays')">
              <span>{{ basicForm.alertDays }}</span>
            </gp-form-item>
          </gp-row>
        </gp-form>
      </div>
    </div>
    <div class="service-content">
      <div class="title">
        {{ $t("lang.rms.fed.multiServiceInfo") }}
      </div>
      <div class="content">
        <geek-customize-table :table-config="tableConfig" :data="list" :page="tablePage"> </geek-customize-table>
        <div class="handle-btn">
          <gp-button type="primary" @click="onDevice">{{ $t("lang.rms.fed.boundDeviceIdInfo") }}</gp-button>
        </div>
      </div>
    </div>
    <DeviceDialog v-if="dialogVisible" :tableList="list" @close="onClose" />
  </geek-main-structure>
</template>

<script>
import DeviceDialog from "./components/device-dialog.vue";
export default {
  name: "UserList",
  components: {
    DeviceDialog,
  },
  data() {
    return {
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          customerId: {
            label: "lang.rms.fed.clientCode",
            default: "",
            tag: "input",
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          instanceId: {
            label: "lang.rms.fed.warehouseCode",
            default: "",
            tag: "input",
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          licenseKey: {
            label: "lang.rms.fed.license",
            default: "",
            tag: "input",
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
          {
            label: "lang.rms.fed.importLicense",
            handler: "on-import",
            type: "primary",
          },
        ],
      },
      form: {
        customerId: "",
        instanceId: "",
        licenseKey: "",
      },
      rulesObj: {
        customerId: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
        instanceId: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
        licenseKey: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
      },
      basicForm: {},
      deviceInfo: "",
      bindIps: "",
      dialogVisible: false,
      tableConfig: {
        attrs: {
          // height: '100%'
          border: true,
          index: true,
        },
        isPagination: false,
        columns: [
          {
            label: "lang.rms.fed.listSerialNumber",
            "show-overflow-tooltip": true,
            align: "center",
            width: "50px",
            slot: "number",
          },
          {
            label: "IP",
            "show-overflow-tooltip": true,
            align: "center",
            width: "240px",
            prop: "ip",
          },
          {
            label: "PORT",
            "show-overflow-tooltip": true,
            align: "center",
            width: "240px",
            prop: "port",
          },
        ],
        data: [],
        pageSize: 10,
        total: 0,
      },
      list: [],
      tablePage: null,
    };
  },
  created() {
    this.getServersId();
    this.qujeryLicenseInfo();
  },
  methods: {
    // 格式化时间
    setTime(value) {
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    // license信息接口请求
    qujeryLicenseInfo() {
      $req.get("/athena/license/queryLicenseInfo", {}, { intercept: false }).then(res => {
        this.basicForm = res.data || {};
        this.bindIps = this.basicForm.bindIps;
        if (this.basicForm.bindIps) {
          const list = [];
          let arr = this.basicForm.bindIps.split(";");
          arr.forEach(item => {
            list.push({
              ip: item.split(":")[0],
              port: item.split(":")[1],
            });
          });
          this.tableConfig.data = list;
          this.list = list;
        }
        if (res.code && res.code !== 2501 && res.code !== 2505) {
          const errorMsg = $utils.Tools.transMsgLang(res.msg);
          this.errorMsg = errorMsg;
          this.$error(errorMsg);
        }
      });
    },
    // 设备信息接口请求
    getServersId() {
      $req.get("/athena/license/getServersId").then(res => {
        console.log(res);
        if (res.code) return;
        this.deviceInfo = res.data;
      });
    },
    // 绑定设备信息
    onDevice() {
      this.dialogVisible = true;
    },
    onClose(type) {
      this.dialogVisible = false;
      if (type === "success") {
        this.getServersId();
        this.qujeryLicenseInfo();
      }
    },
    onReset() {
      this.$refs.importForm.resetFields();
    },
    onImport() {
      this.$refs.importForm.validate().then(data => {
        if (data) {
          const params = {
            ...data,
          };
          $req.post("/athena/license/importLicense", params).then(res => {
            if (res.code === 0) {
              this.basicForm = res.data || {};
              this.$success(res.msg);
              this.$EventBus.$emit("checkLicense", true);
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="less">
.license-manage {
  .table-content {
    padding-top: 10px;
  }
  .handle-btn {
    padding: 20px 0;
    text-align: center;

    .gp-button {
      width: 120px;
    }
  }
  .import-content {
    margin-bottom: 10px;
  }
  .title {
    background: #efefef;
    padding: 6px 10px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    font-size: 16px;
    &::before {
      content: "";
      display: inline-block;
      height: 18px;
      width: 4px;
      background: #409eff;
      margin-right: 4px;
    }
  }
  .content {
    padding-left: 40px;
  }
  .customize-table {
    width: 532px;
  }
}
/deep/ .gp-table__inner-wrapper::before {
  background-color: #ccc;
}
</style>
