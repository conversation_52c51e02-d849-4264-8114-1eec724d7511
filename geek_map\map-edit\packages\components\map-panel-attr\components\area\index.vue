<template>
  <div class="attrPanel">
    <TabFormBase ref="baseTabsRef" :fromData="curSelectNode" :title="$t(areaTitle)" :tabs="tabData" @change="change">
      <template #coordinatesSlot> </template>
      <template #controlPointSlot> </template>
    </TabFormBase>

    <el-row :gutter="20" class="btns" v-if="isBtn">
      <el-col class="btnBox" :span="12">
        <el-button class="btn" @click="canceloneWayStreet">{{ $t("lang.rms.fed.cancel") }}</el-button>
      </el-col>
      <el-col class="btnBox" :span="12">
        <el-button class="btn" type="primary" @click="createWayStreet">{{ $t("lang.rms.fed.confirm") }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, onMounted, onUnmounted } from "vue";
import TabFormBase from "@packages/components/map-form/tabBase.vue";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";
import { getTabConf, changeBaseTabData } from "../common";
import * as NODETYPE from "@packages/configure/dict/nodeType";
import { defOperationFn } from "@packages/hook/useEvent";
import { useEditMap, setCoverPoints } from "@packages/hook/useEdit";
import { sequenceNextId, generatorNodeDirection } from "@packages/api/map";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());
const editMap = useEditMap();
const appStore = useAppStore();

const props = defineProps<{
  panelConf?: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
  title: string;
}>();

const hiddenTabs: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenTabs || []);
const hiddenCodes: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenCodes || []);
const disabledCodes: ComputedRef<string[]> = computed(() => props.panelConf?.disabledCodes || []);

const isAreaSingleLane: ComputedRef<boolean> = computed(() => {
  return attrStoreRef.drawAreaType.value === NODETYPE.AREA_SINGLE_LANE;
});

const isBtn: ComputedRef<boolean> = computed(() => isAreaSingleLane.value);
const baseTabsRef = ref();

const tabData = computed(() => {
  return getTabConf(
    {
      hiddenTabs: hiddenTabs.value,
      hiddenCodes: hiddenCodes.value,
      disabledCodes: disabledCodes.value,
      type: NODETYPE.NODE_AREA,
    },
    attrStore,
    baseTabsRef.value,
  );
});

const curSelectNode = computed(() => {
  const curSelectNode = attrStoreRef.curNodeDataByIndex.value || {};
  return curSelectNode;
});

const areaTitle: ComputedRef<string> = computed(() => {
  let curAreaName: string = "";
  switch ((<any>curSelectNode.value).areaType || attrStoreRef.drawAreaType.value) {
    case NODETYPE.AREA_STOP:
      curAreaName = ` - ${t("lang.rms.fed.area.systemStop")}`;
      break;
    case NODETYPE.AREA_BLOCK:
      curAreaName = ` - ${t("lang.rms.map.edit.mapBlock")}`;
      break;
    case NODETYPE.AREA_TRAFFIC_LIGHT:
      curAreaName = ` - ${t("lang.rms.fed.area.trafficLights")}`;
      break;
    case NODETYPE.AREA_ROBOT:
      curAreaName = ` - ${t("lang.rms.map.edit.area.guidance.robotArea")}`;
      break;
    case NODETYPE.AREA_SHELF:
      curAreaName = ` - ${t("lang.rms.map.edit.shelfArea")}`;
      break;
    case NODETYPE.AREA_NO_STAY:
      curAreaName = ` - ${t("lang.rms.fed.area.noStay")}`;
      break;
    case NODETYPE.AREA_TASK_CONTROL:
      curAreaName = ` - ${t("lang.rms.fed.search.type.logic.area.task")}`;
      break;
    case NODETYPE.AREA_SINGLE_LANE:
      curAreaName = ` - ${t("lang.rms.fed.area.singlaneAndBridge")}`;
      break;
    case NODETYPE.AREA_TRAFFIC_CONTROL:
      curAreaName = ` - ${t("lang.rms.fed.area.trafficControl")}`;
      break;
    case NODETYPE.AREA_HIGHWAY_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.highway")}`;
      break;
    case NODETYPE.AREA_CUSTOM_AREA:
      curAreaName = ` - ${t("lang.rms.fed.area.customArea")}`;
      break;
    case NODETYPE.AREA_SORTING_AREA:
      curAreaName = ` - ${t("lang.rms.fed.sortingArea")}`;
      break;
    case NODETYPE.AREA_RESTRICT_BIG_ARC_AREA:
      curAreaName = ` - ${t("lang.rms.fed.area.restrictBigArcArea")}`;
      break;
    // case NODETYPE.AREA_EMPTYING_AREA:
    //   curAreaName = ` - ${t('lang.rms.fed.area.cleaningArea')}`;
    //   break;
    case NODETYPE.CLEAR_ROBOT_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.clear.area.robot")}`;
      break;
    case NODETYPE.GATHERING_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.gatheringArea")}`;
      break;
    case NODETYPE.STATIC_SPEED_LIMIT_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.staticSpeedLimitArea")}`;
      break;
    case NODETYPE.REAL_TIME_SPEED_LIMIT_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.logic.area.realTimeSpeedLimitArea")}`;
      break;
    case NODETYPE.OBSTACLE_AVOIDANCE_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.obstacleAvoidanceArea")}`;
      break;
    case NODETYPE.PLC_LIMIT_AREA:
      curAreaName = ` - ${t("安全PLC避障区域")}`;
      break;
    case NODETYPE.HIGH_ALTITUDE_OBSTACLE_AREA:
      curAreaName = ` - ${t("高空避障区域")}`;
      break;
    case NODETYPE.SLAM_NAVIGATION_AREA:
      curAreaName = ` - ${t("lang.rms.fed.navigation.slam")}`;
      break;
    case NODETYPE.CLOSE_OBSTACLE_AVOIDANCE_AREA:
      curAreaName = ` - ${t("lang.rms.fed.search.type.closeObstacleAvoidanceArea")}`;
      break;
    case NODETYPE.RESOURCE_ISOLATION_AREA:
      curAreaName = ` - ${t("lang.rms.fed.area.resourceIsolation")}`;
      break;
  }

  return `${t(props.title)}${curAreaName}`;
});

function change(option: any) {
  // 如果当前没有选中的元素则不触发更新
  if ("id" in curSelectNode.value) {
    changeBaseTabData(option, attrStore);
  }
}

function getRef() {
  return baseTabsRef.value;
}

/**
 * 取消单行道, 目前只有单行道有取消和确认
 * ⭐ 需要注意 这里其他的区域也会使用到这个取消
 */
function canceloneWayStreet() {
  defOperationFn(<any>editMap.value);
}

/**
 * 创建单行道独木桥
 * ⭐ 需要注意 这里其他的区域也会使用到这个创建  需要判断一下, 有没有区域名称
 * 如果没有区域名称不能确认
 */
async function createWayStreet() {
  if (attrStore.drawAreaType === NODETYPE.AREA_SINGLE_LANE) {
    const allSelected = editMap.value?.getAllSelected();
    const cellCodes = (allSelected || []).map(item => {
      return item.properties.cellCode;
    });
    const { code, data } = await generatorNodeDirection({
      mapId: <number>appStore.mapId,
      floorId: <number>appStore.floorId,
      cellCodes,
    });

    if (code !== 0) {
      return;
    }

    const { data: nextId } = await sequenceNextId({ sequenceName: "AREA" });
    const fromData = baseTabsRef.value.getFormData();
    // 恢复操作
    defOperationFn(<any>editMap.value, {
      isDefMode: true,
    });
    editMap.value?.addElements({
      id: NODETYPE.NODE_AREA,
      data: [
        {
          ...fromData,
          id: editMap.value.createId(),
          areaType: NODETYPE.AREA_SINGLE_LANE,
          areaCode: nextId,
          areaId: nextId,
          cellCodes,
          cellDirections: data,
          nodeList:data,
          enableFollow:0,
        },
      ],
    });

    // // 恢复操作
    // defOperationFn(<any>editMap.value, {
    //   isDefMode: true
    // });
  }
}
async function areaUpdatedCb() {
  await setCoverPoints(curSelectNode.value);
}
//绑定区域面变更回调
onMounted(() => {
  editMap.value?.bindEventBus("AREA:UPDATED", areaUpdatedCb);
});
onUnmounted(() => {
  editMap.value?.offEventBus("AREA:UPDATED", areaUpdatedCb);
});
defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;

  .btns {
    bottom: 10px;
    width: 100%;
    position: absolute;

    .btnBox {
      text-align: center;
      .btn {
        width: 100px;
      }
    }
  }
}
</style>
