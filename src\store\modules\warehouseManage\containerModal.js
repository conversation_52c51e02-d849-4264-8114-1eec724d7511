export default {
  namespaced: true,
  state: {
    tabModelActive: "",
    addHJModalData: {}, // 新增加的模型
    groundData: {},
    hJModalWarn: "", // 警示语
    emptySwich: 0, // 清空  每次+1  执行一次清除
    activeIDData: "", // 当前选中id数据
    editData: {}, // tab要修改的数据
    maxModelId: 0, // 下发模型
    shelfCategoryDict: [], // 容器类别
    containerModelCategoryDict: [
      { label: "货架", value: "SHELF" },
      { label: "PopPick货架", value: "POPPICK_SHELF" },
      { label: "底面支架", value: "SHELF_HOLDER" },
      { label: "异形货架", value: "HETEROTYPE_SHELF" },
      { label: "货箱", value: "BOX" },
      { label: "托盘", value: "X_PALLET" },
      { label: "托盘架", value: "PALLET_RACK" },
    ], // 容器模型类别
    sizeTypeDict: [], // sizetype字典
  },
  getters: {
    getEmptySwich(state) {
      return state.emptySwich;
    },
  },
  actions: {
    async fetchMaxModelId({ commit }) {
      const res = await $req.get("/athena/shelfModel/getMaxId");
      commit("setMaxModelId", res.data || 0);
    },
    async fetchShelfCategory({ commit }) {
      const res = await $req.post("/athena/shelfCategory/findAll");
      commit("setShelfCategory", res.data.map(i => ({ label: i.categoryName, value: i.id })) || 0);
    },
    async fetchContainerModelCategoryDict({ commit }) {
      const res = await $req.get("/athena/containerModel/containerModelCategory");
      commit("setContainerModelCategory", res.data || []);
    },
    async findDistinctSizeType({ commit }) {
      const res = await $req.get("/athena/robot/manage/findDistinctSizeType");
      commit("setSizeType", res.data || 0);
    },
  },
  mutations: {
    setTabModelActive(state, data) {
      state.tabModelActive = String(data);
    },
    setEditData(state, data) {
      state.editData = Object.assign({}, data);
    },
    setEmptySwich(state) {
      state.emptySwich++;
    },
    setHJModalData(state, shelfData) {
      state.addHJModalData = Object.assign({}, shelfData);
      if (!shelfData.modelName) {
        state.hJModalWarn = "请填写容器模型别称";
        return;
      }

      if (!shelfData.categoryId) {
        state.hJModalWarn = "请填写货架类别";
        return;
      }

      state.hJModalWarn = "";
    },
    setGroundData(state, groundData) {
      state.groundData = Object.assign({}, groundData);
    },
    setActiveIdData(state, data) {
      state.activeIDData = data;
    },
    setMaxModelId(state, modelId) {
      state.maxModelId = modelId;
    },
    setShelfCategory(state, shelfCategory) {
      state.shelfCategoryDict = shelfCategory;
    },
    setContainerModelCategory(state, containerModelCategory) {
      state.containerModelCategoryDict = containerModelCategory.map(i => ({
        label: i.i18n,
        value: i.key,
        category: i.category,
      }));
    },
    setSizeType(state, sizeTypeList) {
      state.sizeTypeDict = sizeTypeList;
    },
  },
};
