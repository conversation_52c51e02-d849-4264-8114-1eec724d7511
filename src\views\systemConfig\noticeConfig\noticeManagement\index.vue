<template>
  <div>
    <edit-dialog
      v-if="showDialog"
      :show-dialog="showDialog"
      :dialog-data="dialogData"
      @closeDialog="ctrlDialog(false)"
      @submit="submitData"
    />
    <!-- <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
    /> -->

    <gp-table v-loading="tableLoading" class="table-style" :data="tableData" border style="width: 100%">
      <gp-table-column prop="title" :label="$t('lang.rms.email.config.configuration.item')">
        <template slot-scope="scope"> {{ $t(scope.row.title) }} </template>
      </gp-table-column>
      <gp-table-column width="400" prop="value" :label="$t('lang.rms.email.config.currentValue')"> </gp-table-column>
      <gp-table-column prop="desc" :label="$t('lang.rms.fed.describe')">
        <template slot-scope="scope"> {{ $t(scope.row.desc) }} </template>
      </gp-table-column>
    </gp-table>

    <div v-if="!isRoleGuest" class="edit-btn">
      <gp-button type="primary" style="width: 100px" @click="ctrlDialog(true)">
        {{ $t("lang.rms.fed.buttonEdit") }}
      </gp-button>
    </div>
  </div>
</template>

<script>
// 编辑dialog
import editDialog from "./components/editDialog";
export default {
  name: "NotificationManagement",
  components: { editDialog },
  data() {
    return {
      showDialog: false,
      tableLoading: false,
      // 项目名称
      appName: {
        title: "lang.rms.fed.replay.projectName",
        value: "",
        desc: "lang.rms.fed.replay.projectName",
      },
      sendMail: {
        title: "lang.rms.email.config.sender.address",
        value: "",
        desc: "lang.rms.email.config.addressAndPwd",
      },
      receiveMail: {
        title: "lang.rms.email.config.receiver.address",
        value: "",
        desc: "lang.rms.email.config.receiver.tos",
      },
      notifyRate: {
        title: "lang.rms.email.config.notice.frequency",
        value: "",
        desc: "lang.rms.email.config.notice.frequency.interval",
      },
      isNotify: {
        title: "lang.rms.email.config.notice.resolved",
        value: "",
        desc: "lang.rms.email.config.notice.switch",
      },
      // 传入dialog的数据
      dialogData: {
        isNotify: true,
      },
      // 更新邮件id
      updateId: null,
      tableConfig: {
        columns: [
          {
            label: "lang.rms.email.config.configuration.item",
            prop: "title",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          {
            label: "lang.rms.email.config.currentValue",
            prop: "value",
            width: 400,
          },
          {
            label: "lang.rms.fed.describe",
            prop: "desc",
            disabled: true,
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
        ]
      },
    };
  },
  computed: {
    tableData() {
      const { appName, sendMail, receiveMail, notifyRate, isNotify } = this;
      return [appName, sendMail, receiveMail, notifyRate, isNotify];
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  activated() {
    this.reqEmailData();
  },
  methods: {
    // 请求接口
    reqEmailData() {
      this.tableLoading = true;
      $req
        .get("/athena/email/config/getEmailConfig")
        .then(res => {
          this.tableLoading = false;
          if (!res.data) return;
          const { id, senderAddress, senderAddressPwd, inboxAddress, isNoticeResolved, frequency, emailContentParam } =
            res.data;
          const appName = emailContentParam.appName;
          const inboxAddressList = inboxAddress.map(item => item.emailAddress);
          this.updateId = id;
          this.appName.value = appName;
          this.sendMail.value = senderAddress;
          this.receiveMail.value = inboxAddressList.join(",");
          this.notifyRate.value = frequency;
          this.isNotify.value = isNoticeResolved.toString();
          // 截取发送邮件的前半段
          const emailNameIndex = senderAddress.lastIndexOf("@");
          const emailName = senderAddress.substr(0, emailNameIndex);
          this.dialogData = {
            appName,
            sendMail: emailName,
            sendMailPassword: senderAddressPwd,
            receiveMail: inboxAddressList,
            rate: frequency,
            isNotify: isNoticeResolved,
          };
        })
        .catch(err => {
          this.tableLoading = false;
        });
    },
    // 发送email内容
    submitData(submitData) {
      this.ctrlDialog(false);
      const { appName, isNotify, rate, receiveMail, sendMail, sendMailPassword } = submitData;
      const params = {
        id: this.updateId,
        inboxAddress: receiveMail.map(email => {
          return { emailAddress: email };
        }),
        isNoticeResolved: isNotify,
        frequency: rate,
        senderAddress: sendMail,
        senderAddressPwd: sendMailPassword,
        languageCode: $app.$i18n.locale,
        emailContentParam: { appName },
      };
      $req.post("/athena/email/config/updateEmailConfig", params).then(res => {
        const { code, msg } = res;
        let messageInfo = {
          type: code === 0 ? "success" : "warning",
          message: this.$t(msg),
        };
        this.$message(messageInfo);
        this.reqEmailData();
      });
    },
    // 控制dialog显隐
    ctrlDialog(flag) {
      this.showDialog = flag;
    },
  },
};
</script>
<style lang="less">
.gp-table .cell.gp-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  max-width: 400px;
  //max-width: 80px;
}
</style>
<style scoped lang="less">
.edit-btn {
  text-align: right;
  margin-top: 20px;
}
</style>
