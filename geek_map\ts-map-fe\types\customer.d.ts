/* ! <AUTHOR> at 2022/08/20 */
// 其他地方定义到的一些内容

declare var __rms_env_conf: any;
declare var _$utils: {
  __isMapDomMounted: boolean;
  /** 获取url参数 */
  getUrlParameter(name: string): string;
  /** 获取本地国际化语言 */
  getLocalLang(): string;
  /** 获取权限模式 */
  getRMSPermission(): boolean | "test";
  getRoleInfo(): string;
  /** 获取后台配置相关 */
  getRMSConfig(): any;
  /** 获取权限模式button权限列表 */
  getRMSAuthBtnList(): any;
  /** 获取权限模式Tab权限列表 */
  getRMSAuthTabList(): any;
  /** get 前端自定义配置相关 */
  getRMSFEDConfig(key?: string): any;
  /** set 前端自定义配置相关 */
  setRMSFEDConfig(key: string, data: { [propName: string]: any } | Array<any>): void;
  /** 国际化 翻译key */
  transMsgLang(localLang: any): any;
  /** 获取数据类型 */
  getDataType(data: any): string;
  /**
   * 格式化时间
   * formatDate('123243546546', "yyyy-MM-dd hh:mm:ss.S") ==> 2013-08-06 08:09:04.423
   * formatDate(new Date(), "yyyy-M-d h:m:s.S")      ==> 2013-8-6 8:9:4.18
   * formatDate(new Date(), "yyyy年M月d日")           ==> 2013年8月6日
   * @param date : 日期格式或String
   * @param fmt
   * @returns {*}
   */
  formatDate(date: any, fmt: string): string;
  /** 向父页面发送消息 */
  sendParentIframe(data: any): void;
  /** 请求下国际化接口 */
  reqLangMsg(localLang: string): Promise<any>;
  /** get请求 */
  reqGet(url: string, params?: any, headers?: any, obj?: any): Promise<any>;
  /** post请求 */
  reqPost(url: string, data?: any, headers?: any): Promise<any>;
  /** ws指令请求响应操作 */
  wsCmdResponse(body: any): void;
  /**
   * 防抖
   * @param func
   * @param wait: 间隔时间
   * @param immediate: 是否立即执行一次
   * @returns {function(): *}
   */
  debounce(func: (...args: any[]) => void, wait: number, immediate: boolean): any;
};
declare var CommonGeekMapColor: MRender.MapColorConfig;
declare var CommonGeekMapSetting: any;

// console.log("[websocket] ----- xxx::" , Date.now());
// console.log("[render] ----- xxx::" , Date.now());
// console.log("[dom] ----- xxx::" , Date.now());
// console.log("[websocket] >>>>> xxx::" , Date.now());
// console.log("[render] >>>>> xxx:: " , Date.now());
// console.log("[dom] >>>>> xxx::" , Date.now());
