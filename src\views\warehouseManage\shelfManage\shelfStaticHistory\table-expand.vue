<template>
  <gp-table :data="rowData.shelfAdjustDetailEntities">
    <gp-table-column type="index" label="#" width="70" />

    <!-- 货架号 -->
    <gp-table-column prop="shelfCode" width="80" :label="$t('lang.rms.fed.adjust.log.shelfCode')" />
    <!-- 货架分数 -->
    <gp-table-column prop="shelfScore" width="100" :label="$t('lang.rms.fed.adjust.log.shelfScore')" />
    <!-- 调整前所在区域 -->
    <gp-table-column prop="shelfOldArea" :label="$t('lang.rms.fed.adjust.log.oldArea')" />
    <!-- 调整前位置 -->
    <gp-table-column prop="shelfOldPlacement" :label="$t('lang.rms.fed.adjust.log.oldPlacement')" />
    <!-- 调整前热度 -->
    <gp-table-column prop="shelfOldZone" :label="$t('lang.rms.fed.adjust.log.oldHeat')" />
    <!-- 调整后所在区域 -->
    <gp-table-column prop="shelfNewArea" :label="$t('lang.rms.fed.adjust.log.newArea')" />
    <!-- 调整后位置 -->
    <gp-table-column prop="shelfNewPlacement" :label="$t('lang.rms.fed.adjust.log.newPlacement')" />
    <!-- 调整后热度 -->
    <gp-table-column prop="shelfNewZone" :label="$t('lang.rms.fed.adjust.log.newHeat')" />
    <!-- 开始时间 -->
    <gp-table-column prop="createTime" :label="$t('lang.rms.fed.startTime')" :formatter="formatterTime" />
  </gp-table>
</template>

<script>
export default {
  name: "TableExpand",
  props: {
    rowData: {
      type: Object,
      require: true,
    },
  },
  methods: {
    formatterTime(row, column, cellValue, index) {
      if (!cellValue) {
        return null;
      }
      return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
    },
  },
};
</script>

<style lang="less" scoped></style>
