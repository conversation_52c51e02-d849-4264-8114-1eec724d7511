<template>
  <div class="form-con">
    <gp-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-position="top"
    >
      <gp-form-item label="设备名称" prop="name">
        <gp-input v-model="ruleForm.name"></gp-input>
      </gp-form-item>
      <gp-form-item label="设备编码" prop="code">
        <gp-input v-model="ruleForm.code"></gp-input>
      </gp-form-item>
      <gp-form-item class="btn-con">
        <gp-button type="primary" @click="submitForm">保存</gp-button>
        <gp-button @click="resetForm">重置</gp-button>
      </gp-form-item>
    </gp-form>
  </div>
</template>

<script>
export default {
  name: "addDevice",
  data() {
    return {
      ruleForm: {
        code: '',
        name: ''
      },
      rules: {
        code: { required: true, message: this.$t('lang.wms.fed.pleaseAdd'), trigger: 'change' },
        name: { required: true, message: this.$t('lang.wms.fed.pleaseAdd'), trigger: 'change' },
      },
    }
  },
  mounted() {

  },
  methods:{
    submitForm() {
      this.$refs['ruleForm'].validate((valid) => {
        if(valid){
          this.addDeviceFn()
        }
      })
    },
    resetForm() {
      this.ruleForm = {
        code: '',
        name: ''
      }
      this.$refs['ruleForm'].resetFields();
    },
    async addDeviceFn() {
      this.$emit('editTrigger')
      this.$emit('closed')
    }
  }
};
</script>

<style scoped lang="less">
.form-con{
  .btn-con{
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
}
</style>
