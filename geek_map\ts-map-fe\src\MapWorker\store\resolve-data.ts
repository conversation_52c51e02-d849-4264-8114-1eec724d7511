/* ! <AUTHOR> at 2022/08/25 */

class DataResolve {
  formatRobots(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: robotData },
    storeRobots: { [propName: string]: robotData },
    storeShelves: { [propName: string]: shelfData },
  ) {
    let robots: any = null;

    let item, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayRobots 找不到位置或楼层：", item.id);
        continue;
      }

      delete item.updatedTime;
      delete item.resourceHashCode;
      // 处理货架分离
      if (item.hasOwnProperty("onloadShelfCode") && item.onloadShelfCode) {
        let storeShelf = storeShelves[item.onloadShelfCode];
        if (storeShelf) storeShelf.location = item.location;
      }

      if (!robots) robots = {};
      if (!robots[floorId]) robots[floorId] = [];
      robots[floorId].push(item);
      storeRobots[key] = item;
    }

    return { robots };
  }

  updateRobots(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: robotData },
    storeRobots: { [propName: string]: robotData },
    storeDeadLocks: { [propName: code]: { code: code; taskId: any; errorCodes: Array<any> } },
    storeShelves: { [propName: string]: shelfData },
  ) {
    // errorCodes: options["errorCodes"] || [],
    let robots: any = null;
    let isOnloadShelf = false;
    let isDeadLocks = false;
    let deadCodes: Array<number> = [];

    let item, floorId, errorCodes, storeDeadLock;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayRobots 找不到位置或楼层：", item.id);
        continue;
      }

      delete item.updatedTime;
      delete item.resourceHashCode;
      // 处理货架分离
      if (item.hasOwnProperty("onloadShelfCode") && item.onloadShelfCode) {
        let storeShelf = storeShelves[item.onloadShelfCode];
        if (storeShelf) storeShelf.location = item.location;
        isOnloadShelf = true;
      }
      // mock 了一下
      // if (key == "1100501") {
      // item.errorCodes = [12101, 12102, 12033, 12034];
      // }
      // if (key == "1100538") {
      //   item.errorCodes = [12101, 12102];
      // }
      errorCodes = item?.errorCodes || [];
      storeDeadLock = storeDeadLocks[key];
      if (errorCodes.length) {
        let isDeadLockCode = false;
        errorCodes.forEach((errorCode: number) => {
          errorCode = Number(errorCode);
          if ([12101, 12102, 12033, 12034].includes(errorCode)) {
            if (deadCodes.indexOf(errorCode) === -1) deadCodes.push(errorCode);
            isDeadLockCode = true;
          }
        });
        if (
          isDeadLockCode &&
          (!storeDeadLock || JSON.stringify(storeDeadLock.errorCodes) !== JSON.stringify(errorCodes))
        ) {
          let lockItem = {
            code: key,
            errorCodes: item["errorCodes"],
            taskId: item["taskId"],
          };
          storeDeadLocks[key] = lockItem;
          isDeadLocks = true;
        }
      } else {
        if (storeDeadLock) {
          delete storeDeadLocks[key];
          isDeadLocks = true;
        }
      }

      if (!robots) robots = {};
      if (!robots[floorId]) robots[floorId] = [];
      robots[floorId].push(item);
      storeRobots[key] = item;
    }

    return { robots, isOnloadShelf, isDeadLocks, deadCodes };
  }

  updateShelves(storeShelves: { [propName: string]: shelfData }) {
    let shelves: any = null;
    let item, floorId;
    for (let key in storeShelves) {
      item = storeShelves[key];

      floorId = item?.location?.z;
      if (!shelves) shelves = {};
      if (!shelves[floorId]) shelves[floorId] = [];
      shelves[floorId].push(item);
    }
    return { shelves };
  }

  resolveShelves(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: shelfData },
    storeShelves: { [propName: string]: shelfData },
  ) {
    let shelves: any = null;

    let item: any, floorId;
    for (let key in displays) {
      item = displays[key];
      if (item["width"] === 0 || item["length"] === 0) continue;

      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayShelves 找不到位置或楼层：", item.shelfCode);
        continue;
      }

      // TODO
      // if (item?.childContainerCodes?.length) {
      //   item.childContainerCodes.forEach((child: code) => {
      //     storeContainerCode[child] = item.shelfCode;
      //   });
      // }

      if (!shelves) shelves = {};
      if (!shelves[floorId]) shelves[floorId] = [];
      shelves[floorId].push(item);
      storeShelves[key] = item;
    }

    return { shelves };
  }

  updateRacks(displays: { [propName: string]: rackData }, storeRacks: { [propName: string]: rackData }) {
    let racks: any = null;
    let isRackUpdate = false;
    for (let key in displays) {
      storeRacks[key] = displays[key];
      isRackUpdate = true;
    }

    if (isRackUpdate) {
      // 数据有更新再执行，没有更新只配返回 null
      let item, floorId;
      for (let key in storeRacks) {
        item = storeRacks[key];

        floorId = item?.location?.z;
        if (!racks) racks = {};
        if (!racks[floorId]) racks[floorId] = [];
        racks[floorId].push(item);
      }
    }

    return { racks };
  }

  resolveRacks(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: rackData },
    storeRacks: { [propName: string]: rackData },
    storeCells: { [propName: string]: cellData },
  ) {
    let racks: any = null;

    let item, floorId, cellCode;
    for (let key in displays) {
      item = displays[key];

      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayRacks 找不到位置或楼层：", item.rackCode);
        continue;
      }

      if (!racks) racks = {};
      if (!racks[floorId]) racks[floorId] = [];

      cellCode = item?.locationCode;
      if (!item.hasOwnProperty("length")) {
        let locationCell = storeCells[cellCode];
        if (locationCell) item.length = locationCell.length;
        else item.length = 1;
      }
      if (!item.hasOwnProperty("width")) {
        let locationCell = storeCells[cellCode];
        if (locationCell) item.width = locationCell.width;
        else item.width = 1;
      }

      racks[floorId].push(item);
      storeRacks[key] = item;
    }
    return { racks };
  }

  updateStations(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: stationData },
    storeStations: { [propName: string]: stationData },
  ) {
    //  更新stopButtonPressed
    let stations: any = null;

    let item, storeItem: any, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      storeItem = storeStations[key];
      if (!floorId || !floorIds[floorId] || !storeItem) {
        // 工作站你不能中途给我做个新增啊 所以continue吧
        const warn = console.warn;
        warn("displayWorkStations 找不到位置或楼层：", item.stationId);
        continue;
      }

      let isParkListUpdate = false;
      if (item?.parkList?.length) {
        item.parkList.forEach((park: any) => {
          const storePark = storeItem.parkList.find((sPark: any) => sPark.parkId === park.parkId);
          if (
            storePark &&
            (park.isWorking !== storePark.isWorking ||
              (park.deviceCode && park.deviceState !== storePark.deviceState) ||
              (park.workStatus && park.workStatus !== storePark.workStatus) || 
              park.jobStatus !== storePark.jobStatus)
          ) {
            isParkListUpdate = true;
          }
        });
      }

      if (
        isParkListUpdate ||
        storeItem.stopButtonPressed !== item.stopButtonPressed ||
        storeItem.isWorking !== item.isWorking
      ) {
        // console.log(item);
        if (!stations) stations = {};
        if (!stations[floorId]) stations[floorId] = [];
        stations[floorId].push(item);
      }
      storeStations[key] = item;
    }
    return { stations };
  }
  resolveStations(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: stationData },
    storeStations: { [propName: string]: stationData },
  ) {
    let stations: any = null;

    let item, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayWorkStations 找不到位置或楼层：", item.stationId);
        continue;
      }

      if (!stations) stations = {};
      if (!stations[floorId]) stations[floorId] = [];
      stations[floorId].push(item);
      storeStations[key] = item;
    }

    return { stations };
  }

  updateChargers(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: chargerData },
    storeChargers: { [propName: string]: chargerData },
  ) {
    //  更新chargerStatus
    let chargers: any = null;

    let item, storeItem, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      storeItem = storeChargers[key];
      if (!floorId || !floorIds[floorId] || !storeItem) {
        // 充电站你不能中途给我做个新增啊 所以continue吧
        const warn = console.warn;
        warn("displayChargers 找不到位置或楼层：", item.chargerId);
        continue;
      }

      if (storeItem.chargerStatus !== item.chargerStatus) {
        if (!chargers) chargers = {};
        if (!chargers[floorId]) chargers[floorId] = [];
        chargers[floorId].push(item);
      }
      storeChargers[key] = item;
    }
    return { chargers };
  }
  resolveChargers(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: chargerData },
    storeChargers: { [propName: string]: chargerData },
  ) {
    let chargers: any = null;

    let item, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayChargers 找不到位置或楼层：", item.chargerId);
        continue;
      }

      if (!chargers) chargers = {};
      if (!chargers[floorId]) chargers[floorId] = [];
      chargers[floorId].push(item);
      storeChargers[key] = item;
    }
    return { chargers };
  }

  updateDevices(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: deviceData },
    storeDevices: { [propName: string]: deviceData },
    storeSystemWarning: MWorker.systemWarning,
  ) {
    let devices: any = null;
    let dmpDevices: any = {};
    let systemWarning: MWorker.systemWarning;

    let item, storeItem, floorId;
    for (let key in displays) {
      item = displays[key];
      floorId = item?.location?.z;
      storeItem = storeDevices[key];
      if (!floorId || !floorIds[floorId]) {
        // 设备你不能中途给我做个新增啊 所以continue吧
        const warn = console.warn;
        warn("displayDevices 找不到位置或楼层：", item.deviceInfoId);
        continue;
      }

      // 单独处理dmp的设备
      if (item?.type === 10) {
        if (!dmpDevices) dmpDevices = {};
        if (!dmpDevices[floorId]) dmpDevices[floorId] = [];
        dmpDevices[floorId].push(item);
        continue;
      }

      if (!storeItem) continue;
      if (storeItem.state !== item.state) {
        if (!devices) devices = {};
        if (!devices[floorId]) devices[floorId] = [];
        devices[floorId].push(item);
      }
      storeDevices[key] = item;

      if (storeSystemWarning === "red") continue;
      if (item.keySwitchOn === 1 && storeSystemWarning != "yellow") {
        // 门禁的钥匙开关打开
        systemWarning = "yellow";
      } else if (item.reset === 1 && storeSystemWarning != "blue") {
        // 每个安全装置都重置
        systemWarning = "blue";
      }
    }
    return { devices, dmpDevices, systemWarning };
  }
  resolveDevices(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: deviceData },
    storeDevices: { [propName: string]: deviceData },
    storeSystemWarning: MWorker.systemWarning,
  ) {
    let devices: any = null;
    let dmpDevices: any = {};
    let systemWarning: MWorker.systemWarning;

    let item, floorId;
    for (let key in displays) {
      item = displays[key];

      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayDevices 找不到位置或楼层：", item.deviceInfoId);
        continue;
      }

      // 单独处理dmp的设备
      if (item?.type === 10) {
        if (!dmpDevices) dmpDevices = {};
        if (!dmpDevices[floorId]) dmpDevices[floorId] = [];
        dmpDevices[floorId].push(item);
        continue;
      }

      if (!devices) devices = {};
      if (!devices[floorId]) devices[floorId] = [];
      devices[floorId].push(item);
      storeDevices[key] = item;

      if (storeSystemWarning === "red") continue;
      if (item.keySwitchOn === 1) {
        // 门禁的钥匙开关打开
        systemWarning = "yellow";
      } else if (item.reset === 1) {
        // 每个安全装置都重置
        systemWarning = "blue";
      }
    }

    return { devices, dmpDevices, systemWarning };
  }

  updateXDevices(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: wsDeviceData },
    storeXDevices: { [propName: string]: xDeviceData },
  ) {
    let xDevices: any = null;

    let item: wsDeviceData, storeItem, floorId, deviceMappings;
    for (let key in displays) {
      item = displays[key];
      deviceMappings = item?.deviceMappings || [];
      if (!deviceMappings || !deviceMappings[0]) continue;

      const { deviceCode, deviceType, deviceTaskNum } = item;

      deviceMappings.forEach((plc: any) => {
        floorId = plc?.location?.z;
        if (!floorId || !floorIds[floorId]) {
          const warn = console.warn;
          warn("displayBaseDevices 找不到位置或楼层：", deviceCode, plc.plcCode);
          return;
        }
        const xDeviceId = `${plc.plcCode}_${plc.cellCode}`;
        storeItem = storeXDevices[xDeviceId];
        if (
          storeItem.displayColourStatus == plc.displayColourStatus &&
          storeItem.status == plc.status &&
          storeItem.onLine == plc.onLine &&
          storeItem?.connectionPointInfo?.targetDevicePointType == plc?.connectionPointInfo?.targetDevicePointType
        )
          return;

        if (!xDevices) xDevices = {};
        if (!xDevices[floorId]) xDevices[floorId] = [];
        const xDevice: xDeviceData = Object.assign(plc, {
          xDeviceId,
          deviceCode,
          deviceType,
          deviceTaskNum,
          deviceMappingsNum: deviceMappings.length,
        });
        xDevices[floorId].push(xDevice);
        storeXDevices[xDeviceId] = xDevice;
      });
    }

    return { xDevices };
  }
  resolveXDevices(
    floorIds: { [propName: floorId]: boolean },
    displays: { [propName: string]: wsDeviceData },
    storeXDevices: { [propName: string]: xDeviceData },
  ) {
    let xDevices: any = null;

    let item: wsDeviceData, floorId, deviceMappings;
    for (let key in displays) {
      item = displays[key];
      deviceMappings = item?.deviceMappings || [];
      if (!deviceMappings || !deviceMappings[0]) continue;

      const { deviceCode, deviceType, deviceTaskNum } = item;
      deviceMappings.forEach((plc: any) => {
        floorId = plc?.location?.z;
        if (!floorId || !floorIds[floorId]) {
          const warn = console.warn;
          warn("displayBaseDevices 找不到位置或楼层：", deviceCode, plc.plcCode);
          return;
        }

        if (!xDevices) xDevices = {};
        if (!xDevices[floorId]) xDevices[floorId] = [];

        const xDeviceId = `${plc.plcCode}_${plc.cellCode}`;
        const xDevice: xDeviceData = Object.assign(plc, {
          xDeviceId,
          deviceCode,
          deviceType,
          deviceTaskNum,
          deviceMappingsNum: deviceMappings.length,
        });
        xDevices[floorId].push(xDevice);
        storeXDevices[xDeviceId] = xDevice;
      });
    }

    return { xDevices };
  }

  resolveKnockAreas(floorIds: { [propName: floorId]: boolean }, displays: Array<any>) {
    let knockAreas: any = null;

    let item, knockAreaApex, floorId;
    for (let i = 0, len = displays.length; i < len; i++) {
      item = displays[i];
      knockAreaApex = item?.knockAreaApex || [];
      floorId = knockAreaApex[0]?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayKnockArea 找不到位置或楼层：", item);
        continue;
      }

      if (!knockAreas) knockAreas = {};
      if (!knockAreas[floorId]) knockAreas[floorId] = [];
      knockAreas[floorId].push(item);
    }

    return { knockAreas };
  }

  resolveRealtimeObstacles(floorIds: { [propName: floorId]: boolean }, displays: Array<any>, store: Array<any>) {
    let realtimeObstacles: any = null;

    let item, floorId;
    for (let i = 0, len = displays.length; i < len; i++) {
      item = displays[i];

      floorId = item?.location?.z;
      if (!floorId || !floorIds[floorId]) {
        const warn = console.warn;
        warn("displayKnockArea 找不到位置或楼层：", item);
        continue;
      }

      if (!realtimeObstacles) realtimeObstacles = {};
      if (!realtimeObstacles[floorId]) realtimeObstacles[floorId] = [];
      realtimeObstacles[floorId].push(item);
      store.push(item);
    }

    return { realtimeObstacles };
  }

  updateCells(displays: Array<cellData>, storeCells: { [propName: string]: cellData }) {
    let cells: any = null;

    let item, storeItem, floorId, cellCode;
    for (let i = 0, len = displays.length; i < len; i++) {
      item = displays[i];
      cellCode = item.cellCode;
      storeItem = storeCells[cellCode];
      if (!storeItem) continue;

      if (item?.cellFlag !== storeItem?.cellFlag || item?.loadDirs !== storeItem?.loadDirs) {
        floorId = storeItem?.location?.z;
        if (!cells) cells = {};
        if (!cells[floorId]) cells[floorId] = [];

        Object.assign(storeItem, item);
        cells[floorId].push(storeItem);
      } else {
        Object.assign(storeItem, item);
      }
    }
    return { cells };
  }

  resolveFloorsData(floorIds: floorId[], floors: { [propName: floorId]: any }) {
    let floorsData: floorsData = {},
      floorList: MWorker.floorList[] = [],
      currentFloorIds: { [propName: floorId]: boolean } = {},
      storeCells: { [propName: string]: cellData } = {},
      storeQrCodes: { [propName: string]: code } = {},
      storeHostCellCodes: { [propName: string]: code } = {};

    floorIds.forEach(floorId => {
      const floor = floors[floorId];
      let floorItem: MWorker.floorList = { floorId, enable: false };
      if (!floor) {
        floorList.push(floorItem);
        return;
      }

      const { mapCells, mapSegments, background } = floor;
      if ((!mapCells || mapCells.length <= 0) && (!mapSegments || mapSegments.length <= 0) && !background?.hasImage) {
        floorList.push(floorItem);
        return;
      }

      let cells = [];
      let cell, cellCode, cellType;
      for (let i = 0, len = mapCells.length; i < len; i++) {
        cell = mapCells[i];
        cellType = cell["cellType"];
        cellCode = cell["cellCode"];
        if (cellType === "NULL_CELL" || cell["width"] === 0 || cell["length"] === 0) continue;
        if (cellType === "BLOCKED_CELL" || !cellCode || cellCode === "undefined") {
          cell.cellType = "BLOCKED_CELL";
        }

        cells.push(cell);
        storeCells[cell["cellCode"]] = cell;
        storeQrCodes[cell["qrCode"]] = cell["cellCode"];
        cell["hostCellCode"] && (storeHostCellCodes[cell["hostCellCode"]] = cell["cellCode"]);
      }

      floorsData[floorId] = {
        floorId,
        background: {
          hasBg: background.hasImage || false,
          width: background.width || 0,
          height: background.height || 0,
          resolution: background.resolution || 0,
          leftBottomPoint: background.leftBottomPoint || { x: 0, y: 0 },
          data: this.formateBackground(background),
        },
        cells,
        segments: mapSegments || [],
      };
      currentFloorIds[floorId] = true;
      floorItem.enable = true;
      floorList.push(floorItem);
    });

    return { floorsData, floorList, currentFloorIds, storeCells, storeQrCodes, storeHostCellCodes };
  }

  private formateBackground(bg: any) {
    // displayType: "SPLIT"
    // height: 3888
    // leftBottomPoint: {x: 16.7962, y: 25.647}
    // resolution: 0.04
    // splitImage: {splitSize: 2000, originWidth: 14190, originHeight: 3888, imageItems: Array(16), xarraySize: 2, …}
    // width: 14190
    const { resolution, splitImage } = bg || {};
    const imgs = splitImage?.imageItems || [];
    if (!imgs.length) return null;
    return imgs.map((item: any) => {
      return {
        resolution,
        location: { x: item.x, y: item.y },
        resource: item["imageText"],
        height: item["height"],
        width: item["width"],
      };
    });
  }
}
export default DataResolve;
