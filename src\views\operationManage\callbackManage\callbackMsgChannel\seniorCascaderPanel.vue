<template>
  <gp-card>
    <p slot="header">
      <span class="title">{{ $t("lang.rms.fed.callbackSelection") }}</span>
      <gp-input class="appendSearch" :placeholder="$t('lang.rms.fed.pleaseEnter')" v-model="callbackQueryText">
        <gp-button slot="append" icon="gp-icon-search" @click="expandChange"></gp-button>
      </gp-input>
    </p>

    <gp-cascader-panel
      class="callbackCascPanelSty"
      ref="cascaderPanelRef"
      v-model="cascaderSelects"
      :options="cascaderDataList"
      :props="cascaderPanelProps"
      @expand-change="expandChange"
    >
      <template slot-scope="{ node, data }">
        <div :ref="`cascader_${node.path.join('_')}`" @dblclick="handlerDoubleClick(node, data)">
          <span v-if="node.path.length === 3">{{ `${data.key}(${$t(data.description)})` }}</span>
          <span v-else>{{ $t(data.description) }}</span>
        </div>
      </template>
    </gp-cascader-panel>
  </gp-card>
</template>
<script>
export default {
  name: "seniorCascaderPanel",
  components: {},
  props: {
    // enableSelfCallback: Boolean,
    isSenior: Boolean,
    option: Object,
  },
  data() {
    return {
      cascaderSelects: [], // 选中的内容(全部)
      cascaderDataList: [], // 可选数据(全部)
      callbackQueryText: "", // 查询
      cascaderPanelProps: {
        // 联级选择器配置
        multiple: true,
        expandTrigger: "click",
        label: "description",
        value: "id",
      },
    };
  },
  computed: {
    showRefIds() {
      const { callbackQueryText, cascaderDataList } = this;
      let cascaderPanelOptions = cascaderDataList;

      // 筛选查询文本
      if (callbackQueryText) {
        cascaderPanelOptions = this.getQueryCascaderDataList(cascaderDataList, callbackQueryText) || [];
      }

      // 筛选内部回调
      // if (!enableSelfCallback) {
      // cascaderPanelOptions = this.filterEnableSelfDataList(cascaderPanelOptions, 'SELF_CALLBACK');
      // }
      return this.getRefIdsByTree(cascaderPanelOptions);
    },
    isAdd() {
      return !this.option.id;
    },
  },
  watch: {
    enableSelfCallback() {
      this.expandChange();
    },
    isSenior() {
      this.expandChange();
    },
  },
  async created() {
    await this.getCascaderDataList(this.option?.id);
  },
  methods: {
    reset() {
      this.callbackQueryText = "";
    },

    // 这里处理双击事件
    handlerDoubleClick(node) {
      // 禁用锁定
      if (node.isDisabled) return;
      const key = `cascader_${node.path.join("_")}`;
      const refEl = this.$refs[key];
      refEl.parentNode.parentNode.querySelector("input").click();
    },

    getCasTreeChildPaths(node, list = []) {
      // 禁用和半选中
      if (node.children?.length) {
        node.children.forEach(itemNode => {
          this.getCasTreeChildPaths(itemNode, list);
        });
      } else if (!(node.isDisabled && !node.checked)) {
        list.push(node.path);
      }

      return list;
    },

    expandChange() {
      this.$nextTick(() => {
        const { showRefIds } = this;
        const treeRefs = this.$refs;
        Object.keys(treeRefs).forEach(treeRefId => {
          if (treeRefId.startsWith("cascader_") && treeRefs[treeRefId]) {
            const isShow = showRefIds.includes(treeRefId);
            const treeEl = treeRefs[treeRefId].parentNode.parentNode;
            treeEl.style.display = isShow ? "" : "none";
          }
        });
      });
    },

    // tree转平铺
    getRefIdsByTree(list) {
      let pathList = [];
      function next(list, ids = []) {
        list.forEach(item => {
          const idList = [...ids, item.id];
          pathList.push(`cascader_${idList.join("_")}`);
          if (item.children?.length) {
            next(item.children, idList);
          }
        });
      }

      next(list);

      return pathList;
    },

    /**
     * 这个函数能够过滤 模糊搜索的 queryText
     * @param {*} cascaderDataList
     * @param {*} queryText
     */
    getQueryCascaderDataList(cascaderDataList, queryText) {
      return cascaderDataList
        .filter(item => {
          return this.getIsCascaderDataByQuery(item, queryText);
        })
        .map(({ children, ...item }) => {
          if (children?.length) {
            item.children = this.getQueryCascaderDataList(children, queryText);
          }
          return item;
        });
    },

    /**
     * 检查当前数据是否匹配queryText
     */
    getIsCascaderDataByQuery(cascaderDataItem, queryText) {
      let flag = false;

      if (this.$t(cascaderDataItem.description).includes(queryText) || cascaderDataItem.key.includes(queryText)) {
        return true;
      }

      const is = list => {
        list.forEach(({ children, key, description }) => {
          if (children?.length) {
            is(children);
          }

          if (this.$t(description).includes(queryText) || key.includes(queryText)) {
            flag = true;
          }
        });
      };
      cascaderDataItem.children?.length && is(cascaderDataItem.children);
      return flag;
    },

    /**
     * 这个函数能够过滤 模糊搜索的 queryText
     * @param {*} cascaderDataList
     * @param {*} queryText
     */
    filterEnableSelfDataList(cascaderDataList, filterKey) {
      return cascaderDataList
        .filter(item => {
          return item.key !== filterKey;
        })
        .map(({ children, ...item }) => {
          if (children?.length) {
            item.children = this.filterEnableSelfDataList(children, filterKey);
          }
          return item;
        });
    },

    async getCascaderDataList(channelPrimaryKey = "") {
      const { data } = await $req.get(
        `/athena/apiCallback/getCallbackSelectable?channelPrimaryKey=${channelPrimaryKey}`,
      );
      this.cascaderDataList = this.parseRemoveNotDataChildren(data);
      this.cascaderSelects = this.getSelectList(data);
      const { cascaderPanelRef } = this.$refs;
      cascaderPanelRef && (cascaderPanelRef.activePath = []);
    },

    /**
     * 获取选中的key
     * @param {} dataList
     */
    getSelectList(dataList) {
      const isAdd = this.isAdd;
      let activeIdPath = [];
      function getActiveByList(list, ids = [], active) {
        list.forEach(item => {
          const idList = [...ids];
          const isForceTrue = active;
          if (item.value === "1" || isForceTrue) {
            idList.push(item.id);
            if (item.children?.length) {
              getActiveByList(item.children, idList, isForceTrue);
            } else {
              activeIdPath.push(idList);
            }
          }
        });
      }

      getActiveByList(dataList);
      return activeIdPath;
    },
    /**
     * 这个函数能够清除无效的children, 递归
     * @param {*} cascaderDataList
     */
    parseRemoveNotDataChildren(cascaderDataList) {
      return cascaderDataList.map(({ children, extendJson, ...item }) => {
        item.disabled = !!(extendJson || {})?.isRequired;
        if (children?.length) {
          item.children = this.parseRemoveNotDataChildren(children);
        }
        return item;
      });
    },

    getSaveCascderData() {
      // enableSelfCallback
      const { cascaderSelects, cascaderDataList } = this;
      const selectSet = new Set();

      cascaderSelects.forEach(itemList => {
        itemList.forEach(item => {
          selectSet.add(item);
        });
      });

      const selectArr = [...selectSet];

      const saveNodeListData = [];
      function recursionSelect(list, isNSelectedByParent) {
        list.forEach(item => {
          let isNSelected = false;

          if (isNSelectedByParent) {
            // 这里说明父节点未选中, 那子节点就不需要做判断了
            saveNodeListData.push({ id: item.id, value: "0" });
          } else if (selectArr.includes(item.id)) {
            // if (!enableSelfCallback && item.key === 'SELF_CALLBACK') {
            //   isNSelected = true;
            //   saveNodeListData.push({ id: item.id, value: '0' })
            // } else {
            // }
            saveNodeListData.push({ id: item.id, value: "1" });
          } else {
            isNSelected = true;
            saveNodeListData.push({ id: item.id, value: "0" });
          }

          if (item.children?.length) {
            recursionSelect(item.children, isNSelected);
          }
        });
      }

      recursionSelect(cascaderDataList);

      return saveNodeListData;
    },
  },
};
</script>
<style scoped>
.appendSearch {
  width: 200px;
  float: right;
}

.title {
  font-size: 16px;
  height: 32px;
  line-height: 32px;
  font-weight: 900;
}
</style>

<style>
.gp-cascader-node.in-active-path {
  background: #f5f7fa;
}

.callbackCascPanelSty .gp-cascader-menu {
  height: 320px;
  user-select: none;
}

.callbackCascPanelSty .gp-cascader-menu:last-child {
  flex: 1;
}

.callbackCascPanelSty .gp-cascader-menu__wrap {
  height: 100% !important;
}
</style>
