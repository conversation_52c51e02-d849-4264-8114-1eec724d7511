<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav
      :block="true"
      :nav-list="permissionNavList"
      @select="tabsNavChange"
      class="callback-management-nav"
    />
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import MsgConfiguration from "./callbackMsgConfigure";
import ChannelConfiguration from "./callbackMsgChannel";
export default {
  components: { MsgConfiguration, ChannelConfiguration },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabCallbackManagementChannelPage",
          id: "ChannelConfiguration",
          text: "lang.rms.fed.CallbackMsgChannelConfiguration",
        },
        {
          permissionName: "TabCallbackManagementMsgPage",
          id: "MsgConfiguration",
          text: "lang.rms.fed.CallbackMsgConfiguration",
        },
      ],
      activeName: "",
    };
  },

  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "callbackManagement"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="less" scoped>
.callback-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
<style lang="less">
.callback-manage-panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);
  .callback-manage-panel-wrap__table {
    flex: 1;
  }
}
</style>
