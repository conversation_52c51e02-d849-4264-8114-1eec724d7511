/* ! <AUTHOR> at 2023/04/20 */

type charger = { element: any; options: mChargerData };
type chargerPosition = { code: code; scale: number; bounds: any };

class ChargersData implements MRender.MapData {
  private mapData: { [propName: code]: charger } = {};

  setData(code: code, data: charger) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  getPositions() {
    const data = this.mapData;

    let positions: chargerPosition[] = [];
    let element, position: chargerPosition;
    for (let key in data) {
      element = data[key]?.element;
      if (!element) continue;

      const code = element.name.toString();
      const bounds = element.getBounds();
      let tWidth = (code.length / 2) * 12; //四个数字 大伟嫌太小了 又不能写死 就减半吧
      if (tWidth < 12) tWidth = 12;

      position = {
        code,
        scale: bounds.width / tWidth,
        bounds,
      };

      positions.push(position);
    }

    return positions;
  }

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  getAll() {
    return this.mapData;
  }

  uninstall() {
    const data = this.mapData;

    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default ChargersData;
