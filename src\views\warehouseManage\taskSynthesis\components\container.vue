<template>
  <div>
    <gp-form ref="searchForm" :inline="true" class="demo-form-inline" label-position="left" :model="searchForm">
      <gp-form-item :label="$t('lang.rms.fed.containerId')">
        <gp-input v-model="searchForm.boxCode" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.selectDate')">
        <gp-date-picker v-model="searchForm.date" type="date" :placeholder="$t('lang.rms.fed.selectDate')">
        </gp-date-picker>
      </gp-form-item>
      <gp-form-item class="align-bottom">
        <gp-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</gp-button>
        <gp-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</gp-button>
      </gp-form-item>
    </gp-form>

    <gp-table ref="selectStations" v-loading="loading" :data="stationList" style="width: 100%">
      <gp-table-column type="expand">
        <template slot-scope="props">
          <template v-if="props.row.robotTasks && props.row.robotTasks.length">
            <gp-table ref="selectStations" :data="props.row.robotTasks" style="width: 100%">
              <gp-table-column
                v-for="item in childCloumnList"
                :key="item.prop"
                :prop="item.prop"
                :label="$t(item.label)"
              />
            </gp-table>
          </template>
        </template>
      </gp-table-column>
      <gp-table-column v-for="item in cloumnList" :key="item.prop" :prop="item.prop" :label="$t(item.label)" />
    </gp-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        date: "",
        boxCode: "",
      },
      loading: false,
      stationList: [],
    };
  },

  computed: {
    cloumnList() {
      return [
        "whStageId",
        "containerCode",
        "containerType",
        "stageType",
        "stageStatus",
        "jobStatus",
        "stageReceivedTime",
        "stageDoneTime",
        "stationId",
        "jobSourceType",
        "startContainerLocation",
        "priority",
        "businessSequence",
      ].map(item => {
        return { label: item, prop: item };
      });
      // return [
      //   // 任务ID
      //   { label: "lang.venus.web.common.taskId", prop: "whJobId" },
      //   // 任务阶段ID
      //   { label: this.$t('lang.rms.web.monitor.robot.taskPhase') + "ID", prop: "whStageId" },
      //   // 容器编号
      //   { label: "lang.rms.fed.containerId", prop: "containerType" },
      //   // 容器类型
      //   { label: "lang.rms.web.container.containerType", prop: "stageType" },
      //   // 任务指令
      //   { label: "lang.rms.fed.taskInstruction", prop: "stageStatus" },
      //   // 任务状态
      //   { label: "lang.rms.web.monitor.robot.taskState", prop: "jobStatus" },
      //   { label: "容器起点位置", prop: "stageReceivedTime" },
      //   { label: "容器终点位置", prop: "stageDoneTime" },
      //   { label: "任务接收时间", prop: "stationId" },
      //   { label: "任务结束时间", prop: "jobSourceType" },
      //   { label: "目标工作站ID", prop: "startContainerLocation" },
      //   { label: "lang.rms.web.monitor.robot.robotPriority", prop: "priority" },
      //   { label: "业务优先级", prop: "businessSequence" },
      // ]
    },
    childCloumnList() {
      return [
        "containerCode",
        "createTime",
        "destLatticeCode",
        "doneTime",
        "endAreaId",
        "endCellCode",
        "multiEndCellCodes",
        "robotId",
        "startCellCode",
        "startLatticeCode",
        "taskAction",
        "taskId",
        "taskPhase",
        "taskStatus",
        "taskType",
        "workstationId",
      ].map(item => {
        return { label: item, prop: item };
      });
      // return [
      //   { label: "lang.mb.robotManage.robotId", prop: "robotId" },
      //   { label: this.$t("lang.rms.fed.taskId") + "ID", prop: "uniqueRobotId" },
      //   { label: "lang.venus.web.common.taskSource", prop: "businessSequence" },
      //   { label: "阶段类型", prop: "stageType" },
      //   { label: "lang.rms.web.monitor.robot.taskType", prop: "jobType" },
      //   { label: "lang.rms.web.monitor.robot.taskState", prop: "jobStatus" },
      //   { label: "lang.rms.fed.containerId", prop: "jobSourceType" },
      //   { label: "lang.rms.web.container.containerType", prop: "priority" },
      // ]
    },
  },
  async created() {
    await this.onSubmit();
  },

  methods: {
    resetForm() {
      this.searchForm = { date: "", boxCode: "" };
      this.onSubmit();
    },
    async onSubmit() {
      this.loading = true;
      const params = { ...this.searchForm };
      if (params.date) {
        params.date = $utils.Tools.formatDate(params.date, "yyyy-MM-dd");
      }
      const { data } = await $req.post("/athena/stats/query/job/select/box", params, { intercept: false });

      // 目前是要展示所有的字段
      this.loading = false;
      this.stationList = data.allWhJobStages || [];
    },
  },
};
</script>

<style></style>
