import i18n from "@lang"; // 引入国际化
export const RobotInstanceForm = [
  {
    name: "robotId",
    label: i18n.t("lang.mb.robotManage.robotId"),
    component: "GpInput",
    clearable: true,
    type: "number",
    span: 6,
    placeholder: i18n.t("lang.rms.api.result.warehouse.pleaseEnterRobotID"),
    onKeypress: "return (/[d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8",
  },
  {
    name: "hostCode",
    label: i18n.t("lang.rms.api.result.warehouse.robotAlias"),
    component: "GpInput",
    span: 6,
    placeholder: i18n.t("lang.rms.api.result.warehouse.pleaseEnterRobotAlias"),
  },
  {
    name: "robotModelId",
    label: i18n.t("lang.rms.api.result.warehouse.robotModel"),
    component: "GpSelect",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseChoose"),
    data: [],
  },
  {
    name: "manageStatus",
    label: i18n.t("lang.rms.api.result.warehouse.robotManageStatus"),
    component: "GpSelect",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseChoose"),
    data: [
      { value: "", label: i18n.t("lang.rms.fed.whole") },
      { value: "UNREGISTERED", label: i18n.t("lang.rms.api.result.warehouse.unRegister") },
      { value: "REGISTERED", label: i18n.t("lang.rms.api.result.warehouse.register") },
      { value: "BLOCKED", label: i18n.t("lang.rms.api.result.warehouse.stop") },
    ],
  },
  {
    name: "workStatus",
    label: i18n.t("lang.rms.api.result.warehouse.robotWorkStatus"),
    component: "GpSelect",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseChoose"),
    data: [
      { value: "", label: i18n.t("lang.rms.fed.whole") },
      { value: "NORMAL", label: i18n.t("lang.rms.api.result.warehouse.normalPresence") },
      { value: "SLEEPING", label: i18n.t("lang.rms.api.result.warehouse.sleep") },
      { value: "REMOVE_FROM_SYSTEM", label: i18n.t("lang.rms.api.result.warehouse.departure") },
    ],
  },
  {
    name: "controlStatus",
    label: i18n.t("lang.rms.api.result.warehouse.lockedStatus"),
    component: "GpSelect",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseChoose"),
    data: [
      { value: "", label: i18n.t("lang.rms.fed.whole") },
      { value: "NORMAL", label: i18n.t("lang.rms.api.result.warehouse.normal") },
      { value: "STOP", label: i18n.t("lang.rms.api.result.warehouse.robotStop") },
      { value: "SUSPEND", label: i18n.t("lang.rms.api.result.warehouse.robotStopAndStopTask") },
      { value: "LOCK", label: i18n.t("lang.rms.api.result.warehouse.robotLock") },
    ],
  },
  {
    name: "commStatus",
    label: i18n.t("lang.rms.api.result.warehouse.connectionStatus"),
    component: "GpSelect",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseChoose"),
    data: [
      { value: "", label: i18n.t("lang.rms.fed.whole") },
      { value: "NORMAL", label: i18n.t("lang.venus.common.dict.onlineStatus.online") },
      { value: "OFFLINE", label: i18n.t("lang.venus.common.dict.onlineStatus.dropLine") },
      { value: "DISCONNECTED", label: i18n.t("lang.venus.common.dict.onlineStatus.reconecting") },
    ],
  },
];

export const RobotInstanceColumns = [
  { label: i18n.t("lang.mb.robotManage.robotId"), prop: "robotId", showLock: true, nowrap: true },
  {
    label: i18n.t("lang.rms.api.result.warehouse.robotAlias"),
    prop: "hostCode",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.robotModel"),
    prop: "robotModelEntity",
    nowrap: true,
    formatter: (record, column) => {
      return record[column]?.product;
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.robotManageStatus"),
    prop: "manageStatus",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      switch (cellValue) {
        case "UNREGISTERED":
          return i18n.t("lang.rms.api.result.warehouse.unRegister");
        case "REGISTERED":
          return i18n.t("lang.rms.api.result.warehouse.register");
        case "BLOCKED":
          return i18n.t("lang.rms.api.result.warehouse.stop");
        default:
          return "--";
      }
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.robotWorkStatus"),
    prop: "workStatus",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      switch (cellValue) {
        case "NORMAL":
          return i18n.t("lang.rms.api.result.warehouse.normalPresence");
        case "SLEEPING":
          return i18n.t("lang.rms.api.result.warehouse.sleep");
        case "REMOVE_FROM_SYSTEM":
          return i18n.t("lang.rms.api.result.warehouse.departure");
        default:
          return "--";
      }
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.lockedStatus"),
    prop: "controlStatus",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      switch (cellValue) {
        case "NORMAL":
          return i18n.t("lang.rms.api.result.warehouse.normal");
        case "STOP":
          return i18n.t("lang.rms.api.result.warehouse.robotStop");
        case "SUSPEND":
          return i18n.t("lang.rms.api.result.warehouse.robotStopAndStopTask");
        case "LOCK":
          return i18n.t("lang.rms.api.result.warehouse.robotLock");
        default:
          return "--";
      }
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.connectionStatus"),
    prop: "commStatus",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      switch (cellValue) {
        case "NORMAL":
          return i18n.t("lang.venus.common.dict.onlineStatus.online");
        case "OFFLINE":
          return i18n.t("lang.venus.common.dict.onlineStatus.dropLine");
        case "DISCONNECTED":
          return i18n.t("lang.venus.common.dict.onlineStatus.reconecting");
        default:
          return "--";
      }
    },
  },
  { label: i18n.t("lang.rms.api.result.warehouse.robotFirmwareVersion"), prop: "firmwareVersion", nowrap: true },
  { label: i18n.t("lang.rms.api.result.warehouse.productionBatch"), prop: "productionBatch", nowrap: true },
  {
    label: i18n.t("lang.rms.api.result.warehouse.lastChargingTime"),
    prop: "lastChargeTime",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
      else return "--";
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.powerOnTime"),
    prop: "uptime",
    nowrap: true,
    formatter: (record, column) => {
      const cellValue = record[column];
      if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
      else return "--";
    },
  },
  {
    label: i18n.t("lang.rms.api.result.warehouse.offTime"),
    prop: "lastCommTime",
    width: "180px",
    formatter: (record, column) => {
      const cellValue = record[column];
      if (cellValue) return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
      else return "--";
    },
  },
  {
    label: i18n.t("lang.rms.fed.listOperation"),
    showSetting: true,
    fixed: true,
    slotName: "operateBth",
    width: 180,
  },
];
