<template>
  <geek-main-structure>
    <div class="header">
      <span class="title">{{ $t("lang.venus.web.common.modelName") }}：{{ info?.modelName }}</span>
      <span class="title">{{ $t("lang.venus.web.common.modelCode") }}：{{ info?.modelCode }}</span>
    </div>
    <geek-tabs-nav
      :nav-list="navList"
      :default-active="activeNavId"
      @select="tabsNavChange"
    />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
    >
      <!--    <geek-customize-table-->
      <!--      :table-config="tableConfig"-->
      <!--      :data="tableData"-->
      <!--      :page="tablePage"-->
      <!--    >-->
      <template #inputParams="{ row }">
        <div v-if="row.inputParams?.length">
          <gp-tag
            v-for="(p,index) in row.inputParams"
            :key="index"
            effect="dark"
            style="margin-left: 4px"
          >
            {{ p }}
          </gp-tag>
        </div>
        <p v-else>-</p>
      </template>
      <template #remark="{ row }">
        <div v-if="row.remark">
          <span>{{ row.remark }}</span>
        </div>
        <span v-else>-</span>
      </template>
    </geek-customize-table>
        <gp-dialog
          title="新增应用程序接口"
          :visible.sync="addApplicationFlag"
          width="25%"
        >
          <add-application-dialog
            v-if="addApplicationFlag"
            @save="save"
          >
          </add-application-dialog>
        </gp-dialog>
        <gp-dialog
          title="新增事件回调接口"
          :visible.sync="addCallbackFlag"
          width="25%"
        >
          <add-callback-dialog
            v-if="addCallbackFlag"
            @save="save"
          >
          </add-callback-dialog>
        </gp-dialog>
  </geek-main-structure>
</template>

<script>
import { getDeviceModelDetail } from "./api";
import { mapGetters } from "vuex";
import AddCallbackDialog from "./components/addCallbackDialog";
import AddApplicationDialog from "./components/addApplicationDialog";
import deviceMixin from "../deviceMixin/deviceMixin";

export default {
  name: "deviceModelDetail",
  mixins: [deviceMixin],
  computed: {
    // ...mapGetters('device',['deviceModelInfo'])
    showCallBackBtn() {
      return this.activeNavId === "application";
    },
    isEdit() {
      return String(this.builtIn) === "0";
    },
  },
  components: { AddApplicationDialog, AddCallbackDialog },
  data() {
    return {
      addApplicationFlag: false,
      addCallbackFlag: false,
      info: null,
      tableData: [],
      activeNavId: "function",
      navList: [
        {
          id: "function",
          name: "lang.rms.web.device.deviceModelFunction",
          text: "lang.rms.web.device.deviceModelFunction",
          columns: [
            { label: "id", prop: "id", width: "60" },
            { label: "lang.rms.web.device.functionName", prop: "name" },
            { label: "lang.rms.web.device.functionCode", prop: "code" },
            { label: "lang.venus.web.common.innerParam", prop: "inputParams", slotName: "inputParams" },
            { label: "lang.rms.web.map.version.mapChange", prop: "remark", slotName: "remark" },
          ],
        },
        {
          id: "status",
          name: "lang.rms.web.device.deviceModelProperties",
          text: "lang.rms.web.device.deviceModelProperties",
          columns: [
            { label: "id", prop: "id", width: "60" },
            { label: "lang.rms.web.device.attributeName", prop: "statusName" },
            { label: "lang.rms.web.device.attributeCode", prop: "statusCode" },
            { label: "lang.rms.web.device.siteType", prop: "siteType" },
            { label: "lang.rms.web.map.version.mapChange", prop: "remark", slotName: "remark" },
          ],
        },
      ],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          // index: true,
        },
        actions: [],
        columns: [],
      },
    };
  },
  watch: {
    // activeNavId(id) {
    //   this.compColumn(id)
    // }
  },
  activated() {
    this.compColumn(this.activeNavId);
    this.getList();
  },
  methods: {
    async getList() {
      const id = this.$route.query.id;
      const res = await getDeviceModelDetail({ id });
      const { code, data } = res;
      if (code) return;
      this.info = data;
      if (this.activeNavId === "function") {
        this.tableData = this.info.deviceModelFunctionVos;
      } else {
        this.tableData = this.info.deviceModelStatusVos;
      }
      // this.tableData = this.formatTable(this.tableData)
    },
    tabsNavChange(id) {
      this.activeNavId = id;
      this.compColumn(id);
      if (!this.info) return;
      if (this.activeNavId === "function") {
        this.tableData = this.info.deviceModelFunctionVos;
      } else {
        this.tableData = this.info.deviceModelStatusVos;
      }
      // this.tableData = this.formatTable(this.tableData)
    },
    save() {

    },
  },
};
</script>

<style scoped lang="less">
.header {
  border-left: 3px solid #4693e8;

  .title {
    padding-left: 10px;
    font-weight: 600;
    color: #545454;
  }
}
</style>
