<template>
  <gp-dialog
    :title="$t('lang.rms.box.changeBoxPlace')"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    :before-close="close"
    :close-on-click-modal="false"
    width="70%"
  >
    <gp-form ref="ruleForm" :inline="true" label-position="top" label-width="80px">
      <gp-form-item :label="$t('lang.rms.box.boxCode')" prop="boxCode">
        <gp-input :value="formData.boxCode" type="text" readonly />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.relocation')" prop="changeType">
        <gp-select v-model="formData.changeType">
          <gp-option v-for="item in changeTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </gp-select>
      </gp-form-item>
      <gp-form-item v-if="formData.changeType === '1'" :label="$t('lang.rms.fed.latticeCode')">
        <gp-input v-model="formData.latticeCode" />
      </gp-form-item>
      <gp-form-item v-if="formData.changeType === '2'" :label="$t('lang.rms.fed.robot') + 'ID'">
        <gp-input v-model="formData.robotId" />
      </gp-form-item>
      <gp-form-item v-if="formData.changeType === '2'" :label="$t('lang.rms.box.robotLayer')">
        <gp-input v-model="formData.robotLayer" />
      </gp-form-item>
      <gp-form-item style="width: 60px; vertical-align: bottom">
        <gp-button
          type="primary"
          :disabled="
            (formData.changeType === '1' && !formData.latticeCode) ||
            (formData.changeType === '2' && (!formData.robotId || !formData.robotLayer))
          "
          @click="save"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </gp-form-item>
    </gp-form>

    <gp-table :data="boxTaskList" style="width: 100%">
      <gp-table-column type="index" />
      <gp-table-column prop="robotId" :label="$t('lang.rms.fed.robot') + 'ID'" width="100px" />
      <gp-table-column prop="taskId" :label="$t('lang.rms.box.task') + 'ID'" />
      <gp-table-column :label="$t('lang.rms.fed.taskType')">
        <template slot-scope="scope">{{ $t(scope.row.instructionCode) }}</template>
      </gp-table-column>
      <gp-table-column prop="latticeCode" :label="$t('lang.rms.box.boxGrid')" />
      <gp-table-column prop="createTime" :label="$t('lang.rms.fed.startTime')">
        <template slot-scope="scope">
          <p>{{ scope.row.createTime | dateFilter }}</p>
        </template>
      </gp-table-column>
      <gp-table-column prop="executeTime" :label="$t('lang.rms.fed.endTime')">
        <template slot-scope="scope">
          <p>{{ scope.row.executeTime | dateFilter }}</p>
        </template>
      </gp-table-column>
    </gp-table>
  </gp-dialog>
</template>

<script>
// TODO 优化当前弹出框
export default {
  name: "PackQueryEditDialog",
  data() {
    return {
      dialogVisible: false,
      formData: {
        changeType: "1",
        boxCode: "",
        latticeCode: "",
        robotId: "",
        robotLayer: "",
      },

      boxTaskList: [],
      changeTypeList: [
        {
          label: this.$t("lang.rms.fed.lattice"),
          value: "1",
        },
        {
          label: this.$t("lang.rms.fed.robot"),
          value: "2",
        },
      ],
    };
  },
  filters: {
    dateFilter(value) {
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
  },
  methods: {
    open(data) {
      this.formData.boxCode = data.boxCode;
      this.getBoxTaskList();
      this.dialogVisible = true;
    },
    close() {
      this.formData = {
        changeType: "1",
        boxCode: "",
        latticeCode: "",
        robotId: "",
        robotLayer: "",
      };
      this.boxTaskList = [];
      this.dialogVisible = false;
    },
    // 保存
    save() {
      let params;
      if (this.formData.changeType === "1") {
        params = {
          boxCode: this.formData.boxCode,
          latticeCode: this.formData.latticeCode,
        };
      } else {
        params = {
          boxCode: this.formData.boxCode,
          robotId: this.formData.robotId,
          robotLayer: this.formData.robotLayer,
        };
      }

      $req.post("athena/box/changeBoxLocation", params).then(res => {
        if (res.code === 0) {
          this.$success(this.$t("lang.common.success"));
          this.$emit("updateTableList");
          this.close();
        }
      });
    },

    getBoxTaskList() {
      $req
        .get("/athena/box/listBoxTask", {
          boxCode: this.formData.boxCode,
        })
        .then(res => {
          if (res.code === 0) {
            this.boxTaskList = res.data;
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.gp-form-item) {
  width: 22%;
}

:deep(.gp-form-item__label) {
  padding-bottom: 0;
  font-size: 13px;
  font-weight: 800;
}
</style>
