<template>
  <gp-popover popper-class="params-detail-popper" placement="right" width="400" trigger="hover">
    <div class="detailBox">
      <div class="contentBox">
        <div class="contentBox-title">{{ $t("lang.venus.web.common.param") }}</div>
        <div class="contentBox-content">{{ currentRow.code }}</div>
      </div>
      <div class="contentBox">
        <div class="contentBox-title">{{ $t("lang.rms.fed.describe") }}</div>
        <div class="contentBox-content">{{ $t(currentRow.descrI18nCode) }}</div>
      </div>
      <div class="contentBox">
        <div class="contentBox-title">{{ $t("lang.rms.fed.parameterValues") }}</div>
        <div class="contentBox-content">{{ currentRow.value }}</div>
      </div>
      <div class="contentBox">
        <div class="contentBox-title">{{ $t("lang.rms.fed.effectiveImmediately") }}</div>
        <div class="contentBox-content">
          {{ currentRow.immediate == 1 ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
        </div>
      </div>
    </div>
    <img slot="reference" src="../../../../imgs/parameterConfig/question.png" class="questionImg" />
  </gp-popover>
</template>

<script>
export default {
  name: "DetailPopover",
  props: {
    currentRow: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {};
  },

  mounted() {},

  methods: {},
};
</script>
<style>
.params-detail-popper {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}
</style>
<style lang="less" scoped>
.contentBox-title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #999ea5;
  width: 82px;
}
.contentBox-content {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #323334;
  width: 100%;
}
.contentBox {
  display: flex;
}
.questionImg {
  width: 17px;

  &:hover {
    cursor: pointer;
  }
}
</style>
