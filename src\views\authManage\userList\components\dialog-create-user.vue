<template>
  <gp-dialog
    :title="$t('lang.rms.fed.add')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @closed="close"
    width="380px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig" />
    <span slot="footer">
      <gp-button @click="close">{{ $t("lang.rms.fed.cancel") }}</gp-button>
      <gp-button type="primary" :loading="saveLoading" @click="onSubmit">{{ $t("lang.rms.fed.save") }}</gp-button>
    </span>
  </gp-dialog>
</template>
<script>
import md5 from "js-md5";
export default {
  data() {
    return {
      passwordEyeShow: false,
      saveLoading: false,

      formConfig: {
        attrs: {
          labelWidth: "100px",
          labelPosition: "right",
        },
        configs: {
          userName: {
            label: "lang.rms.fed.inputUserName",
            default: "",
            tag: "input",
            required: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
          },
          realName: {
            label: "lang.rms.fed.inputFullName",
            default: "",
            tag: "input",
            required: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
          },
          phone: {
            label: "lang.rms.fed.inputTelephone",
            default: "",
            tag: "input",
            autocomplete: "off",
          },
          password: {
            label: "lang.rms.fed.password",
            default: "",
            tag: "input",
            "show-password": true,
            autocomplete: "off",
            required: true,
            rules: [
              // 密码8-20位、数字、字母、特殊符号组成
              { required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" },
              {
                pattern:
                  /^(?=.*?[a-zA-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><\[\]}{\-=+_\\|》《。，、？’‘“”~ ]).{8,20}$/,
                message: this.$t("lang.auth.PwdMgrAPI.item0002"),
                trigger: "blur",
              },
            ],
          },
          roleIds: {
            label: "lang.rms.fed.role",
            tag: "select",
            default: "",
            required: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
            options: [],
          },
        },
      },

      dialogVisible: false,
    };
  },
  methods: {
    open() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.getRoleList();
        this.$refs.refForm.reset();
      });
    },
    close() {
      this.dialogVisible = false;
    },

    onSubmit() {
      this.$refs.refForm.validate().then(data => {
        const params = {
          user: {
            userName: data.userName,
            password: md5(data.password + data.userName),
            realName: data.realName,
            phone: data.phone,
          },
          roleIds: [Number(data.roleIds)],
        };
        this.saveLoading = true;
        $req
          .post("/athena/api/coreresource/auth/user/addUser/v1", params)
          .then(res => {
            if (res.code === 0) {
              this.saveLoading = false;
              const msg = $utils.Tools.transMsgLang(res?.msg || "");
              this.$success(msg);
              this.$emit("updateList");
              this.close();
            }
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    },

    getRoleList() {
      $req.get("/athena/api/coreresource/auth/role/pageQuery/v1").then(res => {
        if (res.code !== 0) return;
        const list = res?.data?.recordList || [];
        this.formConfig.configs.roleIds.options = list.map(item => ({ value: item.roleId, label: item.name }));
      });
    },
  },
};
</script>

<style lang="less" scope></style>
