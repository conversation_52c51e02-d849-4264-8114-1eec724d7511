/* ! <AUTHOR> at 2022/07/11 */
import Cookies from "js-cookie";
import WSocket from "./websocket/websocket";

class WSWorker implements MWorker.Main {
  private wSocket: WSocket;
  private clientFor3D: boolean = false;
  floorIds: Array<floorId> = [];
  cellCodes: Array<code> = [];
  rackCodes: Array<code> = [];
  shelfCodes: Array<code> = [];
  chargerIds: Array<code> = [];
  stationIds: Array<code> = [];
  robotsNeedCoverage: boolean = false;
  robotsNeedPath: boolean = false;
  robotIds: Array<code> = [];

  constructor(wsUrl: string = "") {
    wsUrl = wsUrl || this._getWsUrl();
    if (!wsUrl) throw new Error("wsUrl::" + wsUrl);
    this.wSocket = new WSocket(wsUrl);
  }

  init(): void {
    this.wSocket.init();
  }

  reqFloors(floorIds: Array<floorId> = []): void {
    const { info } = console;
    info("[websocket] >>>>> floorIds::", floorIds);
    this.floorIds = floorIds;

    const params: MWorker.wsParams = {
      msgType: "MapInitRequestMsg",
      body: {
        floorIds,
        clientFor3D: this.clientFor3D,
      },
    };
    this.wSocket.reqInit(params);
  }

  reqUpdate() {
    const params: MWorker.wsParams = {
      msgType: "MapUpdateRequestMsg",
      body: {
        floorIds: this.floorIds,
        clientFor3D: this.clientFor3D,
        robotIds: this.robotIds,
        rackCodes: this.rackCodes,
        shelfCodes: this.shelfCodes,
        cellCodes: this.cellCodes,
        chargerIds: this.chargerIds,
        stationIds: this.stationIds,
        robotsNeedCoverage: this.robotsNeedCoverage,
        robotsNeedPath: this.robotsNeedPath,
      },
    };
    this.wSocket.reqUpdate(params);
  }

  reqSocket(msgType: MWorker.reqMsgType, cmd: any): Promise<any> {
    switch (cmd?.instruction) {
      case "SHELF_HEAT":
        cmd.floorIds = this.floorIds;
        break;
    }
    const params: MWorker.wsParams = {
      msgType,
      body: cmd,
    };
    return this.wSocket.reqSocket(params);
  }

  reqQuery(params: { layer: string; code: code }): void {
    const { layer, code } = params;

    this.cellCodes = [];
    this.shelfCodes = [];
    this.rackCodes = [];
    this.chargerIds = [];
    this.stationIds = [];
    this.robotIds = [];
    switch (layer) {
      case "cell":
        this.cellCodes = [code];
      case "shelf":
      case "xShelf":
      case "poppick":
        this.shelfCodes = [code];
        break;
      case "rack":
        this.rackCodes = [code];
        break;
      case "charger":
        this.chargerIds = [code];
        break;
      case "station":
        this.stationIds = [code];
        break;
      case "robot":
        this.robotIds = [code];
        break;
      case "robotPath":
        this.robotsNeedPath = code === 1 ? true : false; // 1: true, 0: false 需求by:王林林
        return; //后面就不要执行 reqSingleQuery了
      case "robotOccupy":
        this.robotsNeedCoverage = code === 1 ? true : false; // 1: true, 0: false 需求by:王林林
        return; //后面就不要执行 reqSingleQuery了
    }
    this.wSocket.reqSingleQuery(params);
  }

  stopQuery() {
    this.cellCodes = [];
    this.shelfCodes = [];
    this.rackCodes = [];
    this.chargerIds = [];
    this.stationIds = [];
    this.robotIds = [];
    this.wSocket.stopQuery();
  }

  getQueryData(params: { layer: string; code: code }): any {
    switch (params?.layer) {
      case "cell":
      case "rack":
      case "robot":
      case "shelf":
      case "xShelf":
      case "device":
      case "xDevice":
        return this.wSocket.socketMsg.getSingleQuery(params);
      default:
        return null;
    }
  }

  getMultiQueryData(params: { layer: string; codes: Array<code> }): any {
    // TODO 获取批量的当时的数据
    switch (params?.layer) {
      case "cell":
        return this.wSocket.socketMsg.getMultiQuery(params);

      default:
        return null;
    }
  }

  onCallBack(cb: MWorker.wsCallback) {
    this.wSocket.onCallBack(cb);
  }

  destroy(): void {
    if (this.wSocket) {
      this.wSocket.destroy();
      this.wSocket = null;
    }

    this.floorIds = [];
    this.cellCodes = [];
    this.rackCodes = [];
    this.shelfCodes = [];
    this.chargerIds = [];
    this.robotIds = [];
  }

  private _getWsUrl() {
    let protocol = window.location.protocol === "http:" ? "ws" : "wss";
    let hostname = window.location.host;
    if (!__rms_env_conf.isPro) {
      hostname = new URL(__rms_env_conf.API_URL).hostname;
    }
    const RMSPermission = localStorage.getItem("Geek_RMSPermission");
    let RMSToken = Cookies.get("rmsToken");
    if (!RMSToken) {
      RMSToken = localStorage.getItem("Geek_RMSToken");
    }
    const token = RMSPermission === "true" ? `?token=${RMSToken}` : "";

    return `${protocol}://${hostname}/athena-monitor${token}`;
  }
}

export default WSWorker;
