<template>
  <div v-if="logs.length > 0" class="geek-error-log">
    <gp-avatar shape="square" size="small" effect="dark" @click.native="logVisible = true">
      <gp-badge is-dot class="item" type="danger" :border="false">
        <gp-icon><gp-icon-bell /></gp-icon
      ></gp-badge>
    </gp-avatar>

    <gp-dialog :visible.sync="logVisible" title="Error Log" width="80%" :append-to-body="true">
      <gp-table :data="logs" border class="geek-header-error-table">
        <gp-table-column label="Message">
          <template slot-scope="scope">
            <p>
              <strong>Msg:</strong>
              <gp-tag type="danger">{{ scope.row.err }}</gp-tag>
            </p>
            <p>
              <strong>Info:</strong>
              <gp-tag type="warning"> {{ scope.row.tag }} error in {{ scope.row.info }} </gp-tag>
            </p>
            <p>
              <strong>Url:</strong>
              <gp-tag type="success">{{ scope.row.url }}</gp-tag>
            </p>
          </template>
        </gp-table-column>
        <gp-table-column label="Stack">
          <template slot-scope="scope">
            <div>{{ scope.row.stack }}</div>
          </template>
        </gp-table-column>
      </gp-table>
    </gp-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "GeekErrorLog",
  data() {
    return {
      logVisible: false,
    };
  },
  computed: {
    ...mapState(["logs"]),
  },
};
</script>

<style lang="less" scoped>
.geek-error-log {
  button {
    padding: 15px;
    background-image: url(~@imgs/layout/icon-debugger.png);
    background-size: 14px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
  }
}

.geek-header-error-table {
  line-height: 23px;

  th,
  td {
    font-size: 12px;
    color: #606266;
  }

  th {
    .cell {
      font-weight: 600;
      color: #909399;
    }
  }

  td {
    p {
      margin: 16px 0;
      .g-flex();
      justify-content: flex-start;

      > strong {
        flex: 0 0 40px;
        font-size: 16px;
        color: #333;
        font-weight: bold;
      }

      .gp-tag {
        height: auto;
        margin-left: 8px;
        white-space: normal;
      }
    }

    div {
      line-height: 22px;
      white-space: normal;
    }
  }
}
</style>
