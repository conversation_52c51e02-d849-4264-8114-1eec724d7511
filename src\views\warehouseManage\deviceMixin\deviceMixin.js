export default {
  data() {
    return {

    };
  },
  watch: {
    activeNavId(id) {
      this.compColumn(id)
    }
  },
  mounted() {
    this.compColumn(this.activeNavId)
  },
  methods: {
    //格式化table数据
    formatTable(data = []) {
      const formatData = data.map((item,index) => {
        let obj = {}
        for(let key in item){
          obj[key] = item[key] ? item[key] : '-'
        }
        return obj
      })
      return formatData
    },
    //计算列表展示的数据
    compColumn(activeNavId){
      const f = this.navList.filter(item => item.id === activeNavId)
      const columns = f[0].columns
      this.tableConfig.columns = columns
      // if(activeNavId === 'function'){
      //   this.$set(this.tableConfig.attrs,'row-key','functionId')
      //   // this.tableConfig.attrs["row-key"] = "functionId"
      // }else{
      //   this.$set(this.tableConfig.attrs,'row-key','statusId')
      //   // this.tableConfig.attrs["row-key"] = "statusId"
      // }
      // this.refresh = Date.now()
    },
    tabsNavChange(id) {
      this.activeNavId = id
    },
  }
}
