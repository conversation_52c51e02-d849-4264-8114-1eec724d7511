import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 工作站已完成任务量分布
 */
export default class StationReceiveBySplitBar extends Chart {
  /**
   * 初始化图表 - 工作站已完成任务量分布
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "工作站已完成任务量分布";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
        valType: 'yyyy-MM-dd',
        option: {}
      }
    ]
  }

  async request(params = {}) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/split/count/', {
      date: $utils.Tools.formatDate(params?.date || new Date(), "yyyy-MM-dd"),
      cycle: "60",
      showReceive: true,
      showToStationDone: true, 
      showOrgData: false
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) { 
    const totalDataItem = data.split_stationDoneGroupByStation || {};
    const entriesDataList = Object.entries((totalDataItem));

    entriesDataList.sort((itemA, itemB) => {
      const [itemAStartTime] = itemA[0].split('~');
      const [itemBStartTime] = itemB[0].split('~');
      const exec = /\d{0,2}-(\d{0,2}:\d{0,2}:\d{0,2})/;
      const timeA = +new Date(`2023-01-01 ${exec.exec(itemAStartTime)[1]}`);
      const timeB = +new Date(`2023-01-01 ${exec.exec(itemBStartTime)[1]}`);
      return timeA - timeB;
    });
    
    const xAxisData = entriesDataList.map(item => item[0]);
    const seriesDataList = entriesDataList.map(item => item[1]);

    const serverNamesSet = new Set();

    seriesDataList.forEach(item => {
      Object.keys(item).forEach(itemKey => serverNamesSet.add(itemKey));
    })
  
    serverNamesSet.delete('all_info');
    const serverNameList = [...serverNamesSet];
    serverNameList.sort((a, b) => a - b);
  
    const seriesData = serverNameList.map(serverName => {
      return {
        name: serverName,
        type: 'bar',
        stack: 'stack',
        data: xAxisData.map(x => totalDataItem[x][serverName] || 0)
      }
    });
  
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      grid: { left: 30, right: 30, bottom: 30 },
      tooltip: { show: true, trigger: 'axis' },
      series: seriesData
    })
  }
}