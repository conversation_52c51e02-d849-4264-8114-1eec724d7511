<template>
  <div class="detailDialog">
    <gp-dialog
      v-for="(item, index) in itemList"
      :key="index"
      :title="item.title + '详情'"
      :visible.sync="dialogVisible"
      width="45%"
      @close="handleClose(1)"
    >
      <div class="detailBox">
        <div class="contentBox">
          <div class="contentBox-title">参数</div>
          <div class="contentBox-content">map.resolvermap.resolermap.resolermap.resolver</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">描述</div>
          <div class="contentBox-content">文字过多折行展示文字过多折行展示</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">参数值</div>
          <div class="contentBox-content">map.resolvermap.</div>
        </div>
        <div class="contentBox">
          <div class="contentBox-title">立即生效</div>
          <div class="contentBox-content">否</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer"></span>
    </gp-dialog>
  </div>
</template>

<script>
export default {
  name: "DialogDetail",
  props: {
    dialogVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    itemList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      handleClose(n) {
        this.$emit("close", n);
      },
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="less" scoped>
.detailDialog :deep(.gp-dialog) {
  border-radius: 10px;
}
:deep(.gp-dialog__title) {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: #323334;
}
:deep(.gp-dialog__header) {
  padding: 13px 20px 13px;
}
.contentBox-title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #999ea5;
  width: 82px;
}
.contentBox-content {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 36px;
  color: #323334;
  width: 100%;
}
.contentBox {
  display: flex;
}
</style>
