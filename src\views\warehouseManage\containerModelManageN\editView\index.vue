<template>
  <gp-dialog
    class="contentModelManageEditView"
    top="10vh"
    :title="title"
    :visible="visible"
    width="90%"
    :footer="false"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="model">
      <gp-empty
        class="empty"
        v-if="!fromData.extendJson.subCategory"
        :description="$t('lang.fed.rms.pleaseSelectContainerTypeFirst')"
      ></gp-empty>
      <canvas class="viewport-canvas" id="canvas" ref="canvasRef"></canvas>
    </div>
    <div class="from">
      <div class="form-content">
        <gp-form :model="fromData" ref="editForm" class="editForm" label-width="100px">
          <gp-form-item :label="$t('lang.rms.fed.containerCategory')" prop="extendJson.subCategory" required>
            <gp-select
              v-model="fromData.extendJson.subCategory"
              :disabled="isEdit"
              :placeholder="$t('lang.rms.fed.choose')"
              class="w200"
            >
              <gp-option
                v-for="item in containerModelCategoryDict"
                :key="item.value"
                :label="$t(item.label)"
                :value="item.value"
              />
            </gp-select>
          </gp-form-item>
        </gp-form>

        <component
          ref="formComponentRef"
          :is="componentByTypeMap[fromData.extendJson.subCategory]"
          :subCategory="fromData.extendJson.subCategory"
          v-if="fromData.extendJson.subCategory"
          @updateValue="handleUpdateValue"
        />
      </div>

      <div class="dialog-footer">
        <gp-button @click="handleClose">{{ $t("lang.common.cancel") }}</gp-button>
        <gp-popconfirm
          v-if="isUsed"
          @confirm="handleSave"
          placement="top"
          :title="$t('lang.rms.fed.modelCurrentlyUseMsg01')"
        >
          <gp-button slot="reference" type="primary" v-loading="saveLoad">{{ $t("lang.rms.fed.confirm") }}</gp-button>
        </gp-popconfirm>
        <gp-button v-else type="primary" @click="handleSave" v-loading="saveLoad">{{
          $t("lang.rms.fed.confirm")
        }}</gp-button>
      </div>
    </div>
  </gp-dialog>
</template>

<script>
import { mapState } from "vuex";
import HeterotypeShelfForm from "./Components/heterotypeShelfForm.vue";
import PopPickShelfForm from "./Components/PopPickShelfForm.vue";
import shelfHolderForm from "./Components/shelfHolderForm.vue";
import palletRackForm from "./Components/palletRackForm.vue";
import palletForm from "./Components/palletForm.vue";
import shelfForm from "./Components/shelfForm.vue";
import boxForm from "./Components/boxForm.vue";
import Viewport from "@/libs/viewport3d/viewport-3d";

import { addModl } from "../api/index";
import { getComponentByTypeMap, getVpConfig } from "./vpConfig";
export default {
  props: {
    visible: Boolean,
  },
  name: "",
  components: {
    HeterotypeShelfForm,
    PopPickShelfForm,
    shelfHolderForm,
    palletRackForm,
    palletForm,
    shelfForm,
    boxForm,
  },
  data() {
    return {
      fromData: {
        extendJson: {
          subCategory: "",
        },
      },
      saveLoad: false,
      vpInstance: null,
      // 根据容器类型，动态加载不同的表单组件
      componentByTypeMap: {
        SPECIAL_PALLET: "HeterotypeShelfForm",
        PPP_SHELF: "PopPickShelfForm",
        SHELF_HOLDER: "shelfHolderForm",
        PALLET_RACK: "palletRackForm",
        PALLET: "palletForm",
        SHELF: "shelfForm",
        BOX: "boxForm",
        X_PALLET: "palletForm",
        X_HOLDER: "shelfHolderForm",
      },
    };
  },
  watch: {
    "fromData.extendJson.subCategory"() {
      if (this.vpInstance) {
        this.vpInstance.destroy();
      }
      const canvasDom = this.$refs.canvasRef;
      if (!canvasDom) return;
      const meshType = getComponentByTypeMap(this.fromData);
      this.vpInstance = new Viewport();
      this.vpInstance.init({ dom: canvasDom });
      const config = getVpConfig(meshType, { ...this.fromData });
      this.vpInstance.initModel({ meshType, config: config });
      // this.vpInstance.resize();
      // this.vpInstance.setCameraCenter()
    },
  },

  destroyed() {
    this.vpInstance && this.vpInstance.destroy();
  },
  computed: {
    ...mapState("containerModal", ["editData", "containerModelCategoryDict"]),
    title() {
      return this.editData.id
        ? this.$t("lang.rms.web.container.editContainerModel")
        : this.$t("lang.rms.web.container.addContainerType");
    },
    isEdit() {
      return !!this.editData.id;
    },
    isUsed() {
      return !!this.editData?.used;
    },
    modelCategory() {
      const { subCategory } = this.fromData?.extendJson || {};
      return (
        this.containerModelCategoryDict.find(item => {
          return item.value === subCategory;
        })?.category || ""
      );
    },
  },
  created() {
    if (this.editData.id) {
      const { modelCategory, extendJson } = this.editData;
      this.fromData.extendJson.subCategory = extendJson.subCategory || modelCategory;
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.fromData.extendJson.subCategory) {
        const meshType = getComponentByTypeMap(this.fromData);
        this.vpInstance = new Viewport();
        this.vpInstance.init({ dom: this.$refs.canvasRef });
        this.vpInstance.resize();
        console.log(getVpConfig(meshType, { ...this.fromData }));
        this.vpInstance.initModel({ meshType, config: getVpConfig(meshType, { ...this.fromData, ...this.editData }) });
        // this.vpInstance.resize();
        // this.vpInstance.setCameraCenter()
      }
    });
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
    },

    handleUpdateValue(data) {
      if (this.vpInstance) {
        const meshType = getComponentByTypeMap(this.fromData);
        console.log("vpInstance > ", JSON.stringify(getVpConfig(meshType, data)));
        this.vpInstance.updateModel({ meshType, config: getVpConfig(meshType, data) || {} });
        // this.vpInstance.resize()
        // this.vpInstance.setCameraCenter()
      }
    },

    async handleSave() {
      this.saveLoad = true;
      // 接口
      this.getFormData().then(async saveParams => {
        if (!saveParams) {
          return;
        }
        // 接口
        const { code } = await addModl(saveParams);
        if (code === 0) {
          this.$message.success(this.$t("lang.rms.fed.savingSucceeded"));
          // 更新列表
          this.$emit("updateList");
          // 保存结束
          this.handleClose();
        }
        //
      });
      this.saveLoad = false;
    },
    async getFormData() {
      const { formComponentRef } = this.$refs;
      const { fromData } = this;
      if (formComponentRef && formComponentRef.sizeTypeParamTip) {
        return false;
      }
      if (formComponentRef && formComponentRef.validateData) {
        const formComponentData = await formComponentRef.validateData();
        if (formComponentData) {
          const saveParams = formComponentData;
          saveParams.modelCategory = this.modelCategory;
          saveParams.extendJson || (saveParams.extendJson = {});
          saveParams.extendJson.subCategory = fromData.extendJson.subCategory;
          return saveParams;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.contentModelManageEditView {
  .model {
    width: 350px;
    height: 100%;
    padding: 10px;
    border-right: 1px solid #ebeef5;
    position: relative;
  }

  .form-content {
    height: 100%;
    width: 100%;
    overflow: auto;
  }

  .empty {
    height: 100%;
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  .viewport-canvas {
    width: 100%;
    height: 100%;
  }
  .from {
    flex: 1;
    padding: 10px;
    padding-bottom: 45px;
    position: relative;
    overflow: hidden;

    .editForm {
      height: 50px;
      border-bottom: 1px solid #eee;
      margin-bottom: 15px;
    }

    .dialog-footer {
      position: absolute;
      bottom: 10px;
      right: 20px;
    }
  }
}
</style>

<style lang="scss">
.contentModelManageEditView {
  .gp-dialog {
    height: 80%;
    display: flex;
    flex-direction: column;
    padding-bottom: 0;

    .gp-dialog__body {
      flex: 1;
      max-height: 100%;
      display: flex;
      flex-direction: row;
      padding: 0;
    }
  }
}
</style>
