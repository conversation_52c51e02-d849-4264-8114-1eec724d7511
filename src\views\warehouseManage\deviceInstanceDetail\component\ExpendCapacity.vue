<template>
  <geek-customize-table
    :table-config="tableConfig"
    :data="rowData"
  >
    <template #actionType="{ row }">
      <gp-select v-model="row.actionType" disabled>
        <gp-option
          v-for="(item,index) in actionTypeOps"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #plcAddress="{ row }">
      <div class="input-con">
        <span class="required">*</span>
        <gp-input
          v-model="row.plcAddress"
          clearable
          @input="plcAddressChange(row)"
        ></gp-input>
      </div>
    </template>
    <template #registerType="{ row }">
      <gp-select v-model="row.registerType">
        <gp-option
          v-for="(item,index) in registerTypeList"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #dataDealType="{ row }">
      <gp-select
        v-model="row.dataDealType"
        :disabled="disableDataDealTyp"
        @change="dataDealTypeChange(row)"
      >
        <gp-option
          v-for="(item,index) in dataDealTypeOps(row)"
          :key="index"
          :label="$t(item.label)"
          :value="item.value"
        >{{$t(item.label)}}
        </gp-option>
      </gp-select>
    </template>
    <template #plcAddressValue="{row}">
      <div class="input-con">
        <span class="required">*</span>
        <gp-input
          v-if="row.dataDealType === 0"
          v-model="row.plcAddressValue"
          clearable
          @input="plcAddressValueChange(row)"
          maxlength="11"
        >
        </gp-input>
        <gp-select v-else v-model="row.inputParam">
          <gp-option
            v-for="(item,index) in inputParamOps"
            :key="index"
            :label="$t(item.label)"
            :value="item.value"
          >{{$t(item.label)}}
          </gp-option>
        </gp-select>
      </div>
    </template>
    <template #operation="{row}">
      <gp-button type="text" @click="copyItem(row)">{{$t('lang.rms.web.map.version.copy')}}</gp-button>
      <gp-button type="text" @click="deleteItem(row)">{{$t('lang.rms.fed.buttonDelete')}}</gp-button>
    </template>

  </geek-customize-table>
</template>

<script>
export default {
  name: "ExpendCapacity",
  props: {
    rowData: {
      type: Array,
      require: true,
    },
    id: {
      type: Number,
      require: true,
    },
    inputParam: {
      type: Array,
      require: true,
    }
  },
  computed:{
    inputParamOps() {
      const ops = this.inputParam.map(item => {
        return {value:item,label:item}
      })
      return ops
    },
    disableDataDealTyp() {
      return !this.inputParam.length
    },
    dataDealTypeOps() {
      return (row) => {
        let ops = [{label:'lang.rms.web.device.writeFixedValue',value:0}]
        const {plcAddress} = row
        const addressArr = plcAddress.split(',')
        if(addressArr.length > 1){
          ops.push({label:'lang.rms.web.device.writeParamValue',value:2})
          // row.dataDealType = 2
        }else{
          ops.push({label:'lang.rms.web.device.writeParamValue',value:1})
          // row.dataDealType = 1
        }
        return ops
      }
    }
  },
  data() {
    return {
      actionTypeOps:[
        {label:'lang.rms.web.device.read',value:0},
        {label:'lang.rms.web.device.write',value:10},
      ],
      registerTypeList:[
        {label:'lang.venus.common.dict.readCoil',value:0},
        {label:'lang.venus.common.dict.readAndWriteCoil',value:1},
        {label:'lang.venus.common.dict.readRegister',value:3},
        {label:'lang.venus.common.dict.readAndWriteRegister',value:4},
      ],
      // dataDealTypeOps:[
      //   {label:'写入固定值',value:0},
      //   {label:'写入参数值',value:1},
      //   // {label:'写入参数值（地址是多个）',value:2},
      // ],
      tableConfig: {
        // attrs: {  "row-key":"plcId",},
        columns: [
          {
            label: "lang.rms.web.device.plcOperationType",
            prop: "actionType",
            width: "140",
            slotName: "actionType"
          },
          {
            label: "lang.rms.web.device.plcAddress",
            prop: "plcAddress",
            width: "314",
            slotName: "plcAddress"
          },
          {
            label: "lang.venus.web.common.registerType",
            prop: "registerType",
            width: "140",
            slotName: "registerType"
          },
          {
            label: "lang.rms.web.device.writeType",
            prop: "dataDealType",
            width: "140",
            slotName: "dataDealType"
          },
          {
            label: "lang.rms.web.device.plcValue",
            prop: "plcAddressValue",
            width: "140",
            slotName: "plcAddressValue"
          },
          {
            label: "lang.rms.fed.listOperation",
            prop: "operation",
            slotName: "operation",
            width: "140",
            className: "operation-btn",
            "render-header":this.addBtn,
          },
          { label: "", prop: "empty", },
        ],
      },
    }
  },
  methods:{
    dataDealTypeChange(row) {
      const {dataDealType} = row
      //如果是写入固定值
      if(dataDealType === 0){
        row.inputParam = ''
      }else{
        row.plcAddressValue = ''
      }
    },
    plcAddressChange(row) {
      row.plcAddress = row.plcAddress.replace(/[^0-9,]/g, '');
      const {plcAddress,inputParam} = row
      const plcAddressArr = plcAddress.split(',')
      //如果存在输入参数
      if(inputParam){
        if(plcAddressArr.length > 1){
          row.dataDealType = 2
        }else{
          row.dataDealType = 1
        }
      }else{
        row.dataDealType = 0
      }
    },
    plcAddressValueChange(row) {
      row.plcAddressValue = row.plcAddressValue.replace(/[^0-9]/g, '');
    },
    addBtn(h) {
      return h('gp-button',{
        props: {
          type: 'text'
        },
        domProps: {
          innerHTML: this.$t('lang.venus.web.common.add')
        },
        on: {
          click: this.addItem
        },
      })
    },
    addItem() {
      const { deviceCode } = this.$route.query;
      const item =   {
        deviceCode,
        actionType:10,
        plcAddress:'',
        registerType:'',
        dataDealType:this.disableDataDealTyp ? 0 : '',
        plcAddressValue:''
      }
      this.$emit('add',{id:this.id,data:item})
    },
    deleteItem(row) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      ).then(() => {
        const delId = row.plcId
        const delIndex = this.rowData.findIndex(item => item.plcId === delId)
        console.log(row,delIndex)
        this.$emit('delete',{id:this.id,delIndex})
      }).catch((err) => {
        console.log(err)
      })
    },
    copyItem(row) {
      const {plcAddress,dataDealType,...others} = row
      const copyDataDealType = dataDealType === 2 ? 1 : dataDealType
      this.$emit('copy',{id:this.id,data:{...others,plcAddress:'',dataDealType:copyDataDealType}})
    },
  }
};
</script>

<style scoped lang="scss">
.input-con{
  display: flex;
  .required{
    color: red;
    line-height: 32px;
    margin-right: 6px;
  }
}
</style>
