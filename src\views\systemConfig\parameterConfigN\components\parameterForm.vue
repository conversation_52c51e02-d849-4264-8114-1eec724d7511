<template>
  <div class="parameterFromBox">
    <div v-for="item in newList" :key="item.id">
      <div
        v-for="(itemb, index) in item"
        :key="itemb.id"
        :class="
          itemb.code == 'charging.safePowerPercent' ||
          itemb.code == 'charging.chargingTime' ||
          itemb.code == 'charging.task.allowedPowerToBePreempted' ||
          itemb.code == 'charging.task.triggerPreemptionPower' ||
          itemb.code == 'charging.task.electricityPreemptionDifference' ||
          itemb.code == 'charging.idleCharging.lowPowerPercent' ||
          itemb.code == 'charging.idleCharging.idleTime' ||
          itemb.code == 'charging.sorting.flexible.charging.task.allowedPowerToBePreempted' || 
          itemb.code == 'charging.sorting.flexible.charging.task.triggerPreemptionPower' ||
          itemb.code == 'charging.sorting.flexible.charging.task.electricityPreemptionDifference' ||
          itemb.code ==  'charging.fast.trigger.seize.power' ||
          itemb.code ==  'charging.fast.seized.power' || 
          itemb.code ==  'charging.fast.seized.powerDifference'
            ? 'inline'
            : ''
        "
      >
        <template v-if="path == itemb.path && itemb.isShow">
          <div v-if="itemb.sectionLabel && index === 0" class="third-menu">
            {{ $t(itemb.sectionLabel) }}
          </div>
          <div v-if="itemb.code == 'charging.lowPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willSendTaskRobotButMaybeFail") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'robot.task.unworkableWhenLowPower'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.onlyAcceptChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.upperPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerHigherThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willStopChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.safePowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.chargingTaskCanEarlyEndAndMeetSafytyPower") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
          </div>
          <div v-else-if="itemb.code == 'charging.chargingTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.chargingDurationGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.task.waitTimeMinutes'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.assigneChargingTaskToRobotMoreThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <span class="form-text">{{ $t("lang.rms.config.page.reassignNewChargingPileToRobot") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.idleCharging.lowPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotFreeAndPowerLessThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.idleCharging.idleTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotFreeTimeGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willTargetChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.task.allowedPowerToBePreempted'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.powerLessThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.task.triggerPreemptionPower'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlPowerGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.robotChargingPile") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.task.electricityPreemptionDifference'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.formerPowerLessThanLatterPower") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlSuccessfully") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'shelf.dynamicAdjustment.globalConcurrency'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.generateMaxNumberTask") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.rms.web.piece") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'shelf.dynamicAdjustment.idleRobotCount'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.dynamicAdjustRobotFreeSizeGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.rms.config.page.robotSize") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'shelf.dynamicAdjustment.idleRobotRate'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.dynamicAdjustRobotFreeScaleGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <!-- RMSDEV-31929 需求 start -->
          <div v-else-if="itemb.code == 'charging.sorting.flexible.statisticalDurationTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.sorting.flexible.statisticalDurationTime.label") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.configs.charging.sorting.flexible.minute") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.dropTaskRobotRatio'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.sorting.flexible.dropTaskRobotRatio.label") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.closeDurationTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.sorting.flexible.closeDurationTime.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.configs.charging.sorting.flexible.closeDurationTime.end") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.charging.lowPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willSendTaskRobotButMaybeFail") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.charging.upperPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerHigherThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willStopChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.robot.task.unworkableWhenLowPower'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.onlyAcceptChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.charging.task.allowedPowerToBePreempted'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.powerLessThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.charging.task.triggerPreemptionPower'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlPowerGreaterThan") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.robotChargingPile") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.sorting.flexible.charging.task.electricityPreemptionDifference'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.formerPowerLessThanLatterPower") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlSuccessfully") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <!-- RMSDEV-31929 需求 end -->
          <!-- RMSDEV-32626 需求 start -->
          <div v-else-if="itemb.code == 'charging.fast.charging.startThreshold'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.charging.startThreshold.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.configs.charging.fast.charging.startThreshold.end") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.fast.charging.recoveryThreshold'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.charging.recoveryThreshold.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.configs.charging.fast.charging.recoveryThreshold.end") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.fast.loop.minute'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.loop.minute.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.configs.charging.fast.loop.minute.end") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.fast.trigger.seize.power'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.trigger.seize.power.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.fast.seized.power'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.seized.power.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.configs.charging.fast.seized.power.end") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.fast.seized.powerDifference'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.configs.charging.fast.seized.powerDifference.start") }}</span>
            <gp-input
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <!-- RMSDEV-32626 需求 end -->
          <template v-else>
            <!-- 下拉框 -->
            <div v-if="['multi-select', 'select'].includes(itemb.type)" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  class="d"
                />
                <span class="form-title">{{ $t(itemb.label) }} :</span>
                <gp-tooltip
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <gp-select
                    v-model="itemb.options.defVal"
                    clearable
                    :multiple="itemb.widgetType === 'multi-select'"
                    :collapse-tags="itemb.widgetType === 'multi-select'"
                    :placeholder="$t('lang.rms.fed.pleaseChoose')"
                    @change="change(itemb)"
                  >
                    <gp-option
                      v-for="itemc in itemb.options.selectList"
                      :key="itemb.code == 'shelf.dynamicAdjustment.week.days' ? itemc.value : itemc"
                      :label="itemb.code == 'shelf.dynamicAdjustment.week.days' ? $t(itemc.label) : itemc"
                      :value="itemb.code == 'shelf.dynamicAdjustment.week.days' ? itemc.value : itemc"
                    >
                    </gp-option>
                  </gp-select>
                </gp-tooltip>
                <gp-select
                  v-else
                  v-model="itemb.options.defVal"
                  clearable
                  :multiple="itemb.widgetType === 'multi-select'"
                  :collapse-tags="itemb.widgetType === 'multi-select'"
                  :placeholder="$t('lang.rms.fed.pleaseChoose')"
                  :itemb="itemb"
                  @change="change(itemb)"
                >
                  <gp-option
                    v-for="itemc in itemb.options.selectList"
                    :key="itemb.code == 'shelf.dynamicAdjustment.week.days' ? itemc.value : itemc"
                    :label="itemb.code == 'shelf.dynamicAdjustment.week.days' ? $t(itemc.label) : itemc"
                    :value="itemb.code == 'shelf.dynamicAdjustment.week.days' ? itemc.value : itemc"
                  >
                  </gp-option>
                </gp-select>
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 输入框 -->
            <div v-if="itemb.type == 'text' || itemb.type == 'input'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  class="d"
                />
                <span class="form-title">{{ $t(itemb.label) }} :</span>
                <gp-tooltip
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <gp-input
                    v-if="itemb.valueType === 'java.lang.Integer'"
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                  <gp-input
                    v-else
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                </gp-tooltip>
                <template v-else>
                  <gp-input
                    v-if="itemb.valueType === 'java.lang.Integer'"
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                  <gp-input
                    v-else
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                </template>
                <detailPopover :current-row="itemb" />
                <span v-show="itemb.errorTipShow" class="error-tip">
                  {{ itemb.errorTipText }}
                </span>
              </span>
            </div>
            <!-- 文本域 -->
            <div v-if="itemb.type == 'textarea'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  class="d"
                />
                <span class="form-title">{{ $t(itemb.label) }} :</span>
                <gp-tooltip
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <gp-input
                    v-model="itemb.options.defVal"
                    type="textarea"
                    :rows="1"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    class="textarea"
                    @change="change(itemb)"
                  />
                </gp-tooltip>
                <gp-input
                  v-else
                  v-model="itemb.options.defVal"
                  type="textarea"
                  :rows="1"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                  class="textarea"
                  @change="change(itemb)"
                />
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 开关 -->
            <div v-if="itemb.type == 'checkbox'" class="form-switch">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  class="d"
                />
                <span class="form-title">{{ $t(itemb.label) }} :</span>
                <gp-tooltip
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <gp-switch
                    v-model="itemb.options.defVal"
                    active-color="#409EFF;"
                    :active-text="$t('lang.rms.fed.on')"
                    :inactive-text="$t('lang.rms.fed.off')"
                    active-value="true"
                    inactive-value="false"
                    @change="change(itemb)"
                  >
                  </gp-switch>
                </gp-tooltip>
                <gp-switch
                  v-else
                  v-model="itemb.options.defVal"
                  active-color="#409EFF;"
                  :active-text="$t('lang.rms.fed.on')"
                  :inactive-text="$t('lang.rms.fed.off')"
                  active-value="true"
                  inactive-value="false"
                  @change="change(itemb)"
                >
                </gp-switch>
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 单独时间选择器 -->
            <div v-if="itemb.type == 'timeslot'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="
                    itemb.immediate == false &&
                    itemb.value !== itemb.currentUpdateValue &&
                    itemb.currentUpdateValue !== null
                  "
                  class="d"
                />
                <span class="form-title">{{ $t(itemb.label) }} :</span>
                <gp-time-picker
                  v-model="itemb.options.defVal"
                  is-range
                  range-separator="—"
                  value-format="HH:mm:ss"
                  @change="change(itemb)"
                />
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 可添加时间选择器 -->
            <div v-if="itemb.type == 'timeslots'" class="form-input">
              <div v-if="itemb.immediate == false ? true : show" class="flex_c">
                <div>
                  <i
                    v-if="
                      itemb.immediate == false &&
                      itemb.value !== itemb.currentUpdateValue &&
                      itemb.currentUpdateValue !== null
                    "
                    class="d"
                  />
                  <span class="form-title">{{ $t(itemb.label) }} :</span>
                </div>
                <div>
                  <timePickerSlots
                    :value="itemb.options.defVal"
                    @change="data => timePickerSlotsChange(itemb, data)"
                  ></timePickerSlots>
                </div>
                <detailPopover :current-row="itemb" />
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>
    <!-- <dialogDetail v-if="dialog" :items="data" @close="close"></dialogDetail> -->
  </div>
</template>

<script>
import detailPopover from "./detailPopover";
import timePickerSlots from "./timePickerSlots";
export default {
  name: "WorkspaceJsonBasicConfig",
  components: {
    detailPopover,
    timePickerSlots,
  },
  props: {
    path: {
      type: String,
      default() {
        return "";
      },
    },
    list: {
      type: [Array, Object],
      default() {
        return [];
      },
    },
    show: {
      type: Boolean,
      default() {
        return true;
      },
    },
    // immediateShow: {
    //   type: Boolean,
    //   default() {
    //     return false;
    //   },
    // },
  },
  data() {
    return {
      newList: {},
      value: "",
      switchValue: true,
      dialog: false,
      data: {},
      input: "",
      changeData: false,
      isShow: true,
    };
  },
  watch: {
    // change: {
    //   // 一级tab默认展示
    //   handler(newval, oldval) {
    //     console.log("newval", newval);
    //     console.log("oldval", oldval);
    //   },
    //   deep: true,
    // },
    list: {
      handler(val) {
        this.newList = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // console.log(this.list, "parameterForm")
  },
  methods: {
    detail(data) {
      this.dialog = true;
      this.data = data;
    },
    close(n) {
      if (n === 1) {
        this.dialog = false;
      }
    },
    change(data) {
      // data.valueType === 'java.lang.Double' ||
      // data.valueType === 'java.lang.Integer'
      let value = data.options.defVal;
      data.errorTipShow = false;
      data.errorTipText = "";
      console.log(data);
      // debugger

      if (data.valueType === "java.lang.Integer") {
        let intReg = /^[0-9]*$/;
        if (data.options.limitMin && data.options.limitMin == -1) {
          intReg = /^(-1|([0-9]*))$/;
        }
        if (data.options.limitMin && data.options.limitMin == -5) {
          intReg = /^(-1|-2|-3|-4|-5|([0-9]*))$/;
        }
        // 这里针对 缓存位配置 jacking_heights做了单独处理, 可以输入负数
        if (data.code === "job.rsp.ctl.p40.workAdjustMarginHeight") {
          intReg = /^-?\d*$/;
        }
        console.log(intReg, intReg.test(value));
        if (!intReg.test(value)) {
          data.errorTipShow = true;
          if (data.validValueRange) {
            data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}${this.$t(
              "lang.rms.fed.configs.ingergerTip",
            )}`;
          } else {
            data.errorTipText = `${this.$t("lang.rms.containerManage.sendModelId.check")}`;
          }

          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
        if (data.options.limitMin && (Number(value) < data.options.limitMin || Number(value) > data.options.limitMax)) {
          data.errorTipShow = true;
          if (data.validValueRange) {
            data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}${this.$t(
              "lang.rms.fed.configs.ingergerTip",
            )}`;
          } else {
            data.errorTipText = `${this.$t("lang.rms.containerManage.sendModelId.check")}`;
          }
          console.log("错误开始");
          // this.changeData = true;
          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
      } else if (data.valueType === "java.lang.Double") {
        let intReg = /^[+]{0,1}(\d+)[dD]{0,1}$|^[+]{0,1}(\d+\.\d+)[dD]{0,1}$/;
        if (!intReg.test(value)) {
          data.errorTipShow = true;
          data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}${this.$t(
            "lang.rms.fed.configs.doubleTip",
          )}`;
          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
        let newVal = value;
        if (newVal.indexOf("D") !== -1 || newVal.indexOf("d") !== -1) {
          newVal = String(newVal).slice(0, -1);
        }
        if (
          data.options.limitMin &&
          (Number(newVal) < data.options.limitMin || Number(newVal) > data.options.limitMax)
        ) {
          data.errorTipShow = true;
          data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}${this.$t(
            "lang.rms.fed.configs.doubleTip",
          )}`;
          console.log("错误开始");
          // this.changeData = true;
          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
      }
      this.changeData = true;

      this.$emit("changeData", this.changeData);
      this.$emit("itemsDetail", { ...data, value });
    },
    timePickerSlotsChange(item, timeSlotsVal) {
      this.changeData = true;
      this.$emit("changeData", this.changeData);
      item.options.defVal = timeSlotsVal;
      // debugger
      this.$emit("itemsDetail", item);
    },
    // 单独时间选择
    // timePickerSlotChange(item) {
    //   this.changeData = true;
    //   this.$emit("changeData", this.changeData);
    //   item.options.defVal = timeSlotsVal;
    //   this.$emit("itemsDetail", item);
    // },
  },
};
</script>

<style lang="less" scoped>
.parameterFromBox {
  line-height: 38.6px;
  margin-top: -6px;
}
.form-title,
.form-text {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  color: #686c71;
  margin-right: 6px;
}
.form-input,
.form-switch {
  padding-bottom: 8px;
}
.form-switch :deep(.gp-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
  font-size: 12px;
}
.form-switch :deep(.gp-switch__label--right) {
  z-index: 1;
  left: -3px;
}
.form-switch :deep(.gp-switch__label--left) {
  z-index: 1;
  left: 19px;
}
.form-switch :deep(.gp-switch__label.is-active) {
  display: block;
}
.questionImg {
  width: 17px;
}
.questionImg:hover {
  cursor: pointer;
}
.form-input .gp-input, .form-input .gp-select {
  width: 200px;
  // height: 32px;
  // background: #ffffff;
  // border-radius: 6px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  // border-radius: 6px;
  margin-right: 6px;
}
/deep/.gp-date-editor {
  width: 240px;
  height: 30px;
  background: #ffffff;
  // border-radius: 6px;
}
/deep/.gp-range-separator {
  color: #999ea5;
  margin-left: -10px;
}
.textarea {
  width: 222px;
}
.ss {
  display: none;
}
.third-menu {
  font-size: 12px;
  &::before {
    content: "";
    display: inline-block;
    height: 18px;
    width: 4px;
    border-radius: 4px;
    background: #409eff;
    margin-right: 4px;
    vertical-align: text-bottom;
  }
}
.inline {
  display: inline-block;
}
.flex_c {
  display: flex;
}
.flex_w {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.error-tip {
  font-size: 14px;
  color: red;
}
</style>
