<template>
  <div>
    <gp-form ref="searchForm" :inline="true" class="demo-form-inline" label-position="left" :model="searchForm">
      <gp-form-item :label="$t('lang.rms.fed.config.robotId')">
        <gp-input v-model="searchForm.robotId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.selectDate')">
        <gp-date-picker v-model="searchForm.date" type="date" :placeholder="$t('lang.rms.fed.selectDate')">
        </gp-date-picker>
      </gp-form-item>
      <gp-form-item class="align-bottom">
        <gp-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</gp-button>
        <gp-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</gp-button>
      </gp-form-item>
    </gp-form>

    <gp-table ref="selectStations" v-loading="loading" :data="stationList" style="width: 100%">
      <gp-table-column v-for="item in cloumnList" :key="item.prop" :prop="item.prop" :label="$t(item.label)" />
    </gp-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        date: "",
        robotId: "",
      },
      loading: false,
      stationList: [],
    };
  },

  computed: {
    cloumnList() {
      return [
        // 任务编号
        { label: "任务编号", prop: "whJobId" },
        // 机器人ID
        { label: "lang.mb.robotManage.robotId", prop: "containerType" },
        // 任务来源
        { label: "lang.venus.web.common.taskSource", prop: "stageType" },
        // 任务指令
        { label: "lang.rms.fed.taskInstruction", prop: "stageStatus" },
        // 任务状态
        { label: "lang.rms.web.monitor.robot.taskState", prop: "jobStatus" },
        { label: "任务接收时间", prop: "stationId" },
        { label: "任务结束时间", prop: "jobSourceType" },
        { label: "目标工作站ID", prop: "startContainerLocation" },
        { label: "lang.rms.web.monitor.robot.robotPriority", prop: "priority" },
        { label: "业务优先级", prop: "businessSequence" },
      ];
    },
  },
  async created() {
    await this.onSubmit();
  },

  methods: {
    resetForm() {
      this.searchForm = { date: "", robotId: "" };
      this.onSubmit();
    },
    async onSubmit() {
      this.loading = true;
      const params = { ...this.searchForm };
      if (params.date) {
        params.date = $utils.Tools.formatDate(params.date, "yyyy-MM-dd");
      }
      const { data } = await $req.post("/athena/stats/query/job/select/robot", params, { intercept: false });
      // 目前是要展示所有的字段
      this.loading = false;
      this.stationList = data || [];
    },
  },
};
</script>

<style></style>
