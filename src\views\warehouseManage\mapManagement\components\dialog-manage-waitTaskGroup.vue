<template>
  <div class="map-manage-waitTaskGroup">
    <gp-dialog
      width="60%"
      :visible="visible"
      :close-on-click-modal="false"
      :footer="false"
      class="geek-dialog-form"
      @close="handleDialogClosed"
    >
      <template #title>
        <span class="f16">{{ title }}</span>
      </template>
      <!-- 趴窝 添加/编辑 表单 -->
      <div v-if="addOrEditGroup" class="map-manage-waitTaskGroup__form">
        <gp-form ref="groupform" :rules="rules" label-width="120px" :model="formData">
          <gp-form-item :label="$t('lang.rms.fed.function.waitTaskGroup')" prop="groupName">
            <gp-input v-model="formData.groupName" :disabled="editGroup" maxlength="32" />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.fed.function.groupValueType')" prop="valueType">
            <gp-select v-model="formData.valueType" class="w100p" @change="valueTypeChange">
              <gp-option
                v-for="item in valueTypeOptions"
                :key="item.value"
                :value="item.value"
                :label="$t(item.label)"
              />
            </gp-select>
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.fed.function.groupValue')" prop="value">
            <span slot="label">
              <span>
                <gp-tooltip
                  v-show="formData.valueType === 2"
                  class="item"
                  effect="dark"
                  placement="top-start"
                  :content="$t('lang.rms.fed.function.expression.tip')"
                >
                <gp-icon name="gp-icon-warning" />
                </gp-tooltip>
                <span>{{ $t("lang.rms.fed.function.groupValue") }}</span>
              </span>
            </span>
            <gp-input v-if="formData.valueType === 2" v-model="formData.value" type="text" :validate-event="true" />
            <gp-input-number v-else v-model="formData.value" controls-position="right" :min="1" :max="999" />

            <!--  -->
          </gp-form-item>

          <gp-form-item class="tc mt30">
            <gp-button class="w120" @click="handleChangePanel">
              {{ $t("lang.rms.fed.cancel") }}
            </gp-button>
            <gp-button class="w120" type="primary" @click="saveGroup">
              {{ $t("lang.rms.fed.save") }}
            </gp-button>
          </gp-form-item>
        </gp-form>
      </div>
      <div v-else class="content">
        <div class="btnBox fr mb10">
          <!-- 新增按钮 -->
          <gp-button type="primary" @click="handleChangePanel">
            {{ $t("lang.rms.fed.newlyAdded") }}
          </gp-button>
        </div>
        <!-- 弹框表格 -->
        <div class="map-manage-waitTaskGroup__table">
          <geek-customize-table
            :table-config="tableConfig"
            :data="tableData"
          >
            <template #operations="{ row }">
              <gp-link
                type="primary"
                :underline="false"
                @click="handleChangePanel('edit', row)"
              >
                {{ $t("lang.rms.fed.edit") }}
              </gp-link>
              <gp-link
                type="primary"
                :underline="false"
                @click="handleDelete(row)"
              >
                {{ $t("lang.rms.fed.delete") }}
              </gp-link>

            </template>
          </geek-customize-table>
        </div>
      </div>
    </gp-dialog>
  </div>
</template>
<script>
// 趴窝组合表达式类型
const valueTypeOptions = that => [
  // 常量
  { label: that.$t("lang.rms.fed.function.constant"), value: 0 },
  // 百分比
  { label: that.$t("lang.rms.fed.function.percent"), value: 1 },
  // 表达式
  { label: that.$t("lang.rms.fed.function.expression"), value: 2 },
];
//  管理趴窝分组 弹框
export default {
  name: "ManageWaitTaskDialog",
  props: {
    visible: Boolean,
    mapId: String,
    floorId: String,
  },
  data() {
    const validate_value = (rule, value, callback) => {
      if (this.formData.valueType !== 2) {
        if (isNaN(value) || value === "") {
          callback(new Error());
        } else {
          if (value / 1 > 999) {
            this.formData.value = 999;
          }
          callback();
        }
      } else {
        if (value === "") {
          callback(new Error());
        }

        callback();
      }
    };

    return {
      title: this.$t("lang.rms.fed.manageWaitaskGroup"),
      tableConfig: {
        columns: [
          { prop: "groupName", label: this.$t("lang.rms.fed.function.waitTaskGroup") },
          {
            prop: "valueTypeLabel",
            label: this.$t("lang.rms.fed.function.groupValueType"),
          },
          { prop: "value", label: this.$t("lang.rms.fed.function.groupValue") },
          {
            label: "lang.rms.fed.listOperation",
            width: "120",
            fixed: "right",
            slotName: "operations"
          },
        ]
      },
      tableData: [],
      tableExtendConfig: {
        sortNum: false,
        operate: [
          {
            event: "editItem",
            label: this.$t("lang.rms.fed.edit"),
          },
          {
            event: "deleteItem",
            label: this.$t("lang.rms.fed.delete"),
          },
        ],
      },
      formData: {
        groupName: "",
        valueType: "",
        value: "",
      },
      rules: {
        groupName: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnter") + this.$t("lang.rms.fed.function.waitTaskGroup"),
            trigger: "blur",
          },
        ],
        value: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnter") + this.$t("lang.rms.fed.function.groupValue"),
            trigger: "blur",
            validator: validate_value,
          },
        ],
        valueType: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseChoose") + this.$t("lang.rms.fed.function.groupValueType"),
            trigger: "change",
          },
        ],
      },

      addOrEditGroup: false,
      editGroup: false,
    };
  },
  computed: {
    tableItem() {
      return [
        { prop: "groupName", label: this.$t("lang.rms.fed.function.waitTaskGroup") },
        {
          prop: "valueTypeLabel",
          label: this.$t("lang.rms.fed.function.groupValueType"),
        },
        { prop: "value", label: this.$t("lang.rms.fed.function.groupValue") },
      ];
    },
  },
  watch: {},
  created() {
    this.valueTypeOptions = valueTypeOptions(this);
    this.findGroup();
  },
  methods: {
    changeInput() {
      this.$forceUpdate();
    },
    valueTypeChange() {
      this.$set(this.formData, "value", "");
    },
    // 删除确认
    handleDelete(rowData) {
      const groupId = rowData.id;
      this.$confirm(this.$t("lang.rms.fed.deleteWaitaskGroup"), this.$t("lang.mb.robotOperate.tips"), {
        confirmButtonText: this.$t("lang.rms.fed.confirm"),
        cancelButtonText: this.$t("lang.rms.fed.cancel"),
        type: "warning",
      }).then(() => {
        $req.post("/athena/map/cell/functional/deleteGroup", { groupId }).then(() => {
          this.$message.success(this.$t("lang.venus.web.common.successfullyDeleted"));
          this.findGroup();
        });
      }).catch(() => {});
    },
    // 查询接口
    async findGroup() {
      const params = {
        mapId: this.mapId,
      };
      const { data } = await $req.get("/athena/map/cell/functional/findGroup", params);

      this.tableData = (data || []).map(i => {
        return {
          ...i,
          valueTypeLabel: this.valueTypeOptions.find(item => item.value === i.valueType)?.label,
        };
      });
    },
    // 保存接口
    saveGroup() {
      this.$refs["groupform"].validate(valid => {
        if (valid) {
          const params = {
            ...this.formData,
            mapId: this.mapId,
          };
          $req.post("/athena/map/cell/functional/saveGroup", params).then(res => {
            if (res.code === 0) {
              this.$message.success(this.$t(res.msg));
              this.handleChangePanel();
              this.findGroup();
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 切换列表和编辑
    handleChangePanel(type, row) {
      this.$refs.groupform && this.$refs.groupform.resetFields();
      this.addOrEditGroup = !this.addOrEditGroup;
      this.editGroup = type === "edit";
      this.formData = type === "edit" ? row : {};
    },
    handleDialogClosed() {
      this.$emit("update:visible", false);
    },
  },
};
</script>
<style lang="less" scoped>
.map-manage-waitTaskGroup__form {
  max-width: 500px;
  margin: 0 auto;
  .w100p {
    width: 100%;
  }
}
</style>
<style lang="less">
.map-manage-waitTaskGroup {
  .gp-dialog {
    display: flex;
    flex-direction: column;
    margin:0 !important;
    position:absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    height: calc(100% - 180px);
  }
  .gp-dialog__body {
    padding: 15px 20px;
    flex:1;
    max-height: none !important;
    display: flex;
    flex-direction: column;
  }
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .map-manage-waitTaskGroup__table {
    flex: 1;
  }
}
</style>
