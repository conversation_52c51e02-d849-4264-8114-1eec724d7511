//通过秘钥加密密码
import JsEncrypt from 'jsencrypt'
export async function rsa(msg,data = null) {
  let resKey,rm,ts
  if(data){
    const {rsaPublicKey,random,timestamp} = data
    resKey = rsaPublicKey
    rm = random
    ts = timestamp
  }else{
    const {data} = await $req.get('/api/coreresource/auth/rsaKey/get/v1')
    const {rsaPublicKey,random,timestamp} = data
    rm = random
    ts = timestamp
    resKey = rsaPublicKey
  }

  let jse = new JsEncrypt();
  //设置公钥
  jse.setPublicKey(resKey);
  return jse.encrypt(`${msg}_${rm}_${ts}`);
  // return ''
}
