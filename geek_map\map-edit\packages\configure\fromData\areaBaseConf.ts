import { usePureNumber, useCodeQualified, useRequired } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
import { useEditMap } from "@packages/hook/useEdit";

const editMap = useEditMap();
export const AREA_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  attrStore.getAllRobotTypesByOnly();
  attrStore.getSingleLaneTypeList()
  return {
    name: "base",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "80px",
    labelPosition: "left",
    formItem: [
      // 区域ID
      {
        prop: "areaId",
        label: "lang.rms.api.result.warehouse.areaId",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        disabled() {
          return true;
        },
        // appendAttrsFn(value: string, allData: any) {
        //   return {
        //     condition: (allData?.areaType || attrStore.drawAreaType) !== DICT.AREA_SHELF,
        //   };
        // },
        rules: [usePureNumber()],
      },
      // // 区域ID - 货架区域专属
      // {
      //   prop: "areaId",
      //   label: "lang.rms.fed.region",
      //   component: "elSelect",
      //   appendAttrsFn(value: string, allData: any) {
      //     const areaDataList = editMap.value?.getLayerData("AREA") || [];
      //     const areaIdsMap = new Map();
      //     areaDataList.forEach(({ areaId }) => {
      //       areaIdsMap.set(areaId, {
      //         label: `${areaId}`,
      //         value: areaId,
      //       });
      //     });
      //     const dataVal = [...areaIdsMap.values()];
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SHELF,
      //       data: dataVal,
      //     };
      //   },
      //   rules: [usePureNumber()],
      // },
      // 区域编码
      {
        prop: "areaCode",
        label: "lang.rms.fed.mapArea.code",
        component: "elInput",
        maxlength: 20,
        showWordLimit: true,
        // disabled: true,
        // rules: [usePureNumber()],
      },
      // 区域名称
      {
        prop: "areaName",
        label: "lang.rms.fed.areaName",
        component: "elInput",
        maxlength: 20,
        showWordLimit: true,
        rules: [useRequired()],
      },
      // 区域描述
      // {
      //   prop: "desc",
      //   label: "lang.rms.fed.areaDescript",
      //   component: "elSelect",
      //   showWordLimit: true,
      //   disabled: true,
      //   data: DICT.AREA_TYPE_DICT,
      // },
      // 已选点位
      {
        prop: "cellLength",
        label: "lang.rms.fed.selctedPoint",
        component: "elInput",
        suffixName: "lang.rms.web.piece",
        disabled: true,
        get(data: any) {
          return data?.cellCodes?.length || 0;
        },
      },
      // 设备ID
      {
        prop: "deviceID",
        label: "lang.rms.fed.deviceID",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        required:true,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.deviceID = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.deviceID || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_TRAFFIC_LIGHT,
          };
        },
      },
      // 系统急停ID
      {
        prop: "deviceID",
        label: "lang.rms.fed.function.systemStopID",
        labelWidth: "90px",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        required:true,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.stopButtonId = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.stopButtonId || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_STOP,
          };
        },
      },
      // 设备别名
      {
        prop: "deviceAlias",
        label: "lang.venus.web.common.deviceAlias",
        labelWidth: "90px",
        component: "elInput",
        maxlength: 100,
        showWordLimit: true,
        pattern:/[，]/g,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.deviceAlias = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.deviceAlias || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_STOP,
          };
        },
      },
      // 单行道类型
      {
        prop: "singleLaneType",
        label: "lang.rms.fed.singleWayType",
        labelWidth: "90px",
        component: "elSelect",
        // data: DICT.SINGLE_LANG_TYPE,
        data:attrStore.singleLineList,
        appendAttrsFn(value: string, allData: any) {
          console.log("areaType >>> ", allData, allData.areaType);
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SINGLE_LANE,
          };
        },
      },
      //是否跟随(1 允许跟随，0不允许跟随)
      {
        prop: "enableFollow",
        label: "lang.rms.fed.willItFollow",
        labelWidth: "90px",
        component: "elSwitch",
        activeValue:1,
        inactiveValue:0,
        // data: DICT.SINGLE_LANG_TYPE,
        set(value: string, allData: { [k: string]: any }) {
          allData.enableFollow = value
        },
        get(allData: { [k: string]: any }) {
          // return allData?.enableFollow ? allData?.enableFollow : true
          return allData?.enableFollow
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SINGLE_LANE,
          };
        },
      },
      // 空负载模式
      {
        prop: "isLoad",
        label: "lang.rms.fed.emptyLoadMode",
        labelWidth: "90px",
        component: "elSelect",
        data: DICT.SINGLE_LANG_LOADTYPE,
        appendAttrsFn(value: string, allData: any) {
          return {
            // condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SINGLE_LANE,
            condition: [DICT.AREA_SINGLE_LANE,DICT.HIGH_ALTITUDE_OBSTACLE_AREA,DICT.PLC_LIMIT_AREA].includes(allData?.areaType || attrStore.drawAreaType)
          };
        },
      },
      //block区域机器人类型
      {
        prop: "robotTypes",
        label: "lang.rms.fed.robotType",
        // labelWidth: "90px",
        component: "elSelect",
        data: ['P40','RS5-D','RS5-DA','RS2'].map(item => {
          return {label:item,value:item}
        }),
        // loading: !attrStore.robotTypeDict,
        filterable: true,
        multiple: true,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.robotTypes = value
        },
        get(allData: { [k: string]: any }) {
          // return allData.extendJson?.adjacentBlockIds.join(',') || "";
          return allData.extendJson?.robotTypes || []
          // return allData.extendJson?.adjacentBlockIds ? allData.extendJson?.adjacentBlockIds.join(',') : ''
        },
        // get(formData: any) {
        //   return formData.funcC ? JSON.parse(formData.funcC) : [];
        // },
        // set(value: string[], formData: any) {
        //   // formData.funcC = value ? JSON.stringify(value) : "";
        //   // allData.extendJson || (allData.extendJson = {});
        //   // allData.extendJson.adjacentBlockIds = value ? value.split(',') : []
        // },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_BLOCK,
          };
        },
      },
      //block区域可连通区域id集合
      {
        prop: "adjacentBlockIds",
        label: "lang.rms.api.result.edit.map.adjacentBlockIds",
        labelWidth: "110px",
        component: "elInput",
        pattern:/[^0-9,]/g,
        set(value: string, allData: { [k: string]: any }) {
          // allData.extendJson || (allData.extendJson = {});
          // allData.extendJson.adjacentBlockIds = value ? value.split(',') : []
          allData.adjacentBlockIds = value ? value.split(',') : []
        },
        get(allData: { [k: string]: any }) {
          // return allData.extendJson?.adjacentBlockIds.join(',') || "";
          return allData?.adjacentBlockIds ? allData?.adjacentBlockIds.join(',') : null
          // return allData.extendJson?.adjacentBlockIds ? allData.extendJson?.adjacentBlockIds.join(',') : ''
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_BLOCK,
          };
        },
      },
      //避障区域空负载模式
      // {
      //   prop: "loadMode",
      //   label: "lang.rms.fed.emptyLoadMode",
      //   labelWidth: "90px",
      //   component: "elSelect",
      //   data: DICT.SINGLE_LANG_LOADTYPE,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.loadMode = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     const loadMode = allData.extendJson?.loadMode
      //     return loadMode === undefined ? '' : loadMode;
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.OBSTACLE_AREA,
      //     };
      //   },
      // },
      //关闭避障区域空载模式
      {
        prop: "loadMode",
        label: "lang.rms.fed.emptyLoadMode",
        labelWidth: "90px",
        component: "elSelect",
        required:true,
        // data: DICT.SINGLE_LANG_LOADTYPE,
        data:DICT.OBSTACLE_AVOIDANCE_LOADTYPE,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.loadMode = value;
        },
        get(allData: { [k: string]: any }) {
          const loadMode = allData.extendJson?.loadMode
          return loadMode === undefined ? '' : loadMode;
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            // condition: (allData?.areaType || attrStore.drawAreaType) === DICT.CLOSE_OBSTACLE_AVOIDANCE_AREA,
            condition: [DICT.CLOSE_OBSTACLE_AVOIDANCE_AREA,DICT.OBSTACLE_AVOIDANCE_AREA].includes(allData?.areaType || attrStore.drawAreaType),
          };
        },
      },
      //车头方向，集合区域
      {
        prop: "robotAngle",
        label: "lang.rms.fed.headstockDirection",
        labelWidth: "90px",
        component: "elSelect",
        data: DICT.DIR_DICT,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.robotAngle = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.robotAngle || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.GATHERING_AREA,
          };
        },
      },
      //静态区域限速
      {
        prop: "loadSpeedLimit",
        label: "lang.rms.fed.area.loadSpeedLimit",
        // component: "elInput",
        // maxlength: 13,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.loadSpeedLimit = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.loadSpeedLimit || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.STATIC_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        prop: "unloadSpeedLimit",
        label: "lang.rms.fed.area.unloadSpeedLimit",
        // component: "elInput",
        // maxlength: 13,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.unloadSpeedLimit = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.unloadSpeedLimit || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.STATIC_SPEED_LIMIT_AREA,
          };
        },
      },
      //避障区域
      {
        prop: "obstacleAvoidanceLength",
        label: "lang.rms.fed.area.obstacleAvoidanceLength",
        // component: "elInput",
        // maxlength: 13,
        // showWordLimit: true,
        min:-1,
        precision: 0,
        component: "elInputNumber",
        unit: "mm",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.obstacleAvoidanceLength = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.obstacleAvoidanceLength || "-1";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.OBSTACLE_AVOIDANCE_AREA,
          };
        },
      },
      {
        prop: "obstacleAvoidanceWidth",
        label: "lang.rms.fed.obstacleAvoidanceWidth",
        // component: "elInput",
        // maxlength: 13,
        // showWordLimit: true,
        min:-1,
        precision: 0,
        component: "elInputNumber",
        unit: "mm",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.obstacleAvoidanceWidth = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.obstacleAvoidanceWidth || "-1";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.OBSTACLE_AVOIDANCE_AREA,
          };
        },
      },
      //机器人调度区域
      // {
      //   prop: "ROBOT_LIMIT_COUNT",
      //   labelWidth: "140px",
      //   label: "lang.rms.fed.area.robotLimitCount",
      //   component: "elInput",
      //   maxlength: 3,
      //   // type:'number',
      //   showWordLimit: true,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.ROBOT_LIMIT_COUNT = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.ROBOT_LIMIT_COUNT || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "robotTaskThreshold",
      //   labelWidth: "140px",
      //   label: "lang.rms.fed.area.robotTaskThreshold",
      //   component: "elInput",
      //   maxlength: 3,
      //   showWordLimit: true,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.robotTaskThreshold = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.robotTaskThreshold || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "robotDemand",
      //   labelWidth: "140px",
      //   label: "lang.rms.api.result.edit.map.robotDemand",
      //   component: "elInput",
      //   maxlength: 2,
      //   showWordLimit: true,
      //   // type:'number',
      //   suffixName: "%",
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.robotDemand = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.robotDemand || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "FORK_TASK_THRESHOLD",
      //   label: "lang.rms.fed.area.forkTaskThreshold",
      //   labelWidth: "140px",
      //   component: "elInput",
      //   maxlength: 3,
      //   showWordLimit: true,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson['FORK_TASK_THRESHOLD'] = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.FORK_TASK_THRESHOLD || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "shelfTaskThreshold",
      //   label: "lang.rms.fed.area.shelfTaskThreshold",
      //   labelWidth: "140px",
      //   component: "elInput",
      //   maxlength: 3,
      //   showWordLimit: true,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.shelfTaskThreshold = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.shelfTaskThreshold || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "ROBOT_LIMIT_CONTAIN_LOCATION",
      //   label: "机器人数量是否包含当前机器人位置",
      //   labelWidth: "90px",
      //   component: "elSelect",
      //   data: DICT.WHETHER_DICT,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.ROBOT_LIMIT_CONTAIN_LOCATION = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.ROBOT_LIMIT_CONTAIN_LOCATION || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // {
      //   prop: "ROBOT_LIMIT_END_POINT",
      //   label: "是否只限制任务终点",
      //   labelWidth: "90px",
      //   component: "elSelect",
      //   data: DICT.WHETHER_DICT,
      //   set(value: string, allData: { [k: string]: any }) {
      //     allData.extendJson || (allData.extendJson = {});
      //     allData.extendJson.ROBOT_LIMIT_END_POINT = value;
      //   },
      //   get(allData: { [k: string]: any }) {
      //     return allData.extendJson?.ROBOT_LIMIT_END_POINT || "";
      //   },
      //   appendAttrsFn(value: string, allData: any) {
      //     return {
      //       condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_ROBOT,
      //     };
      //   },
      // },
      // 东向权重 - 高速路区域
      {
        prop: "eastCost",
        label: "lang.rms.fed.eastwardWeight",
        labelWidth: "90px",
        precision: 0,
        component: "elInputNumber",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_HIGHWAY_AREA,
          };
        },
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.eastCost = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.eastCost || "0";
        },
      },
      // 西向权重 - 高速路区域
      {
        prop: "westCost",
        label: "lang.rms.fed.westwardWeight",
        labelWidth: "90px",
        precision: 0,
        component: "elInputNumber",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_HIGHWAY_AREA,
          };
        },
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.westCost = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.westCost || "0";
        },
      },
      // 北向权重 - 高速路区域
      {
        prop: "southCost",
        label: "lang.rms.fed.southboundWeight",
        labelWidth: "90px",
        precision: 0,
        component: "elInputNumber",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_HIGHWAY_AREA,
          };
        },
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.southCost = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.southCost || "0";
        },
      },
      // 南向权重 - 高速路区域
      {
        prop: "northCost",
        label: "lang.rms.fed.northboundWeight",
        labelWidth: "90px",
        precision: 0,
        component: "elInputNumber",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_HIGHWAY_AREA,
          };
        },
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.northCost = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.northCost || "0";
        },
      },
      // 动态限速区域
      {
        isTitle:true,
        label: "lang.rms.fed.area.speedLimitL1",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        prop: "loadSpeedLimitL1",
        label: "lang.rms.fed.area.loadSpeedLimit",
        // component: "elInput",
        // maxlength: 100,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.loadSpeedLimitL1 = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.loadSpeedLimitL1 || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        prop: "unloadSpeedLimitL1",
        label: "lang.rms.fed.area.unloadSpeedLimit",
        // component: "elInput",
        // maxlength: 100,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.unloadSpeedLimitL1 = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.unloadSpeedLimitL1 || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        isTitle:true,
        label: "lang.rms.fed.area.speedLimitL2",
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        prop: "loadSpeedLimitL2",
        label: "lang.rms.fed.area.loadSpeedLimit",
        // component: "elInput",
        // maxlength: 100,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.loadSpeedLimitL2 = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.loadSpeedLimitL2 || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
      {
        prop: "unloadSpeedLimitL2",
        label: "lang.rms.fed.area.unloadSpeedLimit",
        // component: "elInput",
        // maxlength: 100,
        // showWordLimit: true,
        min:0,
        precision: 2,
        component: "elInputNumber",
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.unloadSpeedLimitL2 = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.unloadSpeedLimitL2 || "0";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.REAL_TIME_SPEED_LIMIT_AREA,
          };
        },
      },
    ],
  };
};
