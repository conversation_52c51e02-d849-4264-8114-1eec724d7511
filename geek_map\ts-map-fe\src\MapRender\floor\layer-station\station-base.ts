/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

type gripperOptionsType = { code: string; parkList: Array<any>; direction: number };
class LayerStation implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "station";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("station");
    this.container = container;

    this.fillStyle.color = utils.getOriginColor("STATIONS_AREA");
    this.fillStyle.visible = true;
    this.fillStyle.alpha = 0.6;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<stationData>): void {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item: any, options: any, gripperOptions: gripperOptionsType, abnormalOptions: gripperOptionsType;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatStation(item);
      if (options.hasParkList) {
        _this.drawParkStation(options, utils, mapData);
        if (item.isPopPickDeviceStation) {
          gripperOptions = utils.formatGripperStation({
            parkList: item.parkList,
            placeDir: item.placeDir,
          });
          _this.drawGripperStation(gripperOptions, utils, mapData);
          abnormalOptions = utils.formatAbnormalStation({
            parkList: item.parkList,
            placeDir: item.placeDir,
          });
          _this.drawGripperAbnormalStation(abnormalOptions, utils, mapData);
        }
      } else {
        _this.drawStation(options, utils, mapData);
      }
    }
    this.paintStationStatus();
  }

  update(arr: Array<stationData>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, code, options, station, gripperOptions, abnormalOptions;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatStation(item);
      code = options["code"];
      if (options.hasParkList) {
        _this.updateParkStation(options, mapData);
        if (item.isPopPickDeviceStation) {
          gripperOptions = utils.formatGripperStation({
            parkList: item.parkList,
            placeDir: item.placeDir,
          });
          _this.updateGripperStation(gripperOptions, utils, mapData);
          _this.updateGripperAbnormalStation(item, utils, mapData);
        }
      } else {
        station = mapData.station.getData(code);
        if (station) mapData.station.setData(code, Object.assign(station, { options }));
      }
    }

    // _this.layerStationAbnormal.render();

    this.paintStationStatus();
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    // station area
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    // console.log("station repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
  }

  private drawStation(options: mStationData, utils: any, mapData: any) {
    const { code, width, height, position, direction, typeColor, iconName } = options;

    let sprite: any = new PIXI.Sprite(utils.getResources(iconName));
    sprite.mapType = "station";
    sprite.name = code;
    sprite.stationId = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = true;
    sprite.tint = typeColor;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中
    sprite.rotation = direction; // 设置方向

    this.container.addChild(sprite);
    mapData.station.setData(options.code, { element: sprite, options });
  }

  private drawParkStation(options: mStationData, utils: any, mapData: any) {
    const _this = this;
    const { code, width, height, direction, iconName, parkList } = options;
    parkList.forEach((park: any) => {
      let sprite: any = new PIXI.Sprite(utils.getResources(iconName));
      sprite.mapType = "station";
      sprite.name = park.code;
      sprite.stationId = code;
      sprite.width = width;
      sprite.height = height;
      sprite.interactive = sprite.buttonMode = true;
      sprite.tint = park.deviceStatusColor;
      sprite.anchor.set(0.5, 0.5);
      sprite.position.set(park.position.x, park.position.y); // 使图片居中
      sprite.rotation = park.direction; // 设置方向

      _this.container.addChild(sprite);
      mapData.station.setData(park.code, { element: sprite, options });
    });
  }

  private drawGripperStation(options: gripperOptionsType, utils: any, mapData: any) {
    const _this = this;
    const { code, direction, parkList } = options;
    parkList.forEach((park: any) => {
      let sprite: any = new PIXI.Sprite(utils.getResources(park.iconName));
      sprite.mapType = "station_gripper";
      sprite.name = park.code;
      sprite.stationId = code;
      sprite.width = park.width;
      sprite.height = park.height;
      sprite.interactive = sprite.buttonMode = true;
      sprite.anchor.set(0.5, 0.5);
      sprite.position.set(park.position.x, park.position.y); // 使图片居中
      sprite.rotation = direction; // 设置方向
      _this.container.addChild(sprite);
      mapData.station.setGripperData(park.code, { element: sprite, options });
    });
  }

  private drawGripperAbnormalStation(options: gripperOptionsType, utils: any, mapData: any) {
    const _this = this;
    const { code, direction, parkList } = options;
    parkList.forEach((park: any) => {
      let sprite: any = new PIXI.Sprite(utils.getResources(park.iconName));
      sprite.mapType = "station_gripper_abnormal";
      sprite.name = park.code;
      sprite.stationId = code;
      sprite.width = park.width * 0.6;
      sprite.height = park.height * 0.6;
      sprite.interactive = sprite.buttonMode = true;
      sprite.anchor.set(0.5, 0.5);
      sprite.position.set(park.position.x, park.position.y); // 使图片居中
      sprite.rotation = direction; // 设置方向
      _this.container.addChild(sprite);
      mapData.station.setGripperAbnormalData(park.code, { element: sprite, options });
    });
  }

  private updateParkStation(options: mStationData, mapData: any) {
    const parkList = options.parkList;
    let station: any;
    parkList.forEach((park: any) => {
      station = mapData.station.getData(park.code);
      if (station) {
        station.element.tint = park.deviceStatusColor;
        mapData.station.setData(park.code, Object.assign(station, { options }));
      }
    });
  }

  private updateGripperStation(options: gripperOptionsType, utils: any, mapData: any) {
    const parkList = options.parkList;
    let station: any;
    parkList.forEach((park: any) => {
      station = mapData.station.getGripperData(park.code);
      if (station) {
        station.element.texture = utils.getResources(park.iconName);
        mapData.station.setGripperData(park.code, Object.assign(station, { options }));
      }
    });
  }

  private updateGripperAbnormalStation(options: any, utils: any, mapData: any) {
    const parkList = options.parkList;
    const _this = this;
    let direction: number;
    switch (options["placeDir"]) {
      case "EAST":
        direction = (90 + 180) * (Math.PI / 180);
        break;
      case "WEST":
        direction = (270 + 180) * (Math.PI / 180);
        break;
      case "SOUTH":
        direction = (180 + 180) * (Math.PI / 180);
        break;
      case "NORTH":
        direction = (360 + 180) * (Math.PI / 180);
        break;
      default:
        direction = 0;
        break;
    }
    let station: any;
    station = mapData.station.getGripperAbnormalDataAll();
    parkList.forEach((park: any) => {
      let code = `${park.parkId}_gripper`;
      if (station[code]) {
        if (park?.jobStatus !== "JOB_EXCEPTION") {
          mapData.station.delGripperAbnormalData(code);
        }
      } else if (park?.jobStatus === "JOB_EXCEPTION") {
        let sprite: any = new PIXI.Sprite(utils.getResources("abnormal_left"));
        sprite.mapType = "station_gripper_abnormal";
        sprite.name = park.code;
        sprite.stationId = code;
        sprite.width = park.cellWidth * 0.6;
        sprite.height = park.cellLength * 0.6;
        sprite.interactive = sprite.buttonMode = true;
        sprite.anchor.set(0.5, 0.5);
        sprite.position.set(park.iconLocation.x, -park.iconLocation.y); // 使图片居中
        sprite.rotation = direction; // 设置方向
        _this.container.addChild(sprite);
        mapData.station.setGripperAbnormalData(code, { element: sprite, options });
      }
    });
  }

  private paintStationStatus() {
    const _this = this;
    const fillStyle = _this.fillStyle;
    const lineStyle = _this.lineStyle;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    this.repaint();
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    let sum = 0;

    let geometries: Array<any> = [];
    const stations = mapData.station.getAll();
    for (let key in stations) {
      const station = stations[key];
      const options = station.options;
      if (options["stopButtonPressed"]) {
        ++sum;
        let polygon = _this.drawStationArea(options);
        graphicsGeometry.drawShape(polygon, fillStyle, lineStyle);
      }

      // 是否isWorking = false
      let geometry: any;
      if (options.hasParkList) {
        const currentPark = options.parkList.find((park: any) => station.element.name === park.code);
        if (!currentPark || currentPark.isWorking) continue;
        geometry = _this.drawStatusGeometry(currentPark["position"], options.width, options.height, utils);
        geometries.push(geometry);
      } else if (!options.isWorking) {
        geometry = _this.drawStatusGeometry(options["position"], options.width, options.height, utils);
        geometries.push(geometry);
      }
    }
    if (sum) {
      graphicsGeometry.BATCHABLE_SIZE = sum;
      const graphics = new PIXI.Graphics(graphicsGeometry);
      _this.container.addChild(graphics);
      _this.meshList.push(graphics);
    }

    if (geometries.length) {
      const shader = utils.getShader("icon", utils.getResources("stationDisable"));
      let mesh = utils.createMesh(geometries, shader);
      mesh.interactive = mesh.buttonMode = false;
      _this.container.addChild(mesh);
      _this.meshList.push(mesh);
    }
  }

  private drawStatusGeometry(position: location, width: number, height: number, utils: any) {
    width = width * 1.2;
    height = height * 1.2;
    const x = Number((position.x - width / 2).toFixed(3));
    const y = Number((position.y - height / 2).toFixed(3));
    const x1 = Number((x + width).toFixed(3));
    const y1 = Number((y + height).toFixed(3));
    return utils.drawGeometry("icon", [x, y, x1, y, x1, y1, x, y1]);
  }
  private drawStationArea(options: mStationData) {
    const points = options["polygon"];
    if (!points.length) return null;
    return new PIXI.Polygon(points);
  }
}
export default LayerStation;
