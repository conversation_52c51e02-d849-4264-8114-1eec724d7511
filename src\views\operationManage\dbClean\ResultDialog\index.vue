<template>
  <div>
    <div class="result-dialog">
      <gp-dialog
        :title="$t('lang.rms.hygeia.exeResult')"
        :visible="resultVisible"
        :close-on-click-modal="false"
        width="80%"
        @close="dialogClose"
      >
        <gp-table :data="results" style="width: 100%" @expand-change="expandChange">
          <gp-table-column type="expand" prop="details">
            <template slot-scope="props">
              <gp-table :data="props.row.details" stripe border>
                <gp-table-column prop="id" label="ID" width="60" />
                <gp-table-column prop="exeStartTime" :label="$t('lang.rms.fed.startTime')">
                  <template slot-scope="scope">
                    <span>{{ scope.row.exeStartTime | timeformatter }}</span>
                  </template>
                </gp-table-column>
                <gp-table-column prop="exeEndTime" :label="$t('lang.rms.fed.endTime')">
                  <template slot-scope="scope">
                    <span>{{ scope.row.exeEndTime | timeformatter }}</span>
                  </template>
                </gp-table-column>
                <gp-table-column prop="sourceTable" :label="$t('lang.rms.hygeia.tableName')" width="240" />
                <gp-table-column prop="targetInfo" :label="$t('lang.rms.hygeia.targetPath')" />
                <gp-table-column prop="itemExeStatus" :label="$t('lang.rms.fed.state')" />
                <gp-table-column prop="resultDesc" :label="$t('lang.rms.hygeia.failedDesc')" />
                <gp-table-column prop="pollingCount" :label="$t('lang.rms.hygeia.exeCount')" />
                <gp-table-column prop="dataCount" :label="$t('lang.rms.hygeia.exeSize')" />
              </gp-table>
            </template>
          </gp-table-column>
          <gp-table-column prop="id" label="ID" width="80" />
          <gp-table-column prop="jobStartTime" :label="$t('lang.rms.hygeia.startExeTime')" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.jobStartTime | timeformatter }}</span>
            </template>
          </gp-table-column>
          <gp-table-column prop="jobEndTime" :label="$t('lang.rms.fed.endTime')" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.jobEndTime | timeformatter }}</span>
            </template>
          </gp-table-column>
          <gp-table-column prop="cleanJobStatus" :label="$t('lang.rms.web.monitor.robot.taskState')" width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.cleanJobStatus === 'SUCCESS'" style="color: green">{{
                $t("lang.rms.hygeia.exeSuccess")
              }}</span>
              <span v-if="scope.row.cleanJobStatus === 'NEW'">{{ $t("lang.rms.hygeia.init") }}</span>
              <span v-if="scope.row.cleanJobStatus === 'ARCHIVING'">{{ $t("lang.rms.hygeia.archiving") }}</span>
              <span v-if="scope.row.cleanJobStatus === 'CLEANING'">{{ $t("lang.rms.hygeia.cleaning") }}</span>
              <span v-if="scope.row.cleanJobStatus === 'FAILED'" style="color: red">{{
                $t("lang.rms.hygeia.exeFailed")
              }}</span>
            </template>
          </gp-table-column>
          <gp-table-column prop="resultDesc" :label="$t('lang.rms.hygeia.exeResult')" />
        </gp-table>
      </gp-dialog>
    </div>
  </div>
</template>

<script>
// import request from '@/tab/utils/devopsApiRequest'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    results: {
      type: [Object, Array],
      default() {
        return [{}, []];
      },
    },
    resultVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {};
  },
  filters: {
    timeformatter(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    // timeformatter(row, column) {
    //   return new Date(row[column['property']])?.toLocaleString()
    // },
    dialogClose() {
      this.$emit("update:resultVisible", false);
    },
    expandChange(row) {
      if (!row.loadDetails) {
        $req
          .get("/athena/clean/job/items", {
            jobId: row.id,
          })
          .then(res => {
            if (res.code === 0) {
              res.data.map(item => {
                row.details.push(item);
              });
              row.loadDetails = true;
            }
          });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-dialog {
  text-align: left;
}
.box-card {
  margin-bottom: 20px;
}
</style>
