/* ! <AUTHOR> at 2023/04/19 */

/**
 * 机器人接口数据类型
 * @param id 唯一机器人ID
 * @param location 位置数据
 * @param radAngle 弧度，如：3.141592653589793
 * @param robotDisplayState 机器人显示状态: NORMAL-正常；ERROR-机器人故障；OFFLINE-系统故障；WORK-有任务
 * @param robotSeries 机器人系列：P-P系列；S-分拣机器人系列；C-抱箱机器人系列； M-M系列；F-叉车系列
 * @param robotState 机器人状态
 * @param robotType 机器人类型
 * @param taskId 机器人任务ID
 * @param width 宽
 * @param length 长
 * @param errorCode 错误码
 * @param path 机器人路径
 * @param destCellCode 终点cellCode
 * @param stationId 终点工作站ID
 * @param 其他 可选
 */
type robotData = {
  id: code;
  location: location;
  radAngle: number;
  robotDisplayState: "NORMAL" | "ERROR" | "OFFLINE" | "WORK" | string;
  robotSeries: string;
  robotState: string;
  robotType: string;
  taskId: number | string;
  length: number;
  width: number;
  occupyAreas?: Array<any>;
  errorCode?: number;
  path?: location[];
  destCellCode?: code;
  stationId?: code;
  [propName: string]: any;
  errorCoverCells?: Array<any>;
};

/**
 * 机器人地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param width 宽
 * @param length 长
 * @param robotState 机器人状态
 * @param radAngle 角度-接口弧度转角度
 * @param position 机器人位置
 * @param iconName icon名称
 * @param iconState icon状态-用于颜色显示
 * @param color
 * @param destCellCode 终点cellCode
 * @param stationId 终点工作站ID
 * @param occupyPoints
 * @param path 机器人路径
 * @param showBelt
 * @param beltDir
 * @param showBox
 * @param boxConfirm 异常货箱
 * @param boxPosition box位置信息
 * @param 其他 可选
 */
type mRobotData = {
  code: code;
  floorId: floorId;
  width: number;
  height: number;
  robotState: string;
  radAngle: number;
  position: location;
  iconName: string;
  iconState: string;
  color: color16;
  destCellCode: code | "non-existent";
  stationId: code | "non-existent";
  path: Array<any>;
  occupyPoints: Array<any>;
  showBelt: boolean;
  beltDir: any;
  showBox: boolean;
  boxConfirm: boolean;
  boxPosition?: meshPosition;
  [propName: string]: any;
};
