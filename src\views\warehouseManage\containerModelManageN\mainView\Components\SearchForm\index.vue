<template>
  <gp-form ref="searchForm" :inline="true" class="demo-form-inline" label-position="top" :model="searchForm">
    <!-- 容器模型名称 container model name -->
    <gp-form-item
      :label="$t('lang.rms.web.container.containerModel') + $t('lang.rms.fed.shelfCategor.name.msg')"
      prop="id"
    >
      <gp-input v-model="searchForm.modelName" :placeholder="$t('lang.rms.fed.pleaseEnterContent')"></gp-input>
    </gp-form-item>

    <!-- 容器类型 container type -->
    <gp-form-item :label="$t('lang.rms.web.container.containerType')" prop="move">
      <gp-select v-model="searchForm.modelCategory" clearable :placeholder="$t('lang.rms.fed.choose')">
        <gp-option
          v-for="item in containerModelCategoryDict"
          :key="item.value"
          :label="$t(item.label)"
          :value="item.value"
        />
      </gp-select>
    </gp-form-item>
    <gp-form-item class="align-bottom">
      <gp-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</gp-button>
      <gp-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</gp-button>
    </gp-form-item>
  </gp-form>
</template>

<script>
import { mapState } from "vuex";

export default {
  data() {
    // 这里存放数据
    return {
      searchForm: {
        modelName: "",
        modelType: "",
      },
      options: [],
    };
  },
  computed: {
    ...mapState("containerModal", ["editData", "containerModelCategoryDict"]),
  },
  watch: {},
  mounted() {
    this.onSubmit();
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    resetForm() {
      this.searchForm = {
        modelName: "",
        modelType: "",
      };
      this.$emit("onsubmit", { ...this.searchForm });
    },
  },
};
</script>
<style lang="scss" scoped>
.demo-form-inline .gp-form-item {
  margin-bottom: 12px;
}
.align-bottom {
  vertical-align: bottom;
}
</style>
