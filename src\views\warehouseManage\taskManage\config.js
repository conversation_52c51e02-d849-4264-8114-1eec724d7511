import i18n from "@lang"; // 引入国际化

export const taskManageColumns = () => [
  {
    label: i18n.t("lang.rms.fed.taskId"),
    prop: "jobId",
    disabled: true,
    nowrap: true,
    formatter: (record, column) => {
      return !record.jobStageId ? record[column] : "";
    },
  },
  {
    label: i18n.t("lang.rms.fed.subTaskId"),
    prop: "jobStageId",
    nowrap: true,
    width: "105",
  },
  {
    label: i18n.t("lang.rms.fed.taskType"),
    prop: "jobType",
    nowrap: true,
    width: "140",
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.robotPriority"),
    prop: "priority",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.taskState"),
    prop: "jobState",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.fed.taskInstruction"),
    prop: "jobInstruction",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.taskPhase"),
    prop: "jobPhase",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.fed.listRobotId"),
    prop: "robotId",
    nowrap: true,
  },
  { label: i18n.t("lang.rms.fed.containerId"), prop: "shelfCode", nowrap: true },
  { label: i18n.t("lang.rms.fed.stationId"), prop: "stationId", nowrap: true },
  {
    label: i18n.t("lang.rms.web.monitor.exception.info"),
    prop: "excetpionInfo",
    nowrap: true,
    width: "120",
    slotName: "exceptionInfo",
  },
  {
    label: i18n.t("lang.rms.web.charger.chargerId"),
    prop: "chargeStationId",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.startPoint"),
    prop: "startPoint",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.endPoint"),
    prop: "endPoint",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.fed.parentTaskId"),
    prop: "hostTaskId",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.web.monitor.robot.taskCreateTime"),
    prop: "createTime",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.fed.updateTime"),
    prop: "updateTime",
    nowrap: true,
  },
  {
    label: i18n.t("lang.rms.fed.textOperation"),
    showSetting: true,
    fixed: true,
    slotName: "operateBth",
    width: 240,
  },
];
