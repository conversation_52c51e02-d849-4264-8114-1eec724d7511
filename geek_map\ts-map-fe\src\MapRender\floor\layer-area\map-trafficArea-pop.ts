import { i18n } from "../../../../packages/lang/i18n";
import * as PIXI from "pixi.js";
class TrafficArea {
  private pixiInstanceCollection: any;
  private $pixiTexture: any;
  constructor() {
    // this.popData = null
    // this.$pixiTexture = null
    // this.pixiInstanceCollection = {}
  }

  drawBg(width: number, height: number) {
    const bg = new PIXI.Graphics()
    bg.name = "bg"
    bg.lineStyle(1, 0x444, 0.5)
    bg.beginFill(0xffffff)
    bg.drawRect(0, 0, width, height)
    bg.endFill()

    const sanjiao = new PIXI.Graphics()
    sanjiao.name = "sanjiao"
    sanjiao.beginFill(0xffffff, 1)
    sanjiao.lineStyle(1, 0x444, 0.5)
    sanjiao.moveTo(width / 2 - 20, height)
    sanjiao.lineTo(width / 2, height + 20)
    sanjiao.lineTo(width / 2 + 20, height)
    sanjiao.endFill()
    bg.addChild(sanjiao)
    bg.zIndex = 100
    return bg
  }

  drawText(popData: any) {
    const {
      logicId,
      robotActualNumber,
      robotDemand,
      robotNeedNumber,
      robotTaskNumber } = popData
    // <p>{{ $t("lang.rms.fed.mapArea.id") }}: {{ item.areaId }}</p>
    // <p>{{ $t("lang.rms.fed.carProportion") }}: {{ item.robotDemand }}</p>
    // <p>{{ $t("lang.rms.fed.carMount") }}: {{ item.robotNeedNumber }}</p>
    // <p>{{ $t("lang.rms.fed.carActualMount") }}: {{ item.robotActualNumber }}</p>
    // <p>{{ $t("lang.rms.fed.currentTaskMount") }}: {{ item.robotTaskNumber }}</p>
    const i18nText = {
      areaIdText: i18n.t("lang.rms.fed.mapArea.id"),
      robotDemandText: i18n.t("lang.rms.fed.carProportion"),
      robotActualNumberText: i18n.t("lang.rms.fed.carActualMount"),
      robotNeedNumberText: i18n.t("lang.rms.fed.carMount"),
      robotTaskNumberText: i18n.t("lang.rms.fed.currentTaskMount")
    }

    const style = new PIXI.TextStyle({
      fontFamily: "Arial",
      fontSize: 36,
      fontWeight: "bold",
      fill: "blue",
      wordWrap: false,
      lineJoin: "round",
      lineHeight: 40
    })
    const areaIdText = new PIXI.Text(i18nText.areaIdText + ":" + logicId, style)
    areaIdText.name = "areaId"
    // areaIdText.width = 30
    // areaIdText.height = 4
    areaIdText.x = 20
    areaIdText.y = 20
    areaIdText.alpha = 1
    areaIdText.zIndex = 110

    const robotDemandText = new PIXI.Text(i18nText.robotDemandText + ":" + robotDemand, style)
    robotDemandText.name = "robotDemand"
    // robotDemandText.width = 20
    // robotDemandText.height =4
    robotDemandText.x = 20
    robotDemandText.y = 80
    robotDemandText.alpha = 1
    robotDemandText.zIndex = 110

    const robotActualNumberText = new PIXI.Text(i18nText.robotActualNumberText + ":" + robotActualNumber, style)
    robotActualNumberText.name = "robotActualNumber"
    // robotActualNumberText.width = 20
    // robotActualNumberText.height = 4
    robotActualNumberText.x = 20
    robotActualNumberText.y = 140
    robotActualNumberText.alpha = 1
    robotActualNumberText.zIndex = 110

    const robotNeedNumberText = new PIXI.Text(i18nText.robotNeedNumberText + ":" + robotNeedNumber, style)
    robotNeedNumberText.name = "robotNeedNumber"
    // robotNeedNumberText.width = 20
    // robotNeedNumberText.height = 4
    robotNeedNumberText.x = 20
    robotNeedNumberText.y = 200
    robotNeedNumberText.alpha = 1
    robotNeedNumberText.zIndex = 110

    const robotTaskNumberText = new PIXI.Text(i18nText.robotTaskNumberText + ":" + robotTaskNumber, style)
    robotTaskNumberText.name = "robotTaskNumber"
    // robotTaskNumberText.width = 20
    // robotTaskNumberText.height = 4
    robotTaskNumberText.x = 20
    robotTaskNumberText.y = 260
    robotTaskNumberText.alpha = 1
    robotTaskNumberText.zIndex = 110

    return {
      areaIdText,
      robotDemandText,
      robotActualNumberText,
      robotNeedNumberText,
      robotTaskNumberText
    }
  }

  render(popData: any) {
    const container = new PIXI.Container()
    container.sortableChildren = true
    container.zIndex = 99999

    const {
      areaIdText,
      robotDemandText,
      robotActualNumberText,
      robotNeedNumberText,
      robotTaskNumberText
    } = this.drawText(popData)

    container.addChild(areaIdText, robotDemandText, robotActualNumberText, robotNeedNumberText, robotTaskNumberText)

    // 获取集合
    this.pixiInstanceCollection = {
      areaIdText,
      robotDemandText,
      robotActualNumberText,
      robotNeedNumberText,
      robotTaskNumberText
    }

    this.$pixiTexture = container
    container.name = "text" + popData?.areaId

    const bg = this.drawBg(container.width + 50, container.height + 50)
    container.addChild(bg)
    return container
  }

  update(data: any) {
    const { logicId, robotDemand, robotActualNumber, robotNeedNumber, robotTaskNumber } = data
    const i18nText = {
      areaIdText: i18n.t("lang.rms.fed.mapArea.id"),
      robotDemandText: i18n.t("lang.rms.fed.carProportion"),
      robotActualNumberText: i18n.t("lang.rms.fed.carActualMount"),
      robotNeedNumberText: i18n.t("lang.rms.fed.carMount"),
      robotTaskNumberText: i18n.t("lang.rms.fed.currentTaskMount")
    }
    this.pixiInstanceCollection.areaIdText.text = i18nText.areaIdText + ":" + logicId
    this.pixiInstanceCollection.robotDemandText.text = i18nText.robotDemandText + ":" + robotDemand
    this.pixiInstanceCollection.robotActualNumberText.text = i18nText.robotActualNumberText + ":" + robotActualNumber
    this.pixiInstanceCollection.robotNeedNumberText.text = i18nText.robotNeedNumberText + ":" + robotNeedNumber
    this.pixiInstanceCollection.robotTaskNumberText.text = i18nText.robotTaskNumberText + "1:" + robotTaskNumber
  }

  clear() {
    this.$pixiTexture.removeChildren()
  }
}

export default TrafficArea
