<template>
  <div class="paramsChange">
    <gp-dialog
      :title="$t('lang.page.rms.menu.systemParamConfig')"
      :visible.sync="dialogVisible"
      width="30%"
      @close="cancel"
    >
      <!-- <gp-alert
        :title="$t('lang.rms.fed.msgNotification')"
        type="warning"
        show-icon
        :closable="false"
      >
      </gp-alert> -->
      <p class="content">{{ $t("lang.rms.fed.confirm.application") }}</p>
      <span slot="footer" class="dialog-footer">
        <gp-button type="primary" @click="apply">{{ $t("lang.rms.fed.application") }}</gp-button>
        <gp-button @click="cancel">{{ $t("lang.common.cancel") }}</gp-button>
        <gp-button @click="giveUp">{{ $t("lang.rms.fed.giveUp") }}</gp-button>
      </span>
    </gp-dialog>
  </div>
</template>

<script>
export default {
  name: "DialogParamsChange",
  data() {
    return {
      dialogVisible: true,
    };
  },

  mounted() {},

  methods: {
    apply() {
      this.$emit("apply");
    },
    cancel() {
      this.$emit("close");
    },
    giveUp() {
      this.$emit("giveUp");
    },
  },
};
</script>

<style lang="less" scoped>
.paramsChange /deep/.gp-dialog__body {
  padding: 0px;
  margin-top: 10px;
  overflow: hidden;
}
.paramsChange /deep/ .gp-dialog {
  border-radius: 12px;
}
/deep/.gp-dialog__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #323334;
}
/deep/.gp-alert__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #313234;
}
/deep/.gp-alert {
  background: #fffbe6;
}
.content {
  margin: 26px 26px;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #323334;
}
</style>
