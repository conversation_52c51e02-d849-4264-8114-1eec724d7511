<template>
  <!-- x y -->
  <el-form-item :label="$t(title)">
    <el-row>
      <el-col :span="10">
        <el-form-item label="x">
          <div class="my-imput--number">
            <el-input-number v-model="curItem[xKey]" :controls="false" @onChange="locationChange" />
            <div class="define-append">mm</div>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="10" :offset="1">
        <el-form-item label="y">
          <div class="my-imput--number">
            <el-input-number v-model="curItem[yKey]" :controls="false" @onChange="locationChange" />
            <div class="define-append">mm</div>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form-item>
</template>

<script setup lang="ts">
const props = defineProps<{
  title: any;
  xKey?: any;
  yKey?: any;
  curItem?: any;
}>();

const locationChange = () => {
  console.log("x y::变了么", props.curItem[props.xKey], props.curItem[props.yKey]);
};
</script>
