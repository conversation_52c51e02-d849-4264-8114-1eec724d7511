/* ! <AUTHOR> at 2023/04/20 */
type popPosition = { scale: number; bounds: any };
class RacksData implements MRender.MapData {
  private mapData: { [propName: code]: mRackData } = {};
  private abnormalCodes: { [propName: code]: boolean } = {};

  setData(code: code, data: mRackData) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  setAbnormalCode(code: code) {
    this.abnormalCodes[code] = true;
  }

  delAbnormalCode(code: code) {
    delete this.abnormalCodes[code];
  }
  getByFloorId(floorId: floorId): void {}

  getAllAbnormalCode() {
    return Object.keys(this.abnormalCodes);
  }
  getAll() {
    return this.mapData;
  }

  delData(code: code) {
    delete this.mapData[code];
  }

  uninstall() {
    this.mapData = {};
    this.abnormalCodes = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default RacksData;
