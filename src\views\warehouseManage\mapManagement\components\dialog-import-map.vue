<template>
  <div>
    <gp-dialog
      :title="dialogImportMap.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="400px"
      border
      @close="close"
    >
      <gp-form
        ref="mapCreateform"
        v-loading="uploading"
        :model="mapCreateForm"
        label-position="right"
        element-loading-spinner="gp-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.1)"
      >
        <gp-form-item
          :label="$t('lang.rms.fed.mapName')"
          prop="mapName"
          :rules="[
            { required: true, message: $t('lang.rms.api.result.monitor.map.targetMapNameIsNull') },
            {
              max: 25,
              message: $t('lang.rms.fed.map.nameTooLong'),
            },
          ]"
        >
          <gp-input
            v-model="mapCreateForm.mapName"
            :placeholder="$t('lang.rms.fed.pleaseEnter')"
            @keyup.enter.native="submit"
          />
          <input type="text" v-show="false" />
        </gp-form-item>
        <gp-form-item prop="file">
          <gp-upload
            ref="mapUpload"
            drag
            :auto-upload="false"
            action=""
            accept=".xmap"
            :limit="2"
            :file-list="fileList"
            :on-change="fileChange"
            :on-remove="fileRemove"
            :http-request="uploadMap"
            class="map-management-upload"
          >
            <div class="gp-upload__tip" slot="tip">
              <em>{{ $t("lang.rms.fed.selectOrDropFile") }} (*.xmap)</em>
            </div>
          </gp-upload>
        </gp-form-item>
      </gp-form>

      <div slot="footer">
        <gp-button plain @click="visible = false">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button :disabled="fileList.length == 0" :loading="uploading" @click="submit" type="primary">
          {{ $t("lang.rms.fed.upload") }}
        </gp-button>
      </div>
    </gp-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";

export default {
  name: "DialogImportMap",
  data() {
    return {
      mapName: "",
      fileList: [],
      disable: false,
      uploading: false,
      mapCreateForm: {
        mapName: "",
        file: null,
      },
    };
  },
  computed: {
    ...mapState("mapManagement", ["dialogImportMap"]),
    visible: {
      get() {
        return this.dialogImportMap.visible;
      },
      set(val) {
        const { visible } = this.dialogImportMap;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogImportMap.rowData;
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["hideDialog"]),
    close() {
      this.mapName = "";
      this.fileList = [];
      this.mapCreateForm.mapName = "";
      this.mapCreateForm.file = null;
    },
    submitHandel() {
      // 啥也不干 Do nothing
    },
    uploadMap(params) {
      const formData = new FormData();
      formData.append("file", params.file);
      formData.append("name", this.mapCreateForm.mapName);
      const uploadConfig = $utils.Data.getRMSConfig().mapUploadTimeout * 1000 || 120000;
      $req
        .post("/athena/map/version/importMap", formData, {
          timeout: uploadConfig,
        })
        .then(res => {
          this.reqSuccess(res.msg);
        })
        .catch(() => {
          this.disable = false;
          this.uploading = false;
        });
    },
    submit() {
      const $form = this.$refs["mapCreateform"];
      $form.validate(res => {
        if (res) {
          this.uploading = true;
          this.disable = true;
          this.$refs.mapUpload.submit();
        }
      });
    },
    reqSuccess(msg) {
      this.disable = false;
      this.uploading = false;
      this.visible = false;
      this.$emit("refreshList");
      msg = $utils.Tools.transMsgLang(msg);
      this.$success(msg);
    },
    fileChange(file, fileList) {
      const fileName = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = fileName === "xmap";
      if (!extension) {
        this.$message({
          message: this.$t("lang.rms.api.result.monitor.map.xml.unsupportedMapFile"),
          type: "error",
        });
        return false;
      }

      this.fileList = [file];
    },
    fileRemove(file, fileList) {
      console.log(file);
      this.fileList = [];
    },
  },
};
</script>

<style lang="less" scoped></style>
