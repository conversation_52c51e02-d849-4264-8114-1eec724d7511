/* ! <AUTHOR> at 2023/04/20 */

type station = { element: any; options: mStationData };
type stationPosition = { code: code; scale: number; bounds: any };

class StationsData implements MRender.MapData {
  private mapData: { [propName: code]: station } = {};
  private gripperData: { [propName: code]: station } = {};
  private gripperAbnormalData: { [propName: code]: station } = {};

  setData(code: code, data: station) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  setGripperData(code: code, data: station) {
    this.gripperData[code] = data;
  }

  getGripperData(code: code) {
    return this.gripperData[code];
  }

  setGripperAbnormalData(code: code, data: station) {
    this.gripperAbnormalData[code] = data;
  }

  getGripperAbnormalData(code: code) {
    return this.gripperAbnormalData[code];
  }

  delGripperAbnormalData(code: code) {
    const element = this.gripperAbnormalData[code]?.element;
    element && element.destroy();
    delete this.gripperAbnormalData[code];
  }

  getGripperAbnormalDataAll() {
    return this.gripperAbnormalData;
  }

  getByFloorId(floorId: floorId): void { }

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  getAll() {
    return this.mapData;
  }

  getPositions() {
    const data = this.mapData;

    let positions: stationPosition[] = [];
    let element, position: stationPosition;
    for (let key in data) {
      element = data[key]?.element;
      if (!element) continue;

      const code = element.name.toString();
      const bounds = element.getBounds();
      let tWidth = (code.length / 2) * 12; //四个数字 大伟嫌太小了 又不能写死 就减半吧
      if (tWidth < 12) tWidth = 12;

      position = {
        code,
        scale: bounds.width / tWidth,
        bounds,
      };

      positions.push(position);
    }

    return positions;
  }

  uninstall() {
    const data = { ...this.mapData, ...this.gripperData, ...this.gripperAbnormalData };
    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};
    this.gripperData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default StationsData;
