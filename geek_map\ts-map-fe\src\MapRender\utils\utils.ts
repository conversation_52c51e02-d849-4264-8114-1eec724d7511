/* ! <AUTHOR> at 2022/08/27 */
import Mesh from "../floor/element/mesh";
import MapColor from "../config/color/color";
import MapSettings from "../config/settings/settings";

class Utils {
  private settings: MRender.MapSettings;
  private colors: MRender.MapColorConfig;
  private resources: { [key: string]: any };
  private mesh: Mesh = new Mesh();
  constructor() {
    const config = window.CommonGeekMapColor || {};
    this.colors = Object.assign(MapColor, config);

    const setting = window.CommonGeekMapSetting || {};
    this.settings = Object.assign(MapSettings, setting);
  }

  createMesh(geometries: any[], shader: any): any {
    return this.mesh.createMesh(geometries, shader);
  }

  getShader(type: "rect" | "icon" | "iconColor", texture?: any) {
    switch (type) {
      case "rect":
        return this.mesh.getShaderRect();
      case "icon":
        return this.mesh.getShaderIcon(texture);
      case "iconColor":
        return this.mesh.getShaderIconColor(texture);
    }
  }

  drawGeometry(type: "rect" | "icon" | "iconColor", position: Array<number>, vColor?: any) {
    switch (type) {
      case "rect":
        return this.mesh.drawGeometryRect(position, vColor);
      case "icon":
        return this.mesh.drawGeometryIcon(position);
      case "iconColor":
        return this.mesh.drawGeometryIconColor(position, vColor);
    }
  }

  /** formaShelf 货架 */
  formatShelf(options: shelfData): mShelfData {
    const ratio = 1 - this.settings["cellSpaceRatio"];
    const width = options["width"] * ratio;
    const height = options["length"] * ratio;
    const { shelfCode, shelfStatus, lockedState, location, placementCell } = options;

    // 兼容下角度
    let radAngle = options["radAngle"];
    if (radAngle <= -180) radAngle = radAngle + 360;
    else if (radAngle > 180) radAngle = radAngle - 360;

    // 按阙值重新换算下角度
    let rotateAngle: number = radAngle;
    if (radAngle >= -91 && radAngle < -89) {
      rotateAngle = -90; // 下南
    } else if (radAngle >= -1 && radAngle < 1) {
      rotateAngle = 0; // 右东
    } else if (radAngle >= 89 && radAngle < 91) {
      rotateAngle = 90; // 上北
    } else if (radAngle >= 179 && radAngle < 181) {
      rotateAngle = 180; // 默认左西
    }

    const x = Number((location.x - width / 2).toFixed(3));
    const y = Number((-location.y - height / 2).toFixed(3));
    const x1 = Number((x + width).toFixed(3));
    const y1 = Number((y + height).toFixed(3));
    const position = [x, y, x1, y, x1, y1, x, y1];
    const radian: number = ((180 - (rotateAngle + 90)) * Math.PI) / 180; // 弧度 按地图角度算需要+90度

    // 把最终的位置按角度算出来并获取到
    const rotateObj: any = this.rotateRect(position, radian);

    let type = "SHELF";
    if (["PPP_SHELF", "PP_SHELF"].includes(options["subModelCategory"])) {
      type = "POP_PICK";
    } else {
      type = options["shelfType"] || "SHELF";
    }

    return {
      code: shelfCode,
      shelfType: type,
      floorId: location.z,
      shelfStatus,
      lockedState,
      placementCell,
      position: rotateObj.p,
      hitArea: rotateObj.a,
    };
  }

  /** formaRack 固定货架 */
  formatRack(options: rackData): mRackData {
    const ratio = 0.6;
    const { rackCode, rackType, location, boxNum, abnormalLattice, lockBox } = options;
    let width = options["length"] * ratio;
    let height = options["width"] * ratio;

    // 0-up；90-right；180-down；270-left，
    // 后端给的rack角度就这样，不要问我为什么跟shelf的角度不一样，shelf 的0度代表right！！！
    let radAngle = options["degAngle"] || 0;
    if (radAngle <= -180) radAngle = radAngle + 360;
    else if (radAngle > 180) radAngle = radAngle - 360;

    const x = Number((location.x - width / 2).toFixed(3));
    const y = Number((-location.y - height / 2).toFixed(3));
    const x1 = Number((x + width).toFixed(3));
    const y1 = Number((y + height).toFixed(3));
    const position = [x, y, x1, y, x1, y1, x, y1]; // 0 度, 上北

    const radian: number = ((180 - radAngle) * Math.PI) / 180; // 弧度
    // 把最终的位置按角度算出来并获取到
    const rotateObj: any = this.rotateRect(position, radian);

    return {
      code: rackCode,
      floorId: location.z,
      rackType,
      boxNum,
      position: rotateObj.p,
      hitArea: rotateObj.a,
      abnormalLattice,
      lockBox,
    };
  }

  /** formatRobot 机器人 */
  formatRobot(options: robotData): mRobotData {
    const ratio = 1 - this.settings["cellSpaceRatio"];
    const { radAngle, taskId, location, robotState, errorCode, taskType, robotType, robotDisplayState } = options;

    let width = options["width"] * ratio;
    let height = options["length"] * ratio;
    let anchor = [0.5, 0.5];

    // 处理图标
    let iconName = "robot";
    let showBelt = false;
    if (options["robotSeries"] === "F") {
      iconName = "fork";
      width = height = Math.max(width, height);
      anchor = [0.5, 0.9];
    }
    switch (robotType) {
      case "S20":
      case "S20T":
        iconName += "Fat";
        break;
      case "RS5":
      case "P40":
        iconName += "Thin";
        break;
      case "SC100C":
        showBelt = true;
        break;
    }

    // 处理是否展示box
    let showBox = false;
    let boxConfirm = false;
    let boxPosition: meshPosition = [];
    if (robotType.indexOf("P40") !== -1 || robotType.indexOf("RS") !== -1) {
      const rackBoxNum = options?.onloadRack?.boxNum;
      const confirmingBoxNum = options?.onloadRack?.confirmingBoxNum;
      if (rackBoxNum && rackBoxNum > 0) {
        const boxSize = Math.min(width, height) * 0.8;
        let x = Number((location.x - boxSize / 2).toFixed(3));
        let y = Number((-location.y - boxSize / 2).toFixed(3));
        let x1 = Number((x + boxSize).toFixed(3));
        let y1 = Number((y + boxSize).toFixed(3));
        boxPosition = [x, y, x1, y, x1, y1, x, y1];
        showBox = true;
      }
      if (confirmingBoxNum && confirmingBoxNum > 0) boxConfirm = true;
    }

    let iconState = "Normal";
    let color = 0xa7ce63;
    if (taskId) {
      iconState = "Work";
      color = 0x4096ee;
    }
    if (taskId && taskType === "GO_SOMEWHERE_TO_STAY") {
      // iconState = "Stay";
      // color = 0xf0c100;
      iconState = "Normal";
      color = 0xa7ce63;
    }
    if (robotState === "DISCONNECTED") {
      iconState = "Offline";
      color = 0xc44ef2;
    }
    if (robotDisplayState === "SLEEP") {
      iconState = "Sleep";
      color = 0x6e6f66;
    }
    if (errorCode !== 0) {
      iconState = "Error";
      color = 0xe75552;
    }
    // 机器人可恢复异常达到上限
    let showLimitError = false;
    if (errorCode === 50000) {
      showLimitError = true;
    }

    return {
      code: options["id"],
      showLimitError,
      floorId: location.z,
      width: width,
      height: height,
      robotState,
      radAngle: Math.PI * 2 - radAngle + Math.PI / 2,
      position: { x: location.x, y: -location.y },
      anchor,
      iconName: iconName + iconState,
      iconState,
      color,
      destCellCode: options["destCellCode"] || "non-existent",
      stationId: options["stationId"] || "non-existent",
      path: options["path"] || [],
      occupyPoints: options["occupyAreas"] || [],
      showBelt,
      beltDir: options["robotBeltDir"] || 0,
      showBox,
      boxConfirm,
      boxPosition,
      errorCoverCells: options["errorCoverCells"] || [],
    };
  }

  /** formatCell 单元格 */
  formatCell(options: cellData): mCellData {
    const ratio = this.settings["cellSpaceRatio"];
    const { cellCode, cellType, width, length, location } = options;

    let startBounds = options.startBounds;
    if (!startBounds) {
      startBounds = {
        x: location.x - length / 2,
        y: location.y - width / 2,
      };
    }
    let spaceW = Number(((length * ratio) / 2).toFixed(3));
    let spaceH = Number(((width * ratio) / 2).toFixed(3));

    let x = Number(startBounds.x.toFixed(3));
    let y = Number((-startBounds.y - width).toFixed(3));
    let x1 = Number((x + length).toFixed(3));
    let y1 = Number((y + width).toFixed(3));

    let sx = Number((x + spaceW).toFixed(3));
    let sy = Number((y + spaceH).toFixed(3));
    let sx1 = Number((x1 - spaceW).toFixed(3));
    let sy1 = Number((y1 - spaceH).toFixed(3));
    let calScore = Math.floor(options["placementZone"]);
    // let calScore = Math.floor(10 * Math.random()); // 这是自己随机数据测试用

    if (isNaN(calScore)) calScore = -1;
    else if (calScore <= 0) calScore = -1;
    else if (calScore > 10) calScore = 1;
    else if (calScore <= 7) calScore = 7 - calScore;
    else calScore = 10 - calScore;

    const color16 = this.getOriginColor(cellType);
    // 左上、右上、右下、左下; 1 为无方向
    return {
      floorId: location.z,
      code: cellCode,
      cellType,
      sizeType: options["sizeType"],
      cellFlag: options["cellFlag"],
      loadDirs: options["loadDirs"] || null,
      unloadDirs: options["unloadDirs"] || null,
      stationId: options["stationId"] || "",
      location,
      shaderColor: this.getShaderRGB(color16),
      score: calScore,
      position: [sx, sy, sx1, sy, sx1, sy1, sx, sy1],
      nsPosition: [x, y, x1, y, x1, y1, x, y1],
      hitArea: [sx, sy, sx1, sy1],
    };
  }

  /** formatStation 工作站 */
  formatStation(options: stationData): mStationData {
    const { stationId, isWorking, location, stopButtonPressed, parkList } = options;
    let direction: number;
    switch (options["placeDir"]) {
      case "EAST":
        direction = (90 * Math.PI) / 180;
        break;
      case "WEST":
        direction = (270 * Math.PI) / 180;
        break;
      case "SOUTH":
        direction = (180 * Math.PI) / 180;
        break;
      case "NORTH":
        direction = (360 * Math.PI) / 180;
        break;
      default:
        direction = 0;
        break;
    }

    let typeColor: number;
    let iconName: string;
    switch (options["stationType"]) {
      case "COMPLEX_STATION":
        typeColor = 0xa7ce63;
        iconName = "stationComplex";
        break;
      default:
        typeColor = 0x2222aa;
        iconName = "station";
        break;
    }

    const polygon = options["polygon"] || [];
    const size = 0.6;

    let parkListMap: Array<any> = [];
    const hasParkList = parkList && parkList.length > 0 ? true : false;

    if (hasParkList) {
      for (let i = 0, len = parkList.length; i < len; i++) {
        const item: any = parkList[i];
        if (!item?.location) continue;

        let parkDirection: number;
        if (item["placeDir"]) {
          switch (item["placeDir"]) {
            case "EAST":
              parkDirection = (90 * Math.PI) / 180;
              break;
            case "WEST":
              parkDirection = (270 * Math.PI) / 180;
              break;
            case "SOUTH":
              parkDirection = (180 * Math.PI) / 180;
              break;
            case "NORTH":
              parkDirection = (360 * Math.PI) / 180;
              break;
            default:
              parkDirection = 0;
              break;
          }
        } else {
          parkDirection = direction;
        }

        let deviceStatusColor: number = this.getOriginColor("CHARGER_NORMAL");
        if (item.deviceCode) {
          switch (item["deviceState"]) {
            case "EXCEPTION":
              deviceStatusColor = 0xe75552;
              break;
            case "DISABLE":
              deviceStatusColor = 0xc44ef2;
              break;
            case "ENABLE":
              deviceStatusColor = 0x4096ee;
              break;
          }
        }

        parkListMap.push({
          code: item.parkId,
          position: { x: item.location.x, y: -item.location.y },
          isWorking: item.isWorking,
          deviceStatusColor,
          deviceState: item.deviceState || "",
          direction: parkDirection,
        });
      }
    }

    return {
      code: stationId,
      floorId: location.z,
      stationId,
      isWorking,
      direction,
      typeColor,
      iconName,
      width: size,
      height: size,
      position: { x: location.x, y: -location.y },
      polygon: polygon.map((item: location) => {
        return { x: item.x, y: -item.y };
      }),
      stopButtonPressed: stopButtonPressed ? stopButtonPressed : false,
      hasParkList,
      parkList: parkListMap,
    };
  }
  /** formatGripperStation poppic工作站抓手 */
  formatGripperStation(options: { parkList: Array<any>; placeDir: string }): {
    code: string;
    parkList: Array<any>;
    direction: number;
  } {
    const { parkList } = options;
    let direction: number;
    switch (options["placeDir"]) {
      case "EAST":
        direction = (90 + 180) * (Math.PI / 180);
        break;
      case "WEST":
        direction = (270 + 180) * (Math.PI / 180);
        break;
      case "SOUTH":
        direction = (180 + 180) * (Math.PI / 180);
        break;
      case "NORTH":
        direction = (360 + 180) * (Math.PI / 180);
        break;
      default:
        direction = 0;
        break;
    }
    let parkListMap: Array<any> = [];
    const hasParkList = parkList && parkList.length > 0 ? true : false;
    if (hasParkList) {
      for (let i = 0, len = parkList.length; i < len; i++) {
        const item: any = parkList[i];
        if (!item?.iconLocation) continue;
        let iconName: string;

        switch (item.workStatus) {
          case "NORMAL":
            iconName = "gripperIdle";
            break;
          case "DOING":
            iconName = "gripperTask";
            break;
          case "DISCONNECT":
            iconName = "gripperOffLine";
            break;
          case "EXCEPTION":
            iconName = "gripperAnomaly";
            break;
          default:
            iconName = "notWorking";
            break;
        }

        // console.log("item", item);
        if (!item.isWorking) {
          iconName = "notWorking"; //禁用
        }
        parkListMap.push({
          code: `${item.parkId}_gripper`,
          width: item.cellWidth,
          height: item.cellLength,
          position: { x: item.iconLocation.x, y: -item.iconLocation.y },
          iconName: `${iconName}_${item.parkPosition.toLowerCase()}`,
        });
      }
    }

    return {
      code: parkList[0].stationId,
      direction,
      parkList: parkListMap,
    };
  }

  /** formatAbnormalStation poppic工作站抓手显示异常icon */
  formatAbnormalStation(options: { parkList: Array<any>; placeDir: string }): {
    code: string;
    parkList: Array<any>;
    direction: number;
  } {
    const { parkList } = options;
    let direction: number;
    switch (options["placeDir"]) {
      case "EAST":
        direction = (90 + 180) * (Math.PI / 180);
        break;
      case "WEST":
        direction = (270 + 180) * (Math.PI / 180);
        break;
      case "SOUTH":
        direction = (180 + 180) * (Math.PI / 180);
        break;
      case "NORTH":
        direction = (360 + 180) * (Math.PI / 180);
        break;
      default:
        direction = 0;
        break;
    }
    let parkListMap: Array<any> = [];
    const hasParkList = parkList && parkList.length > 0 ? true : false;
    if (hasParkList) {
      for (let i = 0, len = parkList.length; i < len; i++) {
        const item: any = parkList[i];
        if (!item?.iconLocation) continue;
        let iconName: string;
        if (item?.jobStatus === "JOB_EXCEPTION") {
          parkListMap.push({
            code: `${item.parkId}_gripper`,
            width: item.cellWidth,
            height: item.cellLength,
            position: { x: item.iconLocation.x, y: -item.iconLocation.y },
            iconName: `abnormal_${item.parkPosition.toLowerCase()}`,
          });
        }
      }
    }

    return {
      code: parkList[0].stationId,
      direction,
      parkList: parkListMap,
    };
  }

  /** formatCharger 充电站 */
  formatCharger(options: chargerData): mChargerData {
    const ratio = this.settings["cellSpaceRatio"];
    const { cellWidth, cellLength, chargerId, location, startBoundLocation, cellCode } = options;

    const spaceX = Number((cellLength * ratio).toFixed(3));
    const spaceY = Number((cellWidth * ratio).toFixed(3));
    const width = Number((cellLength - spaceX).toFixed(3));
    const height = Number((cellWidth - spaceY).toFixed(3));

    let direction: number;
    switch (options["chargerDir"]) {
      case "EAST":
        direction = (90 * Math.PI) / 180;
        break;
      case "WEST":
        direction = (270 * Math.PI) / 180;
        break;
      case "SOUTH":
        direction = (180 * Math.PI) / 180;
        break;
      case "NORTH":
        direction = (360 * Math.PI) / 180;
        break;
      default:
        direction = 0;
        break;
    }

    let statusColor: color16;
    switch (options["chargerStatus"]) {
      case "ERROR":
        statusColor = this.getOriginColor("CHARGER_ERROR");
        break;
      case "OFFLINE":
        statusColor = this.getOriginColor("CHARGER_OFFLINE");
        break;
      case "WORK":
        statusColor = this.getOriginColor("CHARGER_WORK");
        break;
      default:
        statusColor = this.getOriginColor("CHARGER_NORMAL");
        break;
    }

    let position;
    if (startBoundLocation) {
      position = {
        x: Number((startBoundLocation.x + spaceX / 2 + cellLength / 2).toFixed(3)),
        y: Number((-startBoundLocation.y - spaceY / 2 - cellWidth / 2).toFixed(3)),
      };
    } else {
      position = { x: location.x, y: -location.y };
    }

    return {
      code: chargerId,
      floorId: location.z,
      statusColor,
      direction,
      width,
      height,
      position,
      cellCode,
    };
  }
  /** formatDevice 设备  */
  formatDevice(options: deviceData): mDeviceData {
    const { deviceInfoId, location, state } = options;
    const size = 0.8;

    let iconTexture;
    if (options?.type === 10) {
      // 这个是dmp的单独设备处理
      iconTexture = this.getResources("dmpDeviceError");
    } else {
      switch (state) {
        case 0: // 正常
          iconTexture = this.getResources("deviceNormal");
          break;
        case 1: // 异常
          iconTexture = this.getResources("deviceConfigError");
          break;
        case 2: // 配置异常
          iconTexture = this.getResources("deviceConfigError");
          break;
        default:
          iconTexture = this.getResources("deviceNormal");
          break;
      }
    }

    return {
      code: deviceInfoId,
      floorId: location.z,
      iconTexture,
      width: size,
      height: size,
      position: { x: location.x, y: -location.y },
    };
  }

  /** formatXDevice 设备  */
  formatXDevice(options: xDeviceData): mXDeviceData {
    const ratio = 0.7;
    const { xDeviceId, deviceType, displayColourStatus, location, width, length } = options;

    let color = 0xa7ce63;
    switch (displayColourStatus) {
      case 0: // 异常 红色
        color = 0xf2524f;
        break;
      case 1: // 离线 紫色
        color = 0xc233fb;
        break;
      case 2: // 空闲 绿色
        color = 0xa7ce63;
        break;
      case 3: // 任务 蓝色
        color = 0x4096ee;
        break;
      default:
        color = 0x4096ee;
        break;
    }

    let iconName;
    switch (deviceType) {
      case "CONVEYOR": // 输送线
        iconName = "xDevice_ssx";
        break;
      case "LIFT": // 提升机
        iconName = "xDevice_tsj";
        break;
      case "STACKER": // 碟盘机
        iconName = "xDevice_dpj";
        break;
      case "COLLECTING_TOWER": // 倒箱机
        iconName = "xDevice_dxj";
        break;
      case "TRICOLOR_LIGHT": // 三色灯
        iconName = "xDevice_ssd";
        break;
      case "FORK": // 伸缩叉
        iconName = "xDevice_ssc";
        break;
      case "AUTO_DOOR": // 自动门
        iconName = "xDevice_ad";
        break;
      default:
        iconName = "xDevice_other";
        break;
    }

    return {
      code: xDeviceId,
      floorId: location.z,
      iconName,
      color,
      width: length * ratio,
      height: width * ratio,
      position: { x: location.x, y: -location.y },
    };
  }

  /** format KnockArea  */
  formatKnockArea(options: knockAreaData): mKnockAreaData {
    const path = options["knockAreaApex"] || [];

    let result = [];
    let point;
    for (let i = 0, len = path.length; i < len; i++) {
      point = path[i];
      result.push(point.x);
      result.push(-point.y);
    }
    return {
      code: options["areaId"],
      type: "knockArea",
      shapeData: result,
    };
  }
  /** format 外部障碍物 */
  formatRealtimeObstacle(options: realtimeObstacleData): mRealtimeObstacleData {
    const { tagId, location, coverRadius, lastUpdateTime, timeout } = options;
    const timeoutStatus = Date.now() - lastUpdateTime > timeout || 0;
    const statusColor = timeoutStatus ? 0xd66379 : 0xefa747;
    return {
      code: tagId,
      type: "realtimeObstacle",
      statusColor,
      position: {
        x: location.x,
        y: -location.y,
      },
      radius: coverRadius,
    };
  }
  /** format 全局区域配置  */
  formatArea4Next(options: any): mArea4Next {
    const { color, boundaryPoints } = options;
    // 仅适用于矩形数据
    let paths: Array<any> = [];
    boundaryPoints.forEach((item: any) => {
      if (Object.prototype.toString.call(item) !== "[object Array]") return;
      if (item.length <= 0) return;
      const width = item[1].x - item[0].x;
      const height = Math.abs(item[2].y - item[1].y);

      let minX: number, minY: number;
      item.forEach((path: any, i: number) => {
        if (i === 0) {
          minX = path.x;
          minY = path.y;
        } else {
          if (minX > path.x) minX = path.x;
          if (minY > path.y) minY = path.y;
        }
      });

      paths.push({
        width: Math.abs(width),
        height: height,
        x: minX,
        y: minY - height,
      });
    });
    return {
      code: "area4Next",
      type: "area4Next",
      color: color || 0x000000,
      paths,
    };
  }
  /** format 线段  */
  formatSegment(options: segment): mSegment {
    const points = options.points || [];
    const paths = points.map(item => {
      return { x: item.x, y: -item.y };
    });
    return {
      code: options["segmentId"],
      paths,
      angle: 0,
      segmentType: options["segmentType"],
      loadDirs: options["loadDirs"],
      unloadDirs: options["unloadDirs"],
    };
  }
  /** format 直线 */
  formatRoadLine(options: segment): mSegment | null {
    const points = options.points || [];
    if (points.length != 2) return null;

    const lineWidth = Number((this.getSettings("lineWidth") / 100 / 1.5).toFixed(3));
    const p1 = { x: points[0].x, y: -points[0].y };
    const p2 = { x: points[1].x, y: -points[1].y };

    let shapeData: Array<number>, angle: number;
    if (p1.x === p2.x) {
      // 竖向直线
      if (Math.abs(p2.y) > Math.abs(p1.y)) angle = -90;
      else angle = 90;
    } else if (p1["y"] === p2["y"]) {
      // 横向直线
      if (p2.x > p1.x) angle = 0;
      else angle = 180;
    } else {
      // 斜向直线
      angle = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;
    }

    const angle1 = ((angle + 90) * Math.PI) / 180;
    const angle2 = ((angle - 90) * Math.PI) / 180;
    const topX = lineWidth * Math.cos(angle1);
    const topY = lineWidth * Math.sin(angle1);
    const botX = lineWidth * Math.cos(angle2);
    const botY = lineWidth * Math.sin(angle2);
    shapeData = [
      p1.x + botX,
      p1.y + botY,
      p1.x + topX,
      p1.y + topY,
      p2.x + topX,
      p2.y + topY,
      p2.x + botX,
      p2.y + botY,
    ];

    return {
      code: options["segmentId"],
      paths: [p1, p2],
      angle,
      shapeData,
      lineWidth,
      segmentType: options["segmentType"],
      loadDirs: options["loadDirs"],
      unloadDirs: options["unloadDirs"],
    };
  }

  /** format bezier 曲线 */
  formatRoadBezier(options: segment): mSegment | null {
    const points = options.points || [];
    if (points.length != 4) return null;

    const lineWidth = Number((this.getSettings("lineWidth") / 100 / 1.5).toFixed(3));
    return {
      code: options["segmentId"],
      paths: points.map(item => {
        return { x: item.x, y: -item.y };
      }),
      lineWidth,
      segmentType: options["segmentType"],
      loadDirs: options["loadDirs"],
      unloadDirs: options["unloadDirs"],
    };
  }

  /** name 获取 settings */
  getSettings(name: keyof MRender.MapSettings): any {
    return this.settings[name] || "";
  }

  /** name 获取 resources */
  getResources(name: string): any {
    return this.resources[name] || "";
  }

  /** format 热度颜色 */
  formatHotColor(): { [propName: number]: MRender.shaderColor } {
    const _this = this;
    const colors = _this.colors;
    const hotConf: any = colors["HOT_CONF"];

    let hotColor: any = {};
    for (let key in hotConf) {
      const color: color16 = hotConf[key];
      hotColor[key] = _this.getShaderRGB(color);
    }
    return hotColor;
  }
  /** format 路径热度颜色 */
  formatPathHotColor(): { [propName: number]: MRender.shaderColor } {
    const _this = this;
    const colors = _this.colors;
    const hotConf: any = colors["HOT_CONF"];

    let pathHotColor: any = {};
    for (let key in hotConf) {
      let color: color16 = hotConf[key];
      if (key.toString() === "0") {
        color = 0x2bef69; //单元格默认颜色
      }
      pathHotColor[key] = _this.getShaderRGB(color);
    }
    return pathHotColor;
  }

  /** 根据type 获取shader color */
  getShaderColor(type: keyof MRender.MapColorConfig): MRender.shaderColor {
    const color = this.getOriginColor(type);
    return this.getShaderRGB(color);
  }

  /** 根据type 获取原始颜色 */
  getOriginColor(type: keyof MRender.MapColorConfig): color16 {
    const colors = this.colors;
    return colors[type] || colors["DEFAULT_CELL"];
  }
  /** 获取webgl 顶点色值 */
  getShaderRGB(color16: color16): MRender.shaderColor {
    let color = color16.toString(16).toLowerCase();
    while (color.length < 6) color = "0" + color;
    let arr = [];
    for (let i = 0; i < 6; i += 2) {
      arr.push(parseInt("0x" + color.slice(i, i + 2)) / 255);
    }
    return arr.concat(arr, arr, arr);
  }

  /** 获取map layer zIndex 配置 */
  getLayerZIndex(name: string): any {
    const indexConf: any = this.settings["zIndex"];
    return indexConf[name];
  }

  /** 按 lt 等分获取曲线上的点 默认100等分 */
  getCurvePoint(p: Array<{ x: number; y: number }>, lt = 100) {
    let sx = 0;
    let sy = 0;
    let s = p.length - 1;
    const q: Array<any> = [];
    const curP = p;
    let l = p.length - 2;
    for (let i = 0; i < l; ++i) {
      q[i] = {
        x: (p[i + 1].x - p[i].x) * s - sx,
        y: (p[i + 1].y - p[i].y) * s - sy,
      };
      sx += q[i].x;
      sy += q[i].y;
    }
    q[l] = {
      x: p[l + 1].x - p[0].x - sx,
      y: p[l + 1].y - p[0].y - sy,
    };
    sx = undefined;
    sy = undefined;
    s = undefined;
    l = undefined;
    return (t: any) => {
      const paramsT = t / lt;
      let x = 0;
      let y = 0;
      for (let i = q.length - 1; i >= 0; --i) {
        x = (x + q[i].x) * paramsT;
        y = (y + q[i].y) * paramsT;
      }
      return { x: x + curP[0].x, y: y + curP[0].y };
    };
  }

  getConfig(type: "color" | "setting" | "resource") {
    switch (type) {
      case "color":
        return this.colors;
      case "setting":
        return this.settings;
      case "resource":
        return this.resources;
    }
  }

  setResources(resources: { [key: string]: any }) {
    this.resources = resources;
  }

  /**
   * 传入矩形的四个点的坐标, 再传入一个角度, 返回这个矩形旋转后的四个点的坐标
   * 传入的四个点的坐标是按照左上, 右上, 右下, 左下的顺序传入的
   * 传入的角度是顺时针旋转的弧度
   * @param position
   * @param radAngle: 必须是弧度，不然算错了算你的
   * @returns
   */
  rotateRect(position: Array<number>, radAngle: number) {
    const [x1, y1, x2, y2, x3, y3, x4, y4] = position;
    const x = (x1 + x2 + x3 + x4) / 4;
    const y = (y1 + y2 + y3 + y4) / 4;
    const x1r = Number((x + (x1 - x) * Math.cos(radAngle) - (y1 - y) * Math.sin(radAngle)).toFixed(3));
    const y1r = Number((y + (x1 - x) * Math.sin(radAngle) + (y1 - y) * Math.cos(radAngle)).toFixed(3));
    const x2r = Number((x + (x2 - x) * Math.cos(radAngle) - (y2 - y) * Math.sin(radAngle)).toFixed(3));
    const y2r = Number((y + (x2 - x) * Math.sin(radAngle) + (y2 - y) * Math.cos(radAngle)).toFixed(3));
    const x3r = Number((x + (x3 - x) * Math.cos(radAngle) - (y3 - y) * Math.sin(radAngle)).toFixed(3));
    const y3r = Number((y + (x3 - x) * Math.sin(radAngle) + (y3 - y) * Math.cos(radAngle)).toFixed(3));
    const x4r = Number((x + (x4 - x) * Math.cos(radAngle) - (y4 - y) * Math.sin(radAngle)).toFixed(3));
    const y4r = Number((y + (x4 - x) * Math.sin(radAngle) + (y4 - y) * Math.cos(radAngle)).toFixed(3));
    const maxX: number = Math.max(x1r, x2r, x3r, x4r),
      maxY: number = Math.max(y1r, y2r, y3r, y4r),
      minX: number = Math.min(x1r, x2r, x3r, x4r),
      minY: number = Math.min(y1r, y2r, y3r, y4r);
    return { p: [x1r, y1r, x2r, y2r, x3r, y3r, x4r, y4r], a: [minX, minY, maxX, maxY] };
  }

  destroy() {
    this.settings = null;
    this.colors = null;
    this.resources = null;
    this.mesh = null;
  }
}
export default Utils;
