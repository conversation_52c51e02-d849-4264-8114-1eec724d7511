/* ! <AUTHOR> at 2023/04/14 */

import CellData from "./data-cell";
import SegmentData from "./data-segment";
import ShelfData from "./data-shelf";
import RackData from "./data-rack";
import RobotData from "./data-robot";
import ChargerData from "./data-charger";
import DeadRobotData from "./data-deadRobot";
import DeviceData from "./data-device";
import StationData from "./data-station";
import StopAreaData from "./data-stopArea";
import SpeedLimitAreaData from "./data-speedLimitArea";
import ClearRobotArea from './data-clearRobotArea';

class MapData implements MRender.MapDataMain {
  cell: CellData = new CellData();
  segment: SegmentData = new SegmentData();
  shelf: ShelfData = new ShelfData();
  rack: RackData = new RackData();
  robot: RobotData = new RobotData();
  charger: ChargerData = new ChargerData();
  deadRobot: DeadRobotData = new DeadRobotData();
  device: DeviceData = new DeviceData();
  station: StationData = new StationData();
  stopArea: StopAreaData = new StopAreaData();
  speedLimitArea: SpeedLimitAreaData = new SpeedLimitAreaData();
  clearRobotArea: ClearRobotArea = new ClearRobotArea();
  uninstall() {
    this.cell.uninstall();
    this.segment.uninstall();
    this.shelf.uninstall();
    this.rack.uninstall();
    this.robot.uninstall();
    this.charger.uninstall();
    this.deadRobot.uninstall();
    this.device.uninstall();
    this.station.uninstall();
    this.stopArea.uninstall();
    this.speedLimitArea.uninstall();
    this.clearRobotArea.uninstall();
  }

  destroy() {
    this.cell.destroy();
    this.segment.destroy();
    this.shelf.destroy();
    this.rack.destroy();
    this.robot.destroy();
    this.charger.destroy();
    this.deadRobot.destroy();
    this.device.destroy();
    this.station.destroy();
    this.stopArea.destroy();
    this.speedLimitArea.destroy();
    this.clearRobotArea.destroy();
  }
}

export default MapData;
