/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { LockFilled, FireFilled } from "@ant-design/icons";
import { Radio } from "antd";

type PropsOrderData = {
  shelfData: shelfData;
  targetSelect: code;
  currentSelect: code;
  setTargetSelect: (data: string) => void;
};
function OrderPoppickTarget(props: PropsOrderData) {
  const { t } = useTranslation();
  const [shelfCode, setShelfCode] = useState("");
  const [sides, setSides] = useState([]);
  const [currentSide, setCurrentSide] = useState("");

  const [lattices, setLattices] = useState<any>({});
  const [selectData, setSelectData] = useState<code>("");

  // 处理 shelfData
  useEffect(() => {
    const shelfData = props.shelfData;
    if (!shelfData || !shelfData?.lattices || !shelfData?.lattices.length) {
      setShelfCode("");
      setSides([]);
      setCurrentSide("");
      setLattices({});
      setSelectData("");
      props.setTargetSelect(null);
      return;
    }

    formatData(shelfData);
  }, [props.shelfData]);

  useEffect(() => {
    setSelectData("");
  }, [shelfCode]);

  const clickSelect = (lattice: any) => {
    if (lattice?.relateBoxCode && lattice?.relateBoxCode !== props.currentSelect) return;

    const latticeCode = lattice.latticeCode;
    setSelectData(latticeCode);
    props.setTargetSelect(latticeCode);
  };

  const formatData = (shelfData: shelfData) => {
    const list: Array<any> = shelfData.lattices;
    let _lattices: any = {};
    let item, itemSide, rowIndex, colIndex;

    for (let i = 0, len = list.length; i < len; i++) {
      item = list[i];
      itemSide = item.shelfSide;
      if (!_lattices[itemSide]) _lattices[itemSide] = [];

      rowIndex = item.rowIndex;
      colIndex = item.colIndex;
      if (!_lattices[itemSide][rowIndex]) _lattices[itemSide][rowIndex] = [];
      _lattices[itemSide][rowIndex][colIndex] = item;
    }

    setLattices(_lattices);

    // 计算货架面数据
    const _sides = Object.keys(_lattices);
    // 数组排序
    let FIndex = _sides.indexOf("F");
    if (FIndex !== -1) {
      if (_sides[0] !== "F") {
        _sides.splice(FIndex, 1, ..._sides.splice(0, 1, _sides[FIndex]));
      }
      let BIndex = _sides.indexOf("B");
      if (BIndex !== -1) {
        if (_sides[1] !== "B") {
          _sides.splice(BIndex, 1, ..._sides.splice(1, 1, _sides[BIndex]));
        }
      }
    }
    setSides(_sides);
    setCurrentSide(_sides[0]);
  };

  const getRenderList = () => {
    if (!currentSide) return null;

    const sideLattices = lattices[currentSide] || [];
    const sumLayer = sideLattices.length;
    if (!sumLayer) return null;

    let nodes: any = [];
    let items: Array<any>, itemsNode: any;
    for (let i = sumLayer - 1; i >= 0; i--) {
      items = sideLattices[i];
      if (!items) continue;

      itemsNode = (
        <div key={i} className="poppick-list-item">
          {items.map((item: any, j: number) => {
            if (!item) return <span key={j} className="no-data" />;
            const relateBox = item?.relateBox || null;
            return (
              <span
                key={j}
                className={[
                  item?.relateBoxCode ? "has-box" : "",
                  selectData === item.latticeCode ? "active" : "",
                  item?.latticeFlag == "LOCKED" || relateBox?.lockState === 1 ? "locked" : "",
                  item?.latticeStatus == "ALLOCATED" ? "occupy-box" : "",
                ].join(" ")}
                title={item.latticeCode || ""}
                onClick={() => clickSelect(item)}
              >
                <i className="icon-status">
                  {(item?.latticeFlag == "LOCKED" || relateBox?.lockState === 1) && <LockFilled />}
                  {relateBox && relateBox?.jobIds && !!relateBox?.jobIds.length && (
                    <FireFilled style={{ color: "#ff6b6b" }} />
                  )}
                </i>
                {item?.relateBoxCode && <i className="icon-box" />}
                {item?.relateBoxCode ? item.relateBoxCode : item.latticeCode}
              </span>
            );
          })}
        </div>
      );
      nodes.push(itemsNode);
    }

    return nodes;
  };

  return (
    props.shelfData?.shelfCode && (
      <>
        <h3 className="poppick-title" style={{ marginTop: 10 }}>
          <span className="title-code">
            {t("lang.rms.fed.rackCode")}:<strong>{props.shelfData?.shelfCode || ""}</strong>
          </span>
          <Radio.Group
            size="small"
            value={currentSide}
            onChange={e => setCurrentSide(e.target.value)}
            optionType="button"
            className="poppick-radio"
          >
            {sides.map((item: any, i) => {
              return (
                <Radio.Button key={i} type="primary" value={item}>
                  {item}
                </Radio.Button>
              );
            })}
          </Radio.Group>
        </h3>

        <div className="map2d-poppick-list">{getRenderList()}</div>
      </>
    )
  );
}

export default OrderPoppickTarget;
