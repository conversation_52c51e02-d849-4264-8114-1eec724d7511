<!--机器人信息页面-->
<template>
  <geek-main-structure class="robot-information-panel-wrap" id="J_robotInfoContent">
    <section v-if="!isOnlyContent" id="J_robotInfoFormContent" class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset">
        <template #robotIds="{}">
          <geek-fuzzy-search :id="form.robotIds" query-type="Qrobot" @fuzzySearchBub="fuzzySearchBub" />
        </template>
        <template #workStationId="{}">
          <geek-fuzzy-search :id="form.workStationId" query-type="Qstation" @fuzzySearchBub="fuzzySearchBub" />
        </template>
        <template #robotPower="{}">
          <gp-slider v-model="form.power" range :max="100" style="width: 120px; height: 32px; margin: 0 10px" />
        </template>
      </geek-customize-form>
    </section>

    <section class="content">
      <div id="J_robotInfoTableHeader" style="padding-top: 10px">
        <h3 class="table-content-header">
          <span>{{ $t("lang.rms.fed.robotInformation") }}</span>

          <span class="fr">
            {{ $t("lang.rms.fed.totalNumberOfCurrentRobots") }}：{{ stat.totalCount }};
            {{ $t("lang.rms.fed.abnormal") }} {{ $t("lang.rms.fed.function.groupValue") }}：{{ stat.exceptionCount }}；
            {{ $t("lang.rms.fed.atWork") }}：{{ stat.workingCount }}； {{ $t("lang.rms.fed.charging") }}：{{
              stat.chargingCount
            }}； {{ $t("lang.rms.fed.systemRemoval") }}：{{ stat.removedCount }}；
          </span>
          <gp-button class="exportBtnSty" type="text" @click="exportInfo">{{
            $t("lang.rms.fe.data.export.button")
          }}</gp-button>
          <span class="header_dropdown fr">
            <gp-dropdown split-button>
              <span class="gp-dropdown-link">
                {{ $t("lang.rms.fet.tableHeadCheck") }}
              </span>
              <gp-dropdown-menu slot="dropdown">
                <gp-checkbox class="selectAllStyle" v-model="isSelectHeaderAll" @change="changeSelectAll">{{
                  $t("lang.rms.fed.SelectAll")
                }}</gp-checkbox>
                <!-- 🥺 -->
                <gp-tree
                  ref="headerTree"
                  :data="treeDataHeaderList"
                  node-key="id"
                  default-expand-all
                  draggable
                  show-checkbox
                  :allow-drop="allowDrop"
                  class="tree-box"
                  :props="props"
                  @check-change="handleCheckChange"
                />
              </gp-dropdown-menu>
            </gp-dropdown>
          </span>
        </h3>

        <!-- tips -->
        <div class="tip" style="visibility: hidden">
          <gp-icon name="gp-icon-warning-outline" class="tip-icon" />
          {{ $t("lang.rms.fed.horizontalScrollTips") }}
        </div>
      </div>
      <div class="robot-information-panel-wrap__table">
        <gp-slider
          v-model="sliderValue"
          class="scroll-slider"
          vertical
          :step="sliderStep"
          :height="`${tableHeight}px`"
          :show-tooltip="false"
          :min="0"
          :max="maxLen"
          :debounce="0"
          @input="sliderChange"
        />
        <geek-customize-table
          ref="robotList"
          :table-config="tableConfig"
          :data="tableData"
          @cellDblclick="cellDblclick"
          @sort-change="handleSortChange"
        />
      </div>
    </section>
    <gp-dialog
      :title="$t('lang.rms.fed.robotInformation')"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="beforeClose"
    >
      <span>{{ cacheInfo }}</span>
    </gp-dialog>
  </geek-main-structure>
</template>

<script>
let allRobotList = [];
let resizeDebounce = null;
let onkeydown = null;
let onkeyup = null;
let $domObj = null;
let $table = null;
// let size = -1;
export default {
  name: "RobotInformation",
  data() {
    return {
      size: -1,
      dialogVisible: false,
      cacheInfo: "",
      isOnlyContent: !!this.$route.query.onlycontent,
      stat: {},
      form: {
        robotIds: "",
        workStationId: "",
        errorInfo: "",
        power: [0, 100],
        taskType: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          robotIds: {
            label: "lang.rms.fed.listRobotId",
            default: "",
            slotName: "robotIds",
          },
          workStationId: {
            label: "lang.rms.fed.stationId",
            default: "",
            slotName: "workStationId",
          },
          errorInfo: {
            label: "lang.rms.web.monitor.robot.errorLevel",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.whole",
              },
              {
                value: "WARNING",
                label: "lang.rms.fed.warning",
              },
              {
                value: "ERROR",
                label: "lang.rms.fed.error",
              },
            ],
          },
          power: {
            label: "lang.rms.fed.robotPower",
            default: "",
            slotName: "robotPower",
          },
          taskType: {
            label: "lang.rms.fed.taskType",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tableConfig: {
        attrs: {
          "row-class-name": ({ row }) => {
            if (row.errorType.length > 0) return "info-row errorLine";
            return "info-row";
          },
        },
        columns: [],
      },

      listTimmer: null,
      isSearching: false,

      robotTableHeaderList: [],
      treeDataHeaderList: [], // 默认表头树
      defaultCheckedNodes: [], // 默认选中的表头
      props: {
        label: data => {
          return this.$t(data.label);
        },
      },

      sliderValue: 0,
      sliderStep: 2,
      tableHeight: 0,
      tableScroll: 0,
      maxLen: 0,
      isShift: false,
      isSelectHeaderAll: false,

      groupBy: {},
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      if (roleInfo === "guest") return true;
      else return false;
    },
  },
  activated() {
    resizeDebounce = this._tableResize(); // 为了计算高度 一切为了性能 哎
    onkeydown = e => {
      if (e.keyCode === 16) this.isShift = true;
    };
    onkeyup = e => {
      if (e.keyCode === 16) this.isShift = false;
    };
    this.getTaskTypeList();
    this.getTableHeader();

    window.addEventListener("resize", resizeDebounce, false);
    window.addEventListener("keydown", onkeydown, false);
    window.addEventListener("keyup", onkeyup, false);
    $table = this.$refs.robotList.$el;
    $table.addEventListener("mousewheel", this.handleTableScroll, false);
  },
  deactivated() {
    window.removeEventListener("resize", resizeDebounce, false);
    window.removeEventListener("keydown", onkeydown, false);
    window.removeEventListener("keyup", onkeyup, false);
    $table.removeEventListener("mousewheel", this.handleTableScroll, false);
    if (this.listTimmer) clearTimeout(this.listTimmer);
    this.listTimmer = null;
    this.tableData = [];
    this.tableConfig.height = 0;
    this.tableConfig.columns = [];
    this.treeDataHeaderList = []; // 默认表头树
    this.defaultCheckedNode = []; // 默认选中的表头
    this.tableHeight = 0;

    allRobotList = [];
    resizeDebounce = null;
    $domObj = null;
    $table = null;
    this.size = -1;
  },
  methods: {
    cellDblclick({ row }) {
      $req
        .post("/athena/helper/cache/getCacheInfo", {
          robotId: row.id,
        })
        .then(({ data, code }) => {
          if (code === 0) {
            this.cacheInfo = data || "";
          }
        })
        .finally(() => {
          this.dialogVisible = true;
        });
    },
    beforeClose() {
      this.dialogVisible = false;
    },
    changeSelectAll(select) {
      if (select) {
        this.$refs.headerTree.setCheckedNodes(this.treeDataHeaderList);
      } else {
        this.$refs.headerTree.setCheckedNodes([]);
      }
      debugger;
    },
    sliderChange(value) {
      const tableScroll = this.maxLen - value;
      if (tableScroll < 0) {
        this.maxLen = 0;
        tableScroll = 0;
      }
      this.tableScroll = tableScroll;
      this.tableData = allRobotList.slice(tableScroll, tableScroll + this.size) || [];
    },
    handleTableScroll(event) {
      if (this.isShift) return;
      let wheel = event.deltaY;
      // 滚动方向
      const down = wheel > 0;
      const max = this.maxLen;
      let num;
      if (down) {
        const next = this.tableScroll + this.sliderStep;
        num = next >= max ? max : next;
      } else {
        const pre = this.tableScroll - this.sliderStep;
        num = pre < 0 ? 0 : pre;
      }
      this.tableScroll = num;
      this.sliderValue = max - num;
      this.tableData = allRobotList.slice(num, num + this.size) || [];
    },
    exportInfo() {
      const { form, tableConfig } = this;
      const data = {
        robotIds: form.robotIds,
        workStationId: form.workStationId,
        errorInfo: form.errorInfo,
        power: form.power[0] + "," + form.power[1],
        taskType: form.taskType,
        fields: tableConfig.columns.map(item => item.prop),
        language: localStorage.getItem("curRMSLanguage"),
      };

      $req
        .post("/athena/robotStatus/export/robots", data)
        .then(({ data }) => {
          const linkEl = document.createElement("a");
          linkEl.href = data;
          const exec = /\/(\w+\.xlsx?)/.exec(data);
          if (exec) {
            linkEl.download = exec[1];
          } else {
            linkEl.target = "_blank";
          }
          document.body.appendChild(linkEl);
          linkEl.click();
          document.body.removeChild(linkEl);
        })
        .catch(() => {});
    },
    onQuery(val) {
      if (this.isSearching) return;
      if (this.listTimmer) clearTimeout(this.listTimmer);
      this.listTimmer = null;
      this.form = Object.assign(this.form, { errorInfo: val.errorInfo || "", taskType: val.taskType || "" });
      this.getRobotList();
    },
    onReset() {
      this.form = {
        robotIds: "",
        workStationId: "",
        errorInfo: "",
        power: [0, 100],
        taskType: "",
      };
    },
    getRobotList() {
      const _this = this;
      _this.isSearching = true;
      const data = {
        robotIds: _this.form.robotIds,
        workStationId: _this.form.workStationId,
        errorInfo: _this.form.errorInfo,
        power: _this.form.power[0] + "," + this.form.power[1],
        taskType: _this.form.taskType,
        groupBy: this.groupBy,
      };
      $req
        .post("/athena/robotStatus/queryAll", data)
        .then(res => {
          const resData = res.data || {};
          const robots = resData.robots || [];
          allRobotList = robots;
          _this.stat = resData.stat || {};

          if (this.size !== -1) _this._calTableScroll();

          _this.isSearching = false;
          if (_this.listTimmer) {
            clearTimeout(_this.listTimmer);
          } else {
            if (resizeDebounce) {
              resizeDebounce();
            }
          }
          _this.listTimmer = setTimeout(() => {
            _this.getRobotList();
          }, 3000);
        })
        .catch(e => (_this.isSearching = false));
    },
    robotTypeClassName({ row }) {
      if (row.errorType.length > 0) {
        return "info-row errorLine";
      }
      return "info-row";
    },
    // 接收子组件传递过来的数据
    fuzzySearchBub(params) {
      if (params.type === "Qrobot") {
        this.form.robotIds = params.pointer;
      } else {
        this.form.workStationId = params.pointer;
      }
    },
    getTableHeader() {
      $req.post("/athena/robotStatus/queryHeader").then(res => {
        const data = res.data;

        let arr = [];
        data.forEach((item, index) => {
          const key = item.key;
          item["id"] = key;
          item["label"] = item.value;
          // sort判断
          switch (key) {
            case "id":
              item.fixed = true;
              item.sortable = {
                sorter() {
                  return 0;
                },
              };
              item.width = "90";
              break;
            case "location":
            case "waitPoint":
              item.width = "120";
              break;
            case "powerPercent":
              item.sortable = {
                sorter() {
                  return 0;
                },
              };
              item.width = "120";
              break;
            case "ip":
              item.sortable = {
                sorter() {
                  return 0;
                },
              };
              item.width = "120";
              break;
            case "product":
              item.width = "90";
              break;
            case "errorType":
            case "errorSolution":
              item.width = "300";
              break;
            default:
              item.width = "150";
              break;
          }
          arr.push(item);
        });

        this.treeDataHeaderList = arr;

        let headers = arr;
        let defaultCheckedNodes = arr;
        let robotMessageHeader = $utils.Data.getRMSFEDConfig("robotMessageHeader");
        const robotMessageHeaderLen = robotMessageHeader?.length;
        this.isSelectHeaderAll = robotMessageHeaderLen === arr.length;
        if (robotMessageHeader) {
          headers = robotMessageHeader;
          defaultCheckedNodes = robotMessageHeader;
        }

        this._formatColumns(headers);
        this.defaultCheckedNodes = defaultCheckedNodes;
        this.$nextTick(() => {
          this.$refs.headerTree.setCheckedNodes(defaultCheckedNodes);
          $utils.Data.setRMSFEDConfig(headers, "robotMessageHeader");
          this.getRobotList();
        });
      });
    },
    getTaskTypeList() {
      $req.post("/athena/robotStatus/queryTaskList").then(res => {
        if (res.code !== 0) return;
        const list = res?.data || [];
        this.formConfig.configs.taskType.options = list.map(item => {
          return {
            label: item,
            value: item,
          };
        });
      });
    },

    // 配合表头选择
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.data.level === dropNode.data.level) {
        // fatherId 是父节点id
        if (draggingNode.data.fatherId === dropNode.data.fatherId) {
          return type === "prev" || type === "next";
        } else {
          return false;
        }
      } else {
        // 不同级进行处理
        return false;
      }
    },

    handleCheckChange(data, checked, indeterminate) {
      const headers = this.$refs.headerTree.getCheckedNodes() || [];
      this._formatColumns(headers);
      this.isSelectHeaderAll = this.treeDataHeaderList.length === headers.length;
      $utils.Data.setRMSFEDConfig(headers, "robotMessageHeader");
    },

    _formatColumns(headers) {
      const _this = this;
      const columns = headers.map(item => {
        let column = {
          label: item.label,
          prop: item.key,
          sortable: item.sortable || false,
          formatter: (row, column) => {
            if (!row[column]) return "--";
            if ($utils.Type.isArray(row[column])) {
              if (row[column].length > 0) {
                let txt = "";
                for (let i = 0; i < row[column].length; i++) {
                  const item = row[column][i];
                  txt = txt + (i + 1) + ". " + _this.$t(item) + "\n";
                }
                return txt;
              } else {
                return "--";
              }
            } else if ($utils.Type.isObject(row[column])) {
              row[column] = JSON.stringify(row[column]);
            }
            return _this.$t(row[column]);
          },
        };
        if (item.fixed) column.fixed = item.fixed;
        if (!item.show) column.width = "0";
        else if (item.width) {
        }
        return column;
      });
      this.tableConfig.columns = columns;
      this.$nextTick(() => {
        this.$refs.robotList.$refs.tableRef.doLayout();
      });
    },
    _calTableScroll() {
      let maxLen = allRobotList.length - this.size;
      if (maxLen < 0) maxLen = 0;
      if (maxLen !== this.maxLen) this.maxLen = maxLen;

      const tableScroll = this.tableScroll;
      this.sliderValue = maxLen - tableScroll;
      this.tableData = allRobotList.slice(tableScroll, tableScroll + this.size) || [];
    },

    _tableResize() {
      return $utils.Tools.debounce(() => {
        if (!$domObj) {
          const $content = document.querySelector("#J_robotInfoContent");
          const $form = $content.querySelector("#J_robotInfoFormContent");
          const $header = $content.querySelector("#J_robotInfoTableHeader");
          $domObj = { $content, $form, $header };
        }
        const $dom = $domObj;
        let topH = 12;
        const sumTop = $dom.$content.clientHeight - 28; // 24为上下的padding
        if ($dom.$form) topH += $dom.$form.offsetHeight;
        if ($dom.$header) topH += $dom.$header.offsetHeight;

        const tableHeight = sumTop - topH;
        this.tableHeight = tableHeight;
        // this.tableConfig.height = tableHeight - 40;
        const calSize = Math.floor(tableHeight / 48 - 1);
        if (calSize !== this.size) {
          this.size = calSize;
          this._calTableScroll();
        }
      }, 300);
    },

    handleSortChange({ prop, order }) {
      if (!order) this.groupBy = {};
      else this.groupBy = { [prop]: { ascending: "ASC", descending: "DESC" }[order] };
      this.getRobotList();
    },
  },
};
</script>

<style lang="less">
.robot-information-panel-wrap {
  display: flex;
  flex-direction: column;
  // height: calc(100% -60px);
  .content {
    // flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% -100px);
  }
  .robot-information-panel-wrap__table {
    flex: 1;
  }
}
</style>

<style lang="less" scoped>
:deep(.tree-box) {
  max-height: 320px;
  overflow-y: auto;
  padding: 6px 10px;

  .gp-tree-node__expand-icon.is-leaf {
    display: none !important;
  }
}

.selectAllStyle {
  margin-left: 10px;
}

.table-content-header {
  font-size: 16px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #eee;
  .g-box-shadow-no-top();

  span {
    font-weight: 800;

    &.fr {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

:deep(.gp-table) {
  td.gp-table__cell {
    padding: 0;
    height: 50px;
    div {
      height: 50px;
      vertical-align: middle;
    }
  }
  .errorLine {
    color: red;
  }
}

.scroll-slider {
  position: absolute;
  right: 0;
  bottom: 24px;
  z-index: 999;
  :deep(.gp-slider__runway) {
    background: #a1a3a9;
    .gp-slider__bar {
      background-color: #e4e7ed;
    }
    .gp-slider__button {
      border-radius: 5px;
      width: 12px;
      background: #898686;
      border-color: #ddd;
    }
  }
}

.header_dropdown {
  margin-right: 10px;
  top: -5px;
  display: block;
  position: relative;

  span {
    font-weight: normal !important;
  }
  :deep(.gp-dropdown__caret-button) {
    height: 32px;
  }
}

.tip {
  font-size: 13px;
  line-height: 1.3;
  padding: 18px 8px 8px;
  color: #606266;
  border-radius: 4px;
  .tip-icon {
    margin-right: 5px;
  }
}

.exportBtnSty {
  float: right;
  box-sizing: border-box;
  display: inline-block;
  margin: 0 5px;
  margin-top: -7px;
  font-size: 14px;
}
</style>
