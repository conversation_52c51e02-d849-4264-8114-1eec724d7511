/* ! <AUTHOR> at 2023/04/19 */

/**
 * 地图单独楼层初始化渲染数据
 * @param hasBg 是否有背景图
 * @param width 图片宽
 * @param height 图片高
 * @param resolution 分辨率
 * @param leftBottomPoint 左下角位置
 * @param data 地图数据
 */
type floorBackground = {
  width: number;
  height: number;
  resolution: number;
  hasBg: boolean;
  leftBottomPoint: location;
  data: Array<any>;
};

/**
 * 地图单独楼层初始化渲染数据
 * @param floorId 楼层
 * @param background 本楼层背景图
 * @param cells 本楼层单元格数据
 * @param segments 本楼层线段数据
 */
type floorData = {
  floorId: floorId;
  background: floorBackground;
  cells: Array<cellData>;
  segments: Array<any>;
};

/** 地图楼层初始化渲染数据 */
type floorsData = { [propName: floorId]: floorData };

/**
 * 地图元素渲染数据
 * @param isInitFinish init数据是否finish
 * @param cells 单元格
 * @param robots 机器人
 * @param shelves 货架
 * @param racks 货箱架
 * @param devices 设备
 * @param dmpDevices dmp设备
 * @param chargers 充电站
 * @param stations 工作站
 * @param knockAreas 机器人相撞区域
 * @param realtimeObstacles 外部障碍物
 * @param congestionMap 拥堵区域
 */
type displays = {
  isInitFinish?: boolean;
  cells?: { [propName: floorId]: Array<cellData> };
  robots?: { [propName: floorId]: Array<robotData> };
  shelves?: { [propName: floorId]: Array<shelfData> };
  racks?: { [propName: floorId]: Array<rackData> };
  devices?: { [propName: floorId]: Array<deviceData> };
  dmpDevices?: { [propName: floorId]: Array<any> };
  xDevices?: { [propName: floorId]: Array<xDeviceData> };
  chargers?: { [propName: floorId]: Array<chargerData> };
  stations?: { [propName: floorId]: Array<stationData> };
  knockAreas?: { [propName: floorId]: Array<knockAreaData> };
  realtimeObstacles?: { [propName: floorId]: Array<realtimeObstacleData> };
  speedLimitAreas?: { [propName: floorId]: Array<speedLimitAreaData> };
  congestionMap?: Array<any>;
};
