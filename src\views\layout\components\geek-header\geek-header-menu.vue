<template>
  <div class="geek-header-menu">
    <a href="javascript:;" class="arrow-btn" :class="{ 'arrow-show': arrowShow }" @click.stop="menuMove('left')">
      <gp-icon name="gp-icon-arrow-left" />
    </a>

    <div class="geek-header-menu-wrap" :class="{ 'menu-arrow-show': arrowShow }">
      <div ref="menuContent" class="menu-content">
        <gp-tag
          v-for="item in headerTabs"
          :key="item.path"
          disable-transitions
          size="middle"
          type="info"
          :closable="item.path !== '/dashboard'"
          :class="{ active: item.path === currentPath }"
          @click="goPage(item)"
          @close="closePage(item)"
        >
          {{ $t(item.title) }}
        </gp-tag>
      </div>
    </div>

    <a
      href="javascript:;"
      class="arrow-btn right-btn"
      :class="{ 'arrow-show': arrowShow }"
      @click.stop="menuMove('right')"
    >
      <gp-icon name="gp-icon-arrow-right" />
    </a>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";

export default {
  name: "GeekHeaderMenu",
  data() {
    return {
      currentPath: "",
      $menuContent: null,
      arrowShow: false,
      left: 0,
    };
  },
  computed: {
    ...mapState(["headerTabs", "selectedHeaderTab"]),
  },
  watch: {
    selectedHeaderTab(tab) {
      this.currentPath = tab.path;
    },
  },
  created() {
    let arr = this.headerTabs;
    this.currentPath = arr[arr.length - 1].path;
  },
  mounted() {
    this.$menuContent = this.$refs.menuContent;
    this.toggleArrow();
  },
  updated() {
    if (!this.$menuContent) return;
    this.toggleArrow();

    this.scrollToActiveTab();
  },
  methods: {
    ...mapMutations(["delHeaderTabs"]),
    goPage(item) {
      if (item.path === this.currentPath) return;

      this.currentPath = item.path;
      this.$router.push(item.path);
    },
    closePage(item) {
      if (this.currentPath === item.path) {
        this.delHeaderTabs(item);
        this.$nextTick(() => {
          const tabs = this.headerTabs;
          this.currentPath = tabs[tabs.length - 1].path;
          this.$router.push(this.currentPath);
        });
      } else {
        this.delHeaderTabs(item);
      }
    },
    menuMove(type) {
      const $menuContent = this.$menuContent;
      let totalWidth = $menuContent.parentElement.offsetWidth,
        menuWidth = $menuContent.offsetWidth;
      if (type === "left") {
        this.left += 120;
        if (this.left > 0) {
          this.left = 0;
        }
        $menuContent.style.transform = `translateX(${this.left}px)`;
      } else if (type === "right") {
        this.left -= 120;
        if (Math.abs(this.left) + totalWidth > menuWidth) {
          this.left = -Math.abs(totalWidth - menuWidth);
        }
        $menuContent.style.transform = `translateX(${this.left}px)`;
      }
    },

    toggleArrow() {
      const $menuContent = this.$menuContent;
      let totalWidth = $menuContent.parentElement.offsetWidth,
        menuWidth = $menuContent.offsetWidth;
      if (menuWidth <= totalWidth) {
        this.arrowShow = false;
      } else {
        this.arrowShow = true;
      }
    },

    scrollToActiveTab() {
      const $menuContent = this.$menuContent;
      let $active = $menuContent.querySelector(".active");
      if (!$active) return;

      let totalWidth = $menuContent.parentElement.offsetWidth;
      let lastRight = $active.offsetWidth + $active.offsetLeft + 5;

      if (totalWidth < lastRight) {
        this.left = -Math.abs(lastRight - totalWidth);
      } else {
        this.left = 0;
      }
      $menuContent.style.transform = `translateX(${this.left}px)`;
    },
  },
};
</script>

<style lang="less">
@menu-height: @g-header-height - 3;
.geek-header {
  .gp-header__content {
    overflow: hidden;
  }
}
.geek-header-menu {
  position: relative;
  flex: 1;
  padding-left: 47px;
  padding-right: 12px;
  overflow: hidden;

  .gp-tag {
    background: var(--gp-color-Mask-white1);
    border-color: transparent;
    margin-right: 10px;
    color: var(--gp-color-icon3-secondary);

    .gp-tag__close {
      color: var(--gp-color-icon2-primary);
      &:hover {
        color: var(--gp-color-icon3-secondary);
        background: none !important;
      }
    }

    &.active {
      color: #269bff;
      border-color: #2667ff;
      background: rgba(38, 103, 255, 0.2);
      .gp-tag__close {
        color: rgba(38, 103, 255, 1) !important;
        &:hover {
          background: none !important;
          color: var(--gp-color-blue-2) !important;
        }
      }
    }
  }
}
.geek-header-menu-wrap {
  overflow: hidden;

  &.menu-arrow-show {
    margin: 0 16px;
  }

  :deep(.gp-tag) {
    cursor: pointer;
  }
}

.menu-content {
  display: inline-flex;
  transition-duration: 0.1s;
  -moz-transition-duration: 0.1s; /* Firefox 4 */
  -webkit-transition-duration: 0.1s; /* Safari 和 Chrome */
  -o-transition-duration: 0.1s; /* Opera */
}

.arrow-btn {
  display: none;
  position: absolute;
  top: 0;
  left: 47px;
  width: 30px;
  height: 24px;
  padding: 0 8px;
  text-align: left;
  z-index: 1;
  background: linear-gradient(92deg, #1a232f 52.54%, rgba(26, 35, 47, 0) 91.55%);

  .gp-icon {
    background: var(--gp-color-Mask-white1);
    font-size: 12px;
    padding: 3px;
    border-radius: 50%;
    color: var(--gp-color-icon3-secondary);
  }

  &.right-btn {
    left: auto;
    right: 20px;
    text-align: right;
    background: linear-gradient(270deg, #262b35 52.54%, rgba(24, 26, 32, 0) 91.55%);
  }

  &.arrow-show {
    display: inline-block;
  }
}
</style>
