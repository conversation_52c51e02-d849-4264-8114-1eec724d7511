<template>
  <gp-dialog
    :visible="visible"
    width="50%"
    :close-on-click-modal="false"
    border
    append-to-body
    @close="handleDialogClose"
  >
    <template #title>
      <p>{{ $t("lang.rms.fed.cacheShelfSet") }}</p>
    </template>
    <div class="ui-cacheDialog__content">
      <div class="check-task">
        <gp-checkbox v-model="crossLane">
          {{ $t("lang.rms.fed.taskAssignmentByRoadway") }}
        </gp-checkbox>
      </div>
      <div v-for="(item, num) in cacheRackGroup" :key="item.bindRackGroupCode" class="ui-cahceDialog--group">
        <div class="form-item">
          <span>{{ $t("lang.rms.fed.shelfGroup") }}：</span>
          <gp-input v-model="item.bindRackGroupCode" :disabled="true" :placeholder="$t('lang.rms.web.placeHolder')">
            <gp-button slot="append" type="primary" icon="gp-icon-delete" @click="handleDelItem(num)"></gp-button>
          </gp-input>
        </div>
        <div class="form-item">
          <span>{{ $t("lang.rms.fed.cacheSelf") }}：</span>
          <gp-input
            v-model="item.bindRackCodes"
            type="textarea"
            :rows="4"
            :placeholder="$t('lang.rms.web.placeHolder')"
          >
          </gp-input>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="ui-cacheDialog__footer flex flex-space-between">
        <gp-button type="primary" @click="handleAddRackGroup">
          {{ $t("lang.rms.fed.addShelfGroup") }}
        </gp-button>
        <div>
          <gp-button @click="handleDialogClose" plain>{{ $t("lang.rms.fed.cancel") }}</gp-button>
          <gp-button type="primary" :loading="loading" @click="handleSave">
            {{ $t("lang.rms.fed.confirm") }}
          </gp-button>
        </div>
      </div>
    </template>
  </gp-dialog>
</template>
<script>
/**
 *  lang.rms.fed.cacheShelfSet 缓存架设置
 *  lang.rms.fed.addShelfGroup 新增货架组
 *  lang.rms.fed.shelfGroup 货架组
 *  lang.rms.fed.cacheSelf 缓存货架
 *  lang.rms.fed.taskAssignmentByRoadway 按照巷道进行任务分配
 *
 */
export default {
  props: {
    initRow: Object,
    visible: Boolean,
  },
  data() {
    return {
      cacheRackGroup: [],
      crossLane: false,
      loading: false,
    };
  },
  created() {
    const { bindRacks, crossLane } = this.initRow;
    if (!bindRacks || !bindRacks.length) this.handleAddRackGroup();
    else this.cacheRackGroup = bindRacks.map(i => Object.assign(i, {}));
    // 这边后台经常返回字段传boolan
    this.crossLane = typeof crossLane === "string" ? "true" === crossLane.toLowerCase() : crossLane;
  },
  methods: {
    async handleAddRackGroup() {
      let url = "/athena/station/generateStationBindRackGroupCode";
      const { code, data } = await $req.get(url, { id: this.initRow.stationId });
      if (code) return;
      this.cacheRackGroup.push({ bindRackGroupCode: data, bindRackCodes: "" });
    },
    handleDelItem(num) {
      this.cacheRackGroup.splice(num, 1);
    },
    handleDialogClose() {
      this.$emit("update:visible", false);
    },
    async handleSave() {
      this.loading = true;
      let bindRacks = this.cacheRackGroup.filter(i => i.bindRackCodes);
      if (this.cacheRackGroup.length && !bindRacks.length) {
        this.loading = false;
        return;
      }
      const params = Object.assign({}, { id: this.initRow.stationId, bindRacks, crossLane: this.crossLane });
      try {
        const { code, msg } = await $req.post("athena/station/updateMaxQueueNumWithRacksGroup", params);
        this.loading = false;
        if (code) return this.$message.error(this.$t(msg));
        this.$message.success(this.$t(msg));
        this.handleDialogClose();
        this.$emit("save");
      } catch (e) {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.ui-cahceDialog--group {
  padding: 0 15px;
  border: 1px solid #d2d2d2;
  border-radius: 5px;
  margin: 15px 0;
}
.form-item {
  display: flex;
  margin: 15px 0;
  span {
    min-width: 100px;
    text-align: right;
  }
  :deep(.gp-input) {
    flex: 1;
  }
}
</style>
