<template>
  <gp-dialog
    :title="$t('lang.rms.fed.buttonEdit')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    border
    width="520px"
    :on-ok="save"
  >
    <geek-customize-form ref="areaForm" :form-config="formConfig" />

    <!-- <span slot="footer" class="dialog-footer">
      <gp-button @click="close">{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </gp-button>
    </span> -->
  </gp-dialog>
</template>

<script>
export default {
  name: "EditAreaDialog",
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "150px",
          labelPosition: "right",
        },
        configs: {
          robotDemand: {
            label: "lang.rms.fed.robotProportion",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
        },
      },
    };
  },
  methods: {
    open(data) {
      const params = {
        robotDemand: data.robotDemand || 0,
      };
      this.rowData = data;
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.areaForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      return new Promise((resove, reject) => {
        try {
          const formData = this.$refs.areaForm.getData();
          const params = Object.assign({}, formData, {
            logicId: this.rowData.logicId,
          });
          $req.post("/athena/area/updateById", params).then(res => {
            this.$success();
            this.dialogVisible = false;
            this.$emit("updateMainList");
            resove();
          });
        } catch (e) {
          reject(e);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
