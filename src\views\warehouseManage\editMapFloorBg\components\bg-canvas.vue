<template>
  <div ref="canvas" class="canvas-map-image"></div>
</template>

<script>
import * as PIXI from "pixi.js";
const { Application } = PIXI;
export default {
  name: "BgCanvas",
  props: {
    zoom: {
      type: Number,
      require: true,
    },
    maxZoom: {
      type: Number,
      require: true,
    },
    minZoom: {
      type: Number,
      require: true,
    },
    mapData: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      canvasParams: {
        screenWidth: 0,
        screenHeight: 0,
        wordWidth: 0,
        wordHeight: 0,
        centerX: 0,
        centerY: 0,
        pointX: 0,
        pointY: 0,
      },
      layers: {
        back: null,
        wall: null,
        ground: null,
      },
      touchBlank: false,
      stageOriginalPos: {},
      mouseDownPoint: {},
    };
  },
  watch: {},
  methods: {
    async drawImage(mapData) {
      const { canvas } = this.$refs;
      let app = new Application({ width: canvas.offsetWidth, height: canvas.offsetHeight, transparent: true });
      canvas.innerHTML = "";
      canvas.appendChild(app.view);
      let container = new PIXI.Container();
      let sprite = PIXI.Sprite.from(mapData.imageData);
      container.addChild(sprite);
      app.stage.addChild(container);

      app.renderer.plugins.interaction.on("pointerdown", event => {
        const globalPos = event.data.global;
        // 记录下stage原来的位置
        this.stageOriginalPos = { ...app.stage.position };
        // 记录下mouse down的位置
        this.mouseDownPoint = { ...globalPos };
        this.touchBlank = true;
      });

      app.renderer.plugins.interaction.on("pointermove", event => {
        const globalPos = event.data.global;

        if (this.touchBlank) {
          // 拖拽画布
          const dx = globalPos.x - this.mouseDownPoint.x;
          const dy = globalPos.y - this.mouseDownPoint.y;
          console.log(this.stageOriginalPos.x);
          app.stage.position.set(this.stageOriginalPos._x + dx, this.stageOriginalPos._y + dy);
        }
      });

      app.renderer.plugins.interaction.on("pointerup", event => {
        this.touchBlank = false;
      });

      app.view.addEventListener("wheel", event => {
        // 因为画布是充满视窗的，所以clientX等于mouse point在renderer上的x坐标
        const globalPos = new PIXI.Point(event.clientX, event.clientY);
        const delta = event.deltaY;
        const oldZoom = app.stage.scale.x;
        let newZoom = oldZoom * 0.999 ** delta;
        this.applyZoom(app, oldZoom, newZoom, globalPos);
      });

      // this._initZoom()
      // this._drawImage(layersData)
      // this.scaleImage('init')

      // this.rotateChange(mapData.rotate)
    },

    applyZoom(app, oldZoom, newZoom, pointerGlobalPos) {
      const oldStageMatrix = app.stage.localTransform.clone();
      const oldStagePos = oldStageMatrix.applyInverse(pointerGlobalPos);
      const dx = oldStagePos.x * oldZoom - oldStagePos.x * newZoom;
      const dy = oldStagePos.y * oldZoom - oldStagePos.y * newZoom;

      app.stage.setTransform(
        app.stage.position.x + dx,
        app.stage.position.y + dy,
        newZoom,
        newZoom,
        0,
        0,
        0,
        0,
        0,
      );
    },
  },
};
</script>

<style lang="less" scoped>
.canvas-map-image {
  position: relative;
  width: 100%;
  height: 100%;
  background: #eee;
  overflow: hidden;
}
</style>
