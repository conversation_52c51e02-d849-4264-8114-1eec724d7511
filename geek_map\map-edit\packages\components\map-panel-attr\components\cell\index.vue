<template>
  <div class="attrPanel" :class="{ isBatchModifyCellFun }">
    <TabFormBase
      v-if="curSelectNode"
      ref="baseTabsRef"
      :fromData="curSelectNode"
      :title="$t(title)"
      :tabs="tabData"
      @change="change"
    >
      <template #pinpointSlot>
        <Pinpoint />
      </template>

      <template #functionsSlot="{ option }">
        <Functions :fromData="option.fromData" :updateValue="option.update" />
      </template>
    </TabFormBase>

    <el-row :gutter="20" class="btns" v-if="isBatchModifyCellFun">
      <el-col class="btnBox" :offset="6" :span="6">
        <el-button class="btn" @click="cancelBatchModify">{{$t('lang.rms.fed.cancel')}}</el-button>
      </el-col>
      <el-col class="btnBox" :span="6">
        <el-button class="btn" type="primary" @click="sureBatchModify">{{$t('lang.rms.fed.confirm')}}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref, watch, nextTick } from "vue";
import TabFormBase from "@packages/components/map-form/tabBase.vue";
import { useAttrStore } from "@packages/store/attr";
import Pinpoint from "./components/pinpoint.vue";
import Functions from "./components/functionItem.vue";
import { getTabConf, getTabItemConfByName, changeBaseTabData, batchChangeBaseTabData } from "../common";
import { NODE_CELL } from "@packages/configure/dict/nodeType";
import { getComponentStore } from "@packages/store/piniaSync";
import { defOperationFn } from "@packages/hook/useEvent";
import { useEditMap } from "@packages/hook/useEdit";

const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(attrStore);
const editMap = useEditMap();

const props = defineProps<{
  panelConf?: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
  title: string;
}>();

const hiddenTabs: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenTabs || []);
const hiddenCodes: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenCodes || []);
const disabledCodes: ComputedRef<string[]> = computed(() => props.panelConf?.disabledCodes || []);
const baseTabsRef = ref();

// 这里是批量编辑功能的逻辑
const menuStore = getComponentStore("menuStore")();
const menuStoreRef = menuStore ? storeToRefs(menuStore) : null;
const isBatchModifyCellFun: ComputedRef<boolean> = computed(() => {
  if (!menuStoreRef) {
    return false;
  }
  return <boolean>menuStoreRef.isBatchModifyCellFun.value;
});

const tabData = computed(() => {
  // 如果是批量编辑功能
  if (isBatchModifyCellFun.value) {
    return [getTabItemConfByName(NODE_CELL, "func", baseTabsRef.value)];
  }

  return getTabConf(
    {
      hiddenTabs: hiddenTabs.value,
      hiddenCodes: hiddenCodes.value,
      disabledCodes: disabledCodes.value,
      type: NODE_CELL,
    },
    attrStore,
    baseTabsRef.value,
  );
});

const curSelectNode = computed(() => {
  if (isBatchModifyCellFun.value) {
    return {};
  }
  return attrStoreRef.curNodeDataByIndex.value;
});

watch(
  attrStoreRef.curNodeDataByIndex,
  value => {
    if (isBatchModifyCellFun.value) {
      return;
    }
    baseTabsRef.value && baseTabsRef.value.setItemAll(value);
  },
  {
    deep: true,
    immediate: true,
  },
);

function change(option: any) {
  if (isBatchModifyCellFun.value) {
    // TODO: 当批量编辑功能点的时候, 不再实时去更新节点数据, 只有在点击完成时一次更新
    // batchChangeBaseTabData(option, attrStore);
  } else {
    changeBaseTabData(option, attrStore);
  }
}

function getRef() {
  return baseTabsRef.value;
}

function cancelBatchModify() {
  defOperationFn(<any>editMap.value, {
    isDefMode: true,
    isClearSelect: true,
  });
}

function sureBatchModify() {
  const option = baseTabsRef.value.getFormData();
  batchChangeBaseTabData(option, attrStore);
  nextTick(() => {
    cancelBatchModify();
  });
}

defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;

  &.isBatchModifyCellFun {
    padding-bottom: 40px;
    box-sizing: border-box;
  }

  .btns {
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}
</style>
