<template>
  <geek-main-structure>
    <geek-tabs-nav :nav-list="navList" :default-active="activeNavId" @select="tabsNavChange" />
    <!--    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />-->
    <geek-customize-table
      :key="refresh"
      ref="customizeTableRef"
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @save="save"
      v-loading="loading"
      @expandChange="expandChange"
    >
      <template #inputParam="{ row }">
        <div v-if="row.inputParam?.length">
          <gp-tag
            v-for="(p,index) in row.inputParam"
            :key="index"
            effect="dark"
            style="margin-left: 4px"
          >
            {{ p }}
          </gp-tag>
        </div>
        <p v-else>-</p>
      </template>
      <template #remark="{ row }">
        <div v-if="row.remark">
          <span>{{row.remark}}</span>
        </div>
        <span v-else>-</span>
      </template>
      <template #rowExpand="{ row }">
        <expend-capacity
          v-if="activeNavId === 'function'"
          :id="row.functionId"
          :row-data="row.deviceFunctionPlcInstructionDtos"
          :inputParam="row.inputParam"
          @delete="deleteItem"
          @copy="copyItem"
          @add="addItem"
        ></expend-capacity>
        <expend-status
          v-else
          :id="row.statusId"
          :row-data="row.deviceStatusPlcInstructionDto"
          @delete="deleteItem"
          @add="addItem"
        ></expend-status>
      </template>
    </geek-customize-table>
    <gp-dialog
      :title="$t('lang.rms.fed.validateSave')"
      :visible.sync="verifyDialog"
      width="25%"
    >
      <verify-dialog
        v-if="verifyDialog"
        :verify-info="verifyInfo"
      >
      </verify-dialog>
    </gp-dialog>
  </geek-main-structure>
</template>

<script>
import { getFunData, getStatusData,updateFunData,updateStatusData } from "./api";
//设备能力
import ExpendCapacity from "./component/ExpendCapacity";
//设备状态
import ExpendStatus from "./component/ExpendStatus";
//保存校验
import VerifyDialog from './component/VerifyDialog'
import deviceMixin from "../deviceMixin/deviceMixin";

export default {
  mixins: [deviceMixin],
  name: "equipmentManage",
  components: { ExpendCapacity, ExpendStatus, VerifyDialog },
  computed: {
    //动态属性
    ctrlAttr() {
      const attr = {};
      if (this.activeNavId === "function") {
        attr["list"] = "deviceFunctionPlcInstructionDtos";
        attr["id"] = "functionId";
      } else {
        attr["list"] = "deviceStatusPlcInstructionDto";
        attr["id"] = "statusId";
      }
      return attr;
    },
    rowKey() {
      if(this.activeNavId === "function"){
        return "functionId"
      }else{
        return "statusId"
      }
    }
  },
  data() {
    return {
      refresh:Date.now(),
      tableExpandRowKeys:[],
      verifyInfo:null,
      verifyDialog:false,
      loading:false,
      activeNavId: "function",
      navList: [
        {
          id: "function",
          name: "设备功能",
          text: "设备功能",
          columns: [
            { label: "lang.rms.web.device.functionId", prop: "functionId",width: "140" },
            { label: "lang.rms.map.dmp.deviceCode", prop: "deviceCode" },
            { label: "lang.rms.web.device.functionName", prop: "deviceFunctionName" },
            { label: "lang.rms.web.device.functionCode", prop: "deviceFunctionCode" },
            { label: "lang.rms.web.device.plcPoint", prop: "plcPoint", width: "140" },
            { label: "lang.rms.web.device.inputParam", prop: "inputParam", slotName: "inputParam" },
            { label: "lang.venus.web.common.remark", prop: "remark", slotName: "remark" },
          ],
        },
        {
          id: "status",
          name: "设备属性",
          text: "设备属性",
          columns: [
            { label: "lang.rms.web.device.attributeId", prop: "statusId", width: "140" },
            { label: "lang.venus.web.common.deviceCode", prop: "deviceCode" },
            { label: "lang.rms.web.device.attributeName", prop: "statusName" },
            { label: "lang.rms.web.device.functionCode", prop: "statusCode" },
            { label: "lang.rms.web.device.plcPoint", prop: "plcPoint" },
            { label: "lang.rms.web.map.version.mapChange", prop: "remark", slotName: "remark" },
            // { label: "", prop: "empty", },
          ],
        },
      ],
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          chargerId: {
            label: "lang.rms.web.charger.chargerId",
            default: "",
            tag: "input",
          },
          type: {
            label: "lang.rms.web.charger.type",
            default: "",
            tag: "input",
          },
          hostCode: {
            label: "lang.rms.web.charger.hostCode",
            default: "",
            tag: "input",
          },
          interactiveModel: {
            label: "lang.rms.web.charger.interactive",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          "row-key":"expendKey",
          ref: "tableRef",
          // "row-key": "functionId",
          // index: true,
          // 'default-expand-all':true
        },
        expand: { slotName: "rowExpand" },
        actions: [
          {
            label: "auth.rms.mapManage.button.CellEdit4Save",
            type: "primary",
            handler: "save",
          },
        ],
        columns: [],
      },
    };
  },
  activated() {
    this.getFnList();
  },
  methods: {
    expandChange({ rowList }) {
      this.tableExpandRowKeys = rowList.map(item => item.expendKey);
    },
    resizeExpand() {
      // this.refresh=Date.now()
      // this.$nextTick(() => {
      //   const { customizeTableRef } = this.$refs;
      //   if (customizeTableRef) {
      //     const { tableData, tableExpandRowKeys } = this;
      //     tableExpandRowKeys.forEach(key => {
      //       const dataItem = tableData.find(item => item.expendKey === key);
      //       if (dataItem) {
      //         const { tableRef } = customizeTableRef.$refs
      //         tableRef && tableRef.toggleRowExpansion(dataItem, true);
      //       }
      //     });
      //   }
      // });
    },
    tabsNavChange(id) {
      this.activeNavId = id;
      if (id === "function") {
        this.getFnList();
      } else {
        this.getStatusList();
      }
    },
    //获取设备功能列表
    async getFnList() {
      this.loading = true
      const { deviceCode } = this.$route.query;
      const res = await getFunData({ deviceCode });
      const { code, data } = res;
      this.loading = false
      if (code) return;
      this.tableData = data.map((item,index) => {
        return {expendKey:index,...item}
      })
    },
    //获取设备状态列表
    async getStatusList() {
      const { deviceCode } = this.$route.query;
      const res = await getStatusData({ deviceCode });
      const { code, data } = res;
      if (code) return;
      this.tableData = data.map((item,index) => {
        return {expendKey:index,...item}
      })
    },
    addItem({ id, data }) {
      const { list: listName, id: idName } = this.ctrlAttr;
      const findIndex = this.tableData.findIndex(item => item[idName] === id);
      if (this.activeNavId === "function") {
        this.tableData[findIndex][listName].push(data);
      } else {
        this.tableData[findIndex][listName] = data;
      }
      this.resizeExpand()
    },
    deleteItem({ id, delIndex }) {
      const { list: listName, id: idName } = this.ctrlAttr;
      const findIndex = this.tableData.findIndex(item => item[idName] === id);
      if (this.activeNavId === "function") {
        this.tableData[findIndex][listName].splice(delIndex, 1);
      } else {
        this.tableData[findIndex][listName] = null;
      }
      this.resizeExpand()
    },
    copyItem({ id, data }) {
      const { list: listName, id: idName } = this.ctrlAttr;
      const findIndex = this.tableData.findIndex(item => item[idName] === id);
      this.tableData[findIndex][listName].push(data);
      this.resizeExpand()
    },
    onQuery() {

    },
    onReset() {

    },
    //保存验证
    saveVerify() {
      let flag = true
      const verifyInfo = {}
      const addId = (id) => {
        if(!verifyInfo[id]) verifyInfo[id] = [];
      }
      const addAttr = (id,index,attrName) => {
        addId(id)
        const arr = verifyInfo[id]
        const fIndex = arr.findIndex(item => item.index === index)
        if(fIndex !== -1){
          verifyInfo[id][fIndex].attrName.push(attrName)
        }else{
          verifyInfo[id].push({index,attrName:[attrName]})
          // verifyInfo[id][fIndex].attrName.push(attrName)
        }
      }
      if(this.activeNavId === "function"){
        this.tableData.forEach(item1 => {
          const {functionId,deviceFunctionPlcInstructionDtos = []} = item1
          deviceFunctionPlcInstructionDtos.forEach((item2,index2) => {
            const {dataDealType,inputParam,plcAddressValue,plcId,deviceCode,...others} = item2
            //写入固定值
            if(dataDealType === 0 && plcAddressValue === ''){
              addAttr(functionId,index2,'plcAddressValue')
            }
            //写入参数值
            if([1,2].includes(dataDealType) && inputParam === ''){
              addAttr(functionId,index2,'inputParam')
            }
            for(let key in others){
              if(others[key] === '') {
                addAttr(functionId,index2,key)
              }
            }
          })
        })
      }else{
        this.tableData.forEach(item1 => {
          const {statusId,deviceStatusPlcInstructionDto} = item1
          if(!deviceStatusPlcInstructionDto){
            addAttr(statusId,0,'deviceStatusPlcInstructionDto')
            return;
          }
          const {checkType,plcAddressValue,plcId,deviceCode,...others} = deviceStatusPlcInstructionDto
          //只读取
          if(checkType !== 0 && plcAddressValue === ''){
            addAttr(statusId,0,'plcAddressValue')
          }
          for(let key in others){
            if(others[key] === '') {
              addAttr(statusId,1,key)
            }
          }
        })
      }
      const len = (Object.keys(verifyInfo)).length
      flag = !len;
      console.log(flag,verifyInfo)
      return {flag,verifyInfo}
    },
    async save() {
      const {flag,verifyInfo} = this.saveVerify()
      // return
      if(!flag){
        this.verifyDialog = true
        this.verifyInfo = verifyInfo
        return
      }
      const { deviceCode } = this.$route.query;
      const data = {
        deviceCode,
      }
      if(this.activeNavId === "function"){
        data['deviceFunctionDtos'] = this.tableData
      }else{
        data['deviceStatusDtos'] = this.tableData
      }
      const saveApi = this.activeNavId === "function" ? updateFunData : updateStatusData;
      const res = await saveApi(data)
      const {code,msg} = res
      this.$message({
        message: this.$t(msg),
        type: code ? 'warning' : 'success'
      });
      if(!code){
        this.tabsNavChange(this.activeNavId)
      }
      // this.$alert()
    },

  },
};
</script>

<style scoped>

</style>
