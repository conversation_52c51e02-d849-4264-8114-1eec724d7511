/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /**  地图ready的callback事件 */
  export type mapReady = () => void;
  /**  地图rendered类型 */
  export type renderedType = "floorRendered" | "displayRendered" | "mapPosition" | "abnormalRack";

  /** 地图trigger事件 */
  export type triggerType = "click" | "rect" | "ranging" | "stop";

  /** layer 层对应元素code list  */
  export type layerElements = {
    [key in layerName]?: Array<code>;
  };

  /** 点击返回内容multi 默认为false */
  export type clickParams = {
    layer: layerName;
    code: code;
    multi?: boolean;
  };

  export type toggleLayerParams = {
    layer: layerName;
    visible: boolean;
  };

  /**
   * 传参map元素的颜色
   * @param type
   * @param color 渲染的色值
   * @param codes 需要渲染此色值的codes数组
   */
  export type mapColorParam = {
    type: "cell" | "shelf";
    clear?: boolean;
    color?: color16;
    codes?: Array<code>;
  };

  export type mapSearchFilterType = "FUNCTIONAL_CELL" | "CELL_SIZE_TYPE" | "WAREHOUSE_AREA";
  export type mapSearchFilterParam = {
    type?: MRender.mapSearchFilterType;
    clear?: boolean;
    floorCells?: { [propName: floorId]: mCellData[] };
    areaData?: Array<any>;
  };

  export type mapCellTextParam = {
    [propName: code]: Array<string>;
  };

  export type mapCongestionFilterParam = {
    clear?: boolean;
    floorCells?: { [propName: floorId]: mCellData[] };
  };
}
