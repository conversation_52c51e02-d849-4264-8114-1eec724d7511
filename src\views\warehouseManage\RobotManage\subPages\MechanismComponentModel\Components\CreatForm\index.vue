<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <gp-form
    ref="createForm"
    :model="modelData"
    class="creat-form"
    label-position="right"
    label-width="165px"
    :disabled="!editProp"
    :rules="rules"
  >
    <div class="form-group-title division">{{ $t("lang.rms.api.result.warehouse.baseProperies") }}:</div>
    <!-- <gp-form-item
      :label="$t('lang.rms.api.result.warehouse.mechanismComponentNo')"
      prop="componentCode"
    >
      <gp-input
        v-model="modelData.componentCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMechanismComponentNo')"
      />
    </gp-form-item> -->
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechanism.component.spuName')" prop="name">
      <gp-input
        v-model="modelData.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMechanismComponentSPU')"
      />
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechanismModel')" prop="mechanismModelId">
      <gp-select v-model="modelData.mechanismModelId" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option v-for="item in mechanismList" :key="item.id" :label="item.label" :value="item.mechanismId" />
      </gp-select>
    </gp-form-item>
    <div class="form-group-title division">{{ $t("lang.rms.api.result.warehouse.physicalProperty") }}:</div>
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechanismComponentModelLength')" prop="length">
      <gp-input
        v-model="modelData.length"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentLength')"
        @input="val => handleQueueValue('length', val)"
      />
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechanismComponentModelWidth')" prop="width">
      <gp-input
        v-model="modelData.width"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMechanismCompentModelWidth')"
        @input="val => handleQueueValue('width', val)"
      />
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechanismComponentModelHeight')" prop="beginHeight">
      <gp-input
        v-model="modelData.beginHeight"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentHeight')"
        @input="val => handleQueueValue('beginHeight', val)"
      />
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.api.result.warehouse.mechainsmComponentSn')" prop="sn">
      <gp-input
        v-model="modelData.sn"
        type="number"
        :disabled="!!modelData.id"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterSnOfMechanismComponents')"
        @input="val => handleQueueValue('sn', val)"
      />
    </gp-form-item>
  </gp-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import robotManageRequest from "@/api/robotManage";
import { cloneDeep } from "lodash";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      modelData: { ...this.slopProps },
      mechanismList: [],
      rules: {
        // componentCode: [
        //   {
        //     pattern: /^[a-z0-9]+$/i,
        //     message: this.$t("lang.rms.api.result.warehouse.notEnterSpecialWords"),
        //   },
        // ],
        name: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechanismComponentSPU"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        mechanismModelId: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseSelectMechanismModelID"),
            trigger: "blur",
          },
        ],
        length: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentLength"),
            trigger: "blur",
          },
        ],
        width: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechanismCompentModelWidth"),
            trigger: "blur",
          },
        ],

        beginHeight: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterHeightOfMechanismComponent"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        sn: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterSnOfMechanismComponents"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    slopProps(newObj) {
      this.modelData = { ...newObj };
    },
  },
  mounted() {
    this.getMechanismList();
  },
  // 方法集合
  methods: {
    handleQueueValue(input, value) {
      const reg = value.replace(/[^0-9.]/g, "");
      switch (input) {
        case "length":
          this.modelData.length = Number(value) < 0 ? reg : value;
          break;
        case "width":
          this.modelData.width = Number(value) < 0 ? reg : value;
          break;
        case "beginHeight":
          this.modelData.beginHeight = Number(value) < 0 ? reg : value;
          break;
        case "sn":
          const val = value.replace(/[^0-9]/g, "");
          this.modelData.sn = Number(value) < 0 ? val : value;
          break;
      }
    },
    labelFun(arr) {
      arr.forEach(item => {
        item.label = item.mechanismCode ? item.mechanismCode + "(" + item.name + ")" : "(" + item.name + ")";
        this.mechanismList = cloneDeep(arr);
      });
    },

    getMechanismList() {
      robotManageRequest
        .getMechanismPageList(
          {
            name: "",
            mechanismCode: "",
            controlAbilities: [],
            actionAbilities: [],
          },
          {
            currentPage: 1,
            pageSize: 1000,
          },
        )
        .then(({ data }) => {
          this.labelFun(data.recordList);
        });
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-form .gp-input,
.creat-form .gp-select {
  width: 100%;
}
.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px auto;
  text-align: left;
}
.division {
  margin-bottom: 20px;
}
</style>
