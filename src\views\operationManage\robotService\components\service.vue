<template>
  <section>
    <geek-customize-form ref="refForm" :form-config="formConfig" style="max-width: 600px; margin: 10px auto">
      <template #recycleId="{ config }">
        <gp-select v-model="recycleId" v-bind="config.attr" clearable @change="recycleIdChange">
          <gp-option
            v-for="item in maintainAreas"
            :key="item.recycleId"
            :value="item.recycleId"
            :label="item.recycleId"
          />
        </gp-select>
      </template>
    </geek-customize-form>

    <div class="btn-form">
      <gp-button type="primary" :disabled="curMaintainArea?.isArrivedMaintainArea" @click="goMaintain">
        {{ $t("lang.rms.fed.callRobot") }}
      </gp-button>
    </div>

    <div class="btn-big">
      <gp-button type="primary" :disabled="!curMaintainArea?.isArrivedMaintainArea" @click="moveRobot">
        {{ $t("lang.rms.fed.moveRobot") }}
      </gp-button>
    </div>
  </section>
</template>
<script>
export default {
  name: "RobotServiceTab",
  props: ["maintainAreas"],
  data() {
    return {
      recycleId: "",

      curMaintainArea: null,
    };
  },
  computed: {
    formConfig() {
      return {
        attrs: {
          labelWidth: "202px",
          labelPosition: "right",
        },
        configs: {
          recycleId: {
            label: "lang.rms.fed.serviceArea",
            tag: "select",
            slotName: "recycleId",
            required: true,
          },
          robotInTask: {
            label: "lang.rms.fed.robotInTask",
            default: "",
            placeholder: "--",
            tag: "input",
            disabled: true,
            readonly: true,
          },
          robotId: {
            label: "lang.rms.fed.robot",
            tag: "input",
            default: "",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterTheRobotID"),
                trigger: "blur",
              },
              {
                trigger: "blur",
                validator: (rule, value, callback) => {
                  const reg = new RegExp("^[0-9]*$");
                  if (reg.test(value)) {
                    callback();
                  } else {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  }
                },
              },
            ],
          },
          robotLoading: {
            label: "lang.rms.fed.robotLoading",
            default: "",
            placeholder: "--",
            tag: "input",
            disabled: true,
            readonly: true,
          },
        },
      };
    },
  },

  activated() {
    this.recycleId = "";
    this.$refs.refForm.reset();
  },
  methods: {
    goMaintain() {
      this.$refs.refForm.validate().then(data => {
        const cmd = {
          instruction: "GO_MAINTAIN_TO_STAY",
          recycleId: data.recycleId,
          robotId: data.robotId,
        };

        this.$emit("reqServiceWorker", cmd);
      });
    },

    moveRobot() {
      const robotId = this.curMaintainArea?.robotId || "";
      if (!robotId) return;
      $req.post("/athena/maintain/clearWaitPoint", { robotId });
    },

    recycleIdChange(value) {
      this.recycleId = value;
      this.$refs.refForm.setData({ recycleId: value });
    },

    clearInputRobotId() {
      this.$refs.refForm.setData({ robotId: "" });
    },

    getMaintainPercent() {
      const curMaintainArea = this.maintainAreas.find(item => item.recycleId == this.recycleId);

      const robotId = curMaintainArea?.robotId || "";
      this.curMaintainArea = curMaintainArea;

      if (!robotId) {
        this.$refs.refForm.setData({
          robotInTask: robotId,
          robotLoading: "",
        });
        return;
      }
      $req.get("/athena/maintain/getMaintainProgress", { robotId }).then(res => {
        const p = res?.data?.progress || "--";

        this.$refs.refForm.setData({
          robotInTask: robotId,
          robotLoading: p + "\xa0\xa0\xa0" + "m",
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.btn-form {
  max-width: 600px;
  margin: 10px auto;
  text-align: right;
}
.btn-big {
  margin: 40px 0 0;
  padding: 40px 0;
  border-top: 1px solid #eeeeee;
  text-align: center;
  .gp-button {
    height: 200px;
    width: 200px;
    border-radius: 100px;
    font-size: 22px;
  }
  // .gp-button:active {
  //   background-color: #c00 !important;
  // }
}
</style>
