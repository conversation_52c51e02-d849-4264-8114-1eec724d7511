<!--
* @file 用户列表
!-->
<template>
  <geek-main-structure style="position: relative">
    <iframe ref="iframe" class="auth-iframe" :src="`/athena/auth/index.html#/auth/user?v=${new Date().getTime()}`" />
  </geek-main-structure>
</template>

<script>
export default {
  name: "AutoReSer",
  data() {
    return {
      iframeWin: {},
      offsetX: 0,
      offsetY: 0,
    };
  },
  watch: {
    $route() {
      this.getMeta();
    },
  },
  computed: {
    getStyles() {
      // 控制偏移量，隐藏跨域页面中不期望呈现的内容
      const defaultWidth = "100%";
      const defaultHeight = "100%";
      return {
        width: `calc(${defaultWidth} + ${Math.abs(this.offsetX)}px)`,
        height: `calc(${defaultHeight} + ${Math.abs(this.offsetY)}px)`,
        marginLeft: `${this.offsetX}px`,
        marginTop: `${this.offsetY}px`,
      };
    },
  },
  created() {
    this.getMeta();
  },
  beforeDestory() {
    window.removeEventListener("message", this.handleMessage);
  },
  mounted() {
    window.addEventListener("message", this.handleMessage);
    this.iframeWin = this.$refs.iframe.contentWindow;
  },
  methods: {
    handleMessage(event) {
      const data = event.data;
      console.log("父接收", event);
      this.sendMessage();
      // switch (data.cmd) {
      //   case 'getParentToken':
      //     this.sendMessage();
      //     break;
      //   case 'returnHeight':
      //     break;
      //   default:
      //     break;
      // }
    },
    sendMessage() {
      this.iframeWin.postMessage(
        {
          cmd: "GeekPlus_Message",
          data: {
            eventName: "transLang",
            type: localStorage.getItem("curLanguage"),
          },
        },
        "*",
      );
    },
    getMeta() {
      const route = this.$route;
      if (route.meta.offset) {
        this.setOffset(route);
      }
    },
    setOffset(route) {
      this.offsetX = route.meta.offset.X;
      this.offsetY = route.meta.offset.Y;
    },
  },
};
</script>

<style lang="less" scoped>
.auth-iframe {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0;
  padding: 0;
  user-select: none;
}
</style>
