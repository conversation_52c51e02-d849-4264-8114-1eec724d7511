<template>
  <section class="panel-wrap">
    <div class="panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        @row-add="rowAdd"
        @row-del="rowDel"
        @row-export="rowExport"
        @row-application="rowApply"
      />
    </div>
    <editDialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>

<script>
import editDialog from "./editDialog";
export default {
  name: "LanguageList",
  components: { editDialog },
  props: {
    tableData: Array,
  },
  data() {
    const permission = !this.isRoleGuest();
    return {
      tableConfig: {
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.fed.languageName", prop: "languageName" }, // "语言名称"
          { label: "lang.rms.fed.languageField", prop: "languageCode" }, // "语言字段"
          { label: "lang.rms.fed.uploadTime", prop: "createTime", width: "240" },
          { label: "lang.rms.fed.updateTime", prop: "updateTime", width: "240" },
          {
            label: "lang.rms.fed.listOperation",
            width: "200",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonExport",
                handler: "row-export",
              },
              {
                label: "lang.rms.fed.application",
                permission,
                handler: "row-application",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
                type: "danger",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.$emit("getTableList");
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open();
    },
    rowDel(rowData) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req
          .postParams("/athena/api/coreresource/i18n/deleteLanguage", {
            languageCode: rowData.languageCode,
          })
          .then(res => {
            if (res.code === 0) {
              this.$success();
              this.$emit("getTableList");
            }
          });
      });
    },

    // 应用
    rowApply(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/applyLanguage", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            this.$success();
            $utils.Data.setLocalLang(rowData.languageCode);
            this.$emit("getTableList");
          }
        });
    },

    // 导出
    rowExport(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/exportI18nItem", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            if (window.location.host == "127.0.0.1" || window.location.host == "localhost") {
              window.open("http://" + process.env.VUE_APP_serverIP + res.data);
            } else {
              window.open(window.location.origin + res.data);
            }
          }
        });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped>
.panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);
  .panel-wrap__table {
    flex: 1;
  }
}
</style>
