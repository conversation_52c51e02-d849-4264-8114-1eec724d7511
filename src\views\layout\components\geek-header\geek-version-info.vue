<template>
  <div class="">
    <gp-dropdown trigger="hover" class="geek-version-info">
      <!-- <div class="gp-header__menu-item">
        <img src="@imgs/common/icon-version.png" width="21" />
      </div> -->
      <div class="gp-header__menu-item verion-wrapper">
        <gp-icon :size="16" style="--gp-color: #fff">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13.3337 7.66665V4.66665L10.3337 1.33331H3.33373C2.96554 1.33331 2.66707 1.63179 2.66707 1.99998V14C2.66707 14.3682 2.96554 14.6666 3.33373 14.6666H7.33373"
              stroke-width="1.33333"
              stroke="currentColor"
            />
            <path d="M12.3329 10.3333L13.9996 12L12.3329 13.6666" stroke="currentColor" stroke-width="1.33333" />
            <path d="M10.3337 10.3333L8.66707 12L10.3337 13.6666" stroke="currentColor" stroke-width="1.33333" />
            <path d="M10 1.33334V4.66668H13.3333" stroke="currentColor" stroke-width="1.33333" />
          </svg>
        </gp-icon>
      </div>
      <gp-dropdown-menu slot="dropdown">
        <div class="version-info">
          <strong>{{ $t("lang.rms.fed.front.end") }}</strong>
          <div>{{ $t("lang.rms.fed.rms.version") }} : {{ versionInfoStatic.tag || "--" }}</div>
          <div>{{ $t("lang.rms.fed.packing.date") }} : {{ versionInfoStatic.date || "--" }}</div>
          <div>CommitID : {{ versionInfoStatic.commitId || "--" }}</div>
          <strong>{{ $t("lang.rms.fed.rear.end") }}</strong>
          <div>
            {{ $t("lang.rms.fed.rms.version") }} :
            {{ versionInfoApi.tag || versionInfoApi.rmsVersion || "--" }}
          </div>
          <div>{{ $t("lang.rms.fed.packing.date") }} : {{ versionInfoApi.packagingDate || "--" }}</div>
          <div>CommitID: {{ versionInfoApi.commitId || "--" }}</div>
        </div>
      </gp-dropdown-menu>
    </gp-dropdown>
  </div>
</template>

<script>
export default {
  name: "GeekVersionInfo",
  data() {
    return {
      versionInfoStatic: {},
      versionInfoApi: {},
    };
  },
  created() {
    this.getVersionInfoStatic();
    this.getGitProperties();
  },
  methods: {
    getVersionInfoStatic() {
      $req.reqGitVersionConfig().then(res => {
        this.versionInfoStatic = res;
      });
    },
    getGitProperties() {
      $req.get("/athena/git/properties/get").then(res => {
        this.versionInfoApi = res.data;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.geek-version-info {
  cursor: pointer;
  margin-left: 8px !important;
  margin-right: 0px !important;
  padding: 1px 0;
  .gp-header__menu-item {
    margin: 0px;
    // padding: 1px 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .avatar-wrapper {
    .g-flex();
    line-height: 46px;
    min-width: 20px;
    justify-content: flex-end;
    font-size: 13px;
    color: @g-nav-right-color;
  }
}

.version-info {
  padding: 0 10px;
}
.verion-wrapper {
  height: 32px;
  line-height: 32px;
}
</style>
