<template>
  <div>
    <div class="alertBox">
      <gp-icon name="gp-icon-warning" />
      <span class="alert__title">{{ $t("lang.rms.fed.parNotTakeEffectImmediateToRestart") }}</span>
      <gp-button type="text" class="checkBtn" @click="checkOn()">{{ $t("lang.rms.fed.buttonView") }}</gp-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "WorkspaceJsonRestartAlert",

  data() {
    return {};
  },

  mounted() {},

  methods: {
    checkOn() {
      this.$emit("parameterIsShow", false);
    },
  },
};
</script>

<style lang="less" scoped>
.alertBox {
  width: 100%;
  height: 36px;
  background: #fffbe6;
  margin: 10px 0;
}
.alert__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  color: #313234;
  margin-right: 6px;
}
/deep/.gp-icon-warning {
  color: #f0ba50;
  margin-left: 16px;
  margin-right: 6px;
  line-height: 36px;
  width: 16px;
}
</style>
