<template>
  <geek-main-structure class="operate-log-panel-wrap">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <div class="operate-log-panel-wrap__table" style="padding-top: 16px">
      <geek-customize-table :table-config="tableConfig" :data="tableData" :page="tablePage" @page-change="pageChange">
        <template #request="{ row }">
          <gp-tooltip v-if="row.request" placement="left">
            <template #content>
              <pre><code  v-text="formatterJson(row.request)"></code></pre>
            </template>
            <gp-link type="primary" :underline="false">{{ row.request }}</gp-link>
          </gp-tooltip>
        </template>
        <template #response="{ row }">
          <gp-tooltip v-if="row.response" placement="left">
            <template #content>
              <pre><code v-text="formatterJson(row.response)"></code></pre>
            </template>
            <gp-link type="primary" :underline="false">{{ row.response }}</gp-link>
          </gp-tooltip>
        </template>
      </geek-customize-table>
    </div>
  </geek-main-structure>
</template>

<script>
export default {
  data() {
    return {
      form: {
        source: "",
        module: "",
        clientIp: "",
        operator: "",
        dataRange: "",
        request: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          source: {
            label: "lang.rms.fed.fetchType",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              { value: "WEB", label: "WEB" },
              { value: "WEBSOCKET", label: "WEBSOCKET" },
            ],
          },
          clientIp: {
            label: "lang.rms.fed.customIP",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          operator: {
            label: "lang.rms.fed.operator",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          request: {
            label: "lang.rms.fed.reqParams",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          dataRange: {
            label: "lang.rms.fed.operateTime",
            default: "",
            valueFormat: "yyyy-MM-dd HH:ss:mm",
            type: "datetimerange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        columns: [
          { label: "lang.rms.fed.fetchType", prop: "source", width: "100" },
          {
            label: "lang.rms.fed.operateModule",
            prop: "module",
            width: "160",
            formatter: (row, column) => {
              return this.$t(row[column]);
            },
          },
          { label: "lang.rms.fed.operator", prop: "operator", width: "100" },
          { label: "lang.rms.fed.operateTime", prop: "createTime", width: "160" },
          { label: "lang.rms.fed.customIP", prop: "clientIp", width: "120" },
          { label: "lang.rms.fed.fetchApi", prop: "operatorType", width: "180" },
          {
            label: "lang.rms.fed.reqParams",
            prop: "request",
            className: "cell-json",
            slotName: "request",
          },
          {
            label: "lang.rms.fed.resParams",
            prop: "response",
            className: "cell-json",
            slotName: "response",
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      const { currentPage, pageSize } = this.tablePage;
      const { source, clientIp, operator, request, dataRange } = this.form;
      let temporaryTime = dataRange ? dataRange.map(item => new Date(item).getTime()) : "";
      const data = {
        source,
        request,
        clientIp,
        operator,
        startTime: temporaryTime && temporaryTime[0] ? temporaryTime[0] : "",
        endTime: temporaryTime && temporaryTime[1] ? temporaryTime[1] : "",
      };
      $req.post(`/athena/operatorlog/findAll?currentPage=${currentPage}&pageSize=${pageSize}`, data).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList;
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          total: result.recordCount || 0,
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    formatterJson(data) {
      let obj = data;
      try {
        // 防止不标准格式JSON
        obj = JSON.parse(data);
      } catch (e) {
        console.log(e);
      }
      return Array.isArray(obj) ? data : JSON.stringify(obj, null, 2);
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.cell-json .cell) {
  display: block;
  width: 100%;
  max-width: 200px;
  a {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
<style lang="less">
.operate-log-panel-wrap {
  display: flex;
  flex-direction: column;
  // height: calc(100% -20px);
  .operate-log-panel-wrap__table {
    flex: 1;
  }
}
</style>
