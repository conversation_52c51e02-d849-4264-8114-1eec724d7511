/* ! <AUTHOR> at 2023/04/22 */
import <PERSON><PERSON>esh from "./mesh-cell";
import She<PERSON><PERSON>esh from "./mesh-shelf";
import Rack<PERSON>esh from "./mesh-rack";
import PoppickMesh from "./mesh-poppick";
import XShelfMesh from "./mesh-shelf-x";

class MeshData implements MRender.MeshDataMain {
  cell: CellMesh = new CellMesh();
  shelf: ShelfMesh = new ShelfMesh();
  rack: RackMesh = new RackMesh();
  poppick: PoppickMesh = new PoppickMesh();
  xShelf: XShelfMesh = new XShelfMesh();

  uninstall() {
    this.cell.uninstall();
    this.shelf.uninstall();
    this.rack.uninstall();
    this.poppick.uninstall();
    this.xShelf.uninstall();
  }

  destroy() {
    this.cell.destroy();
    this.shelf.destroy();
    this.rack.destroy();
    this.poppick.destroy();
    this.xShelf.destroy();
  }
}

export default MeshData;
