/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

/** cell颜色 */
class LayerCellText {
  private floorId: floorId;
  private mapCore: any;
  private container: PIXI.Container;

  private meshList: Array<any> = [];
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "cellFeature";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(cells: Array<mCellData>, params: MRender.mapCellTextParam): void {
    const _this = this;
    const utils = _this.mapCore.utils;

    let cell;
    for (let i = 0, len = cells.length; i < len; i++) {
      cell = cells[i];
      const text = _this.drawText(cell, params[cell.code]);
      _this.meshList.push(text);
      _this.container.addChild(text);
    }
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.mapCore = null;
    this.floorId = undefined;
  }

  private drawText(cell: mCellData, texts: Array<string>) {
    // console.log("1", cell, texts);

    //     const basicText = new PIXI.Text('Basic text in pixi');

    // basicText.x = 50;
    // basicText.y = 100;

    //     app.stage.addChild(basicText);

    // const { val, x, y } = params;
    const style = new PIXI.TextStyle({
      fontFamily: "Arial",
      fontSize: 36,
      fontStyle: "italic",
      fill: "#ffffff",
    });

    const richText = new PIXI.Text(texts.join("\n"), style);
    richText.width = 1;
    richText.height = 1;
    richText.position.set(cell.hitArea[0], cell.hitArea[1]);
    richText.x = cell.hitArea[0];
    richText.y = cell.hitArea[1];

    // console.log("3", richText);
    return richText;
  }
}
export default LayerCellText;
