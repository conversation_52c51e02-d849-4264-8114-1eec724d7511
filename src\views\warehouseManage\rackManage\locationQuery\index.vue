<template>
  <section class="rack-manage-panel-wrap">
    <geek-customize-form :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset">
      <template #robotId="{}">
        <div>
          <span class="robot-label">{{ $t("lang.rms.fed.inputRobotId") }}</span>
          <gp-tooltip
            class="item"
            effect="dark"
            :content="$t('lang.rms.fed.search.lockLatticeOfRobotException')"
            placement="top-start"
          >
            <gp-icon name="gp-icon-info" style="color: #409eff" />
          </gp-tooltip>
        </div>
        <gp-input
          v-model="robotId"
          type="number"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
          clearable
          onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
        />
      </template>
    </geek-customize-form>
    <div class="rack-manage-panel-wrap__handle">
      <gp-button type="primary" :disabled="lockArr.length === 0" @click="onUnlock">{{ $t("lang.rms.fed.UnlockLattice") }}</gp-button>
    </div>
    <div class="rack-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @selectionChange="handleSelection"
        @page-change="pageChange"
      >
      </geek-customize-table>
    </div>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>

<script>
// 为了避免 不同地方时间显示格式不同 统一处理 已考虑当地时区
const formatter = new Intl.DateTimeFormat("en-US", { year: "numeric", month: "2-digit", day: "2-digit" });
const parts = formatter.formatToParts(new Date());
const dateString = `${parts.find(p => p.type === "month").value}/${parts.find(p => p.type === "day").value}/${
  parts.find(p => p.type === "year").value
}`;
const defaultData = new Date(dateString);

import EditDialog from "./components/editDialog";

export default {
  name: "LocationQueryIndex",
  components: { EditDialog },
  data() {
    return {
      dayTime: 24 * 60 * 60 * 1000 - 1000, // 到当前0点
      boxStatusObj: {
        NORMAL: "lang.rms.fed.normal",
        LOCKED: "lang.rms.fed.lock",
      },
      boxSourceObj: {
        ROBOT_EXCEPTION: "lang.configs.robot.exception",
        BROWSER: "lang.configs.fed.browser",
      },
      robotId: "",
      // 搜索条件
      form: {
        latticeCode: "",
        boxCode: "",
        latticeFlag: "",
        errorSource: "",
        robotId: "",
        updateTime: [defaultData, defaultData],
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          latticeCode: {
            label: "lang.rms.fed.latticeCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          boxCode: {
            label: "lang.rms.fed.boxCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          latticeFlag: {
            label: "lang.rms.fed.latticeStatus",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              // {
              //   value: 0,
              //   label: "lang.rms.fed.no",
              // },
              // {
              //   value: 1,
              //   label: "lang.rms.fed.yes",
              // },
            ],
          },
          // errorSource: {
          //   label: "lang.rms.fed.lockSource",
          //   default: "",
          //   tag: "select",
          //   placeholder: "lang.rms.fed.pleaseChoose",
          //   options: [
          //     {
          //       value: "",
          //       label: "lang.rms.fed.wholeStatus",
          //     },
          //     // {
          //     //   value: 0,
          //     //   label: "lang.rms.fed.no",
          //     // },
          //     // {
          //     //   value: 1,
          //     //   label: "lang.rms.fed.yes",
          //     // },
          //   ],
          // },
          robotId: {
            label: "",
            default: "",
            tag: "input",
            slotName: "robotId",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          // 更新时间
          // updateTime: {
          //   label: "lang.rms.fed.updateTime",
          //   default: [defaultData, defaultData],
          //   valueFormat: "yyyy-MM-dd",
          //   type: "daterange",
          //   tag: "date-picker",
          //   "range-separator": "-",
          //   "start-placeholder": "lang.rms.fed.startTime",
          //   "end-placeholder": "lang.rms.fed.endTime",
          // },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
          // {
          //   label: "lang.rms.fed.UnlockLattice",
          //   disabled: true,
          //   handler: "on-unlock",
          //   type: "success",
          // },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
          index: true,
          "row-key": "latticeCode",
          "reserve-selection": true,
        },
        columns: [
          {
            label: "lang.rms.fed.latticeCode",
            prop: "latticeCode",
          },
          {
            label: "lang.rms.box.boxCode",
            prop: "boxCode",
          },
          {
            label: "lang.rms.fed.latticeStatus",
            prop: "latticeFlag",
            formatter: (row, column) => {
              return this.$t(this.boxStatusObj[row[column]]);
            },
          },
          { label: "lang.rms.fed.layer", prop: "layer" },
          { label: "lang.rms.fed.beginHeight", prop: "height" },
          {
            label: "lang.rms.fed.errorCode",
            prop: "errorCodes",
            formatter: (row, column) => {
              if (row[column].length === 0) return "--";
              return row[column].join("、");
            },
          },
          {
            label: "lang.rms.fed.lockReason",
            prop: "errorReasons",
            formatter: (row, column) => {
              if (row[column].length === 0) return "--";
              let arr = [];
              row[column].forEach(item => {
                arr.push(this.$t(item));
              });
              return arr.join("、");
            },
          },
          {
            label: "lang.rms.fed.lockRobot",
            prop: "robotIds",
            formatter: (row, column) => {
              if (row[column].length === 0) return "--";
              return row[column].join("、");
            },
          },
          {
            label: "lang.rms.fed.lockSource",
            prop: "errorSource",
            formatter: (row, column) => {
              if (!row[column]) return "--";
              return this.$t(this.boxSourceObj[row[column]]);
            },
          },
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
            width: "160",
            formatter: (row, column) => {
              if (!row[column]) return "--";
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
        ],
      },
      selectList: [], // 可进行一键还箱
    };
  },
  computed: {
    lockArr() {
      let arr = [];
      this.selectList.forEach(item => {
        if (item.latticeFlag === "LOCKED") {
          if (item.robotIds?.length) {
            if (this.form.robotId) {
              arr.push({
                latticeCode: item.latticeCode,
                errorSource: item.errorSource,
                robotId: this.form.robotId,
              });
            } else {
              item.robotIds.forEach(cItem => {
                arr.push({
                  latticeCode: item.latticeCode,
                  errorSource: item.errorSource,
                  robotId: cItem,
                });
              });
            }
          } else {
            arr.push({
              latticeCode: item.latticeCode,
              errorSource: item.errorSource,
              robotId: null,
            });
          }
        }
      });
      return arr;
    },
  },
  // watch: {
  //   lockArr(v) {
  //     console.log(v);
  //     let formConfig = this.formConfig;
  //     formConfig.operations[2].disabled = v.length === 0;
  //     this.formConfig = Object.assign({}, formConfig);
  //   },
  // },
  activated() {
    this.getTableList();
    this.getBoxStatus();
    // this.getLockSource();
  },
  methods: {
    itemSave(row) {
      $req.post("/athena/box/confirmBoxStatus", { boxCode: row.boxCode }).then(res => {
        if (res.code === 0) {
          this.$success(this.$t("lang.common.success"));
          this.getTableList();
        }
      });
    },
    itemChange(row) {
      this.$refs.editDialog.open(row);
    },
    handleSelection(selection) {
      this.selectList = selection;
    },
    // 解锁货位
    async onUnlock() {
      if (this.lockArr && this.lockArr.length) {
        const { code, msg } = await $req.post("/athena/box/batchUnlockLattice	", {
          batchUnlockLatticeInfo: this.lockArr,
        });
        if (!code) {
          this.$message.success(this.$t("lang.common.success"));
          this.tablePage.currentPage = 1;
          this.getTableList();
        }
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.form.robotId = this.robotId;
      if (this.form.robotId) {
        this.form.robotId = Number(this.form.robotId);
        this.form.latticeFlag = "LOCKED";
      }
      this.getTableList();
    },
    onReset(val) {
      this.robotId = "";
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getBoxStatus() {
      $req.get("/athena/box/querySearchConditionValue").then(res => {
        let list = res.data.latticeFlag || [];
        this.formConfig.configs.latticeFlag.options = list.map(item => {
          return {
            value: item,
            label: this.$t(this.boxStatusObj[item]),
          };
        });
      });
    },
    getLockSource() {
      $req.get("/athena/box/queryLockSourceValue").then(res => {
        let list = res.data.lockSource || [];
        list = list.slice(0, 2);
        this.formConfig.configs.errorSource.options = list.map(item => {
          return {
            value: item,
            label: this.$t(this.boxSourceObj[item]),
          };
        });
      });
    },
    getTableList() {
      // const { updateTime } = this.form;
      // let temporaryUpdateTime = updateTime ? updateTime.map(item => new Date(item).getTime()) : "";
      // if (temporaryUpdateTime && temporaryUpdateTime[1]) temporaryUpdateTime[1] += this.dayTime;
      const params = {
        robotId: this.form.robotId || null,
        latticeCode: this.form.latticeCode || null,
        boxCode: this.form.boxCode || null,
        latticeFlag: this.form.latticeFlag || null,
        // errorSource: this.form.errorSource || null,
        // updateTime: temporaryUpdateTime ? temporaryUpdateTime.join("-") : null,
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      $req.post("/athena/box/listLatticeInfo", params).then(res => {
        let result = res.data;
        if (!result) {
          this.tableData = [];
          return;
        }
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          total: result.recordCount || 0,
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.robot-label {
  font-weight: 800;
  color: #606266;
  font-size: 13px;
}
</style>
