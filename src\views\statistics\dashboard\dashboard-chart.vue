<template>
  <div class="dashboard-chart" :style="curChartStyle" v-loading="charLoading">
    <!-- loading -->
    <span class="updateLoading gp-icon-loading" v-if="updateChartLoading">
      <gp-icon><gp-icon-loading /></gp-icon>
    </span>
    <!-- filter -->
    <gp-popover v-if="option.filterList.length" placement="bottom-end" trigger="click" v-model="filterVisible">
      <FilterItem :filterData="option.filterList" @query="queryFilter" />
      <span slot="reference" class="filterIcon">
        <gp-icon><gp-icon-setting /></gp-icon>
      </span>
    </gp-popover>

    <!-- 图表异常 -->
    <div class="emptyBox" v-if="chartError">
      <gp-empty class="empty" :image-size="30"></gp-empty>
    </div>

    <template v-if="chartType === 'cred'">
      <number-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'table'">
      <table-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'annular'">
      <annular-item :option="chartData" />
    </template>
    <!-- 图表组 -->
    <template v-else-if="chartType === 'group'">
      <chart-group :option="chartData" />
    </template>
    <template v-else>
      <ChartItem :option="chartData" :isFilter="option.isChartFilter" @filter="filter" />
    </template>
  </div>
</template>

<script>
import NumberItem from "../components/common/number-item.vue";
import ChartGroup from "../components/common/chart-group.vue";
import ChartItem from "../components/common/chart-item.vue";
import TableItem from "../components/common/table-item.vue";
import AnnularItem from "../components/common/chart-annular.vue";
import FilterItem from "../components/common/filter-item.vue";
export default {
  name: "dashboard",
  props: {
    option: {
      type: Object,
      default: () => {},
    },
    gridWidth: {
      type: Number,
      default: 100,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
  },
  components: { NumberItem, ChartItem, TableItem, AnnularItem, FilterItem, ChartGroup },
  data() {
    return {
      isDataLoad: true,
      chartData: null,
      pollingTimer: null,
      curFilterData: {},
      charLoading: false,
      updateChartLoading: false,
      filterVisible: false,
      chartError: false,
    };
  },
  computed: {
    chartType() {
      return this.option?.type || "";
    },
    curChartStyle() {
      const { width, height } = this.option;
      const { gridWidth } = this;
      let curWidth = width;
      let curHeight = height;
      if (typeof width === "number") curWidth = `${parseInt(width * gridWidth) - 1}px`;
      if (typeof height === "number") curHeight = `${parseInt(height * gridWidth)}px`;
      return { width: curWidth, height: curHeight };
    },
  },
  watch: {},
  async created() {
    await this.option.preDataReady();
    await this.filter();
  },

  destroyed() {
    clearInterval(this.pollingTimer);
  },

  methods: {
    async queryGlobalFilter(option) {
      this.filterVisible = false;
      await this.filter({ ...this.curFilterData, ...option });
    },

    async queryFilter(option) {
      this.filterVisible = false;
      await this.filter(option);
    },

    async filter(filterData) {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }

      this.curFilterData = filterData || {};
      const { option } = this;

      if (!this.chartData) {
        this.charLoading = true;
      } else {
        this.updateChartLoading = true;
      }

      let data;
      try {
        data = await option.request(await option.requestChartData(this.curFilterData));
        this.chartError = false;
        this.charLoading = false;
        this.updateChartLoading = false;
        this.chartData = await option.handler(data);
      } catch (error) {
        console.log("图表异常", error);
        this.chartError = true;
        this.charLoading = false;
        this.updateChartLoading = false;
      }

      if (option.intervalTimer) {
        this.pollingTimer = setTimeout(() => {
          this.filter(this.curFilterData);
        }, option.intervalTimer);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dashboard-chart {
  display: inline-block;
  overflow: hidden;
  padding: 5px;
  position: relative;

  .updateLoading {
    position: absolute;
    right: 25px;
    top: 8px;
    font-size: 13px;
    z-index: 5;
  }

  .filterIcon {
    position: absolute;
    right: 8px;
    font-size: 14px;
    top: 8px;
    color: #666;
    cursor: pointer;
    z-index: 5;
  }

  .emptyBox {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 5px;
    box-sizing: border-box;
    z-index: 1;
    background: #fff;
  }

  .empty {
    width: 100%;
    height: 100%;
    border: 0.01rem solid #eee;
    border-radius: 0.03rem;
    box-shadow: 0 0.02rem 0.12rem 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }
}
</style>

<style lang="less">
.dashboard-chart {
  .content-container {
    overflow: hidden;
    padding: 5px;
  }

  &.vdr.active:before {
    outline: none;
  }
}
</style>
