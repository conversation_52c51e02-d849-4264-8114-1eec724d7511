/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";
import LayerDmpDevice from "./device-dmp";
import LayerXDevice from "./device-x";

class LayerDevice implements MRender.Layer {
  floorId: floorId;
  layerDmpDevice: LayerDmpDevice = null;
  layerXDevice: LayerXDevice = null;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.layerDmpDevice = new LayerDmpDevice(mapCore, floor);
    this.layerXDevice = new LayerXDevice(mapCore, floor);
    this.init();
  }

  render(arr: Array<deviceData>): void {
    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, options, device;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatDevice(item);
      device = _this.drawSprite(options);

      container.addChild(device);
      mapData.device.setData(options.code, { element: device, options });
    }
  }

  update(arr: Array<deviceData>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, code, options, device;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatDevice(item);
      code = options["code"];
      device = mapData.device.getData(code);
      if (device) {
        mapData.device.setData(code, Object.assign(device, { options }));
        _this.updateSprite(device);
      } else {
        device = _this.drawSprite(options);
        _this.container.addChild(device);
        mapData.device.setData(code, { element: device, options });
      }
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
    this.layerXDevice.triggerLayer(isTrigger);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }
  repaint(): void {
    this.layerDmpDevice.repaint();
    this.layerXDevice.repaint();
    console.log("device repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.layerDmpDevice.destroy();
    this.layerXDevice.destroy();
    this.layerDmpDevice = null;
    this.layerXDevice = null;
    this.mapCore = null;
    this.container = null;
    this.floor = null;
  }

  init(): void {
    const utils = this.mapCore.utils;

    let container = new PIXI.Container();
    container.name = "device";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("device");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  private drawSprite(options: mDeviceData) {
    const { code, width, height, position, iconTexture } = options;
    let sprite: any = new PIXI.Sprite(iconTexture);
    sprite.mapType = "device";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = true;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中
    return sprite;
  }

  private updateSprite(device: { element: any; options: mDeviceData }) {
    const { element, options } = device;

    element.texture = options.iconTexture;
    element.position.set(options.position.x, options.position.y); // 使图片居中
  }
}
export default LayerDevice;
