<template>
  <div>
    <!-- 复制 -->
    <gp-link :disabled="btnDisabled" type="primary" :underline="false" @click="copy"
      >{{ $t("lang.rms.web.map.version.copy") }}
    </gp-link>
    <!-- 创建楼层 -->
    <gp-link
      v-if="[4, 5, 6].includes(rowData.status)"
      :disabled="btnDisabled"
      type="primary"
      :underline="false"
      @click="createFloor"
    >
      {{ $t("lang.rms.fed.createAFloor") }}
    </gp-link>
    <!-- 应用 -->
    <gp-link
      v-if="[1, 3, 4, 5, 6].includes(rowData.status)"
      :disabled="btnDisabled"
      type="primary"
      :underline="false"
      @click="application"
    >
      {{ $t("lang.rms.fed.application") }}
    </gp-link>
    <!-- 导出 -->
    <!-- <gp-button
      type="text"
      :disabled="btnDisabled"
      @click="exportMap"
    >
      {{ $t("lang.rms.fed.buttonExport") }}
    </gp-button> -->
    <!-- 封板 -->
    <gp-link
      v-if="[4, 5, 6].includes(rowData.status)"
      :disabled="btnDisabled"
      type="primary"
      :underline="false"
      @click="issue"
    >
      {{ $t("lang.rms.web.map.version.release") }}
    </gp-link>
    <!-- 删除地图 -->
    <gp-link v-if="[1, 4].includes(rowData.status)" type="danger" :underline="false" @click="deleteMap">
      {{ $t("lang.rms.fed.deleteAMap") }}
    </gp-link>
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  name: "MainTableOperating",
  data() {
    return {
      btnDisabled: false,
    };
  },
  props: {
    rowData: {
      type: Object,
      require: true,
    },
  },
  watch: {
    rowData: {
      handler(data) {
        const { uploadStatus } = data;
        if (uploadStatus === undefined) {
          this.btnDisabled = false;
          return;
        }
        this.btnDisabled = uploadStatus !== 2;
      },
      deep: true,
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["showDialog"]),
    // 复制
    copy() {
      this.showDialog({
        currentComponent: "dialogMapAddCopy",
        title: this.$t("lang.rms.web.map.version.copyMap"),
        rowData: this.rowData,
      });
    },
    // 创建楼层
    createFloor() {
      this.showDialog({
        currentComponent: "dialogFloorCreate",
        title: this.$t("lang.rms.fed.createAFloor"),
        rowData: this.rowData,
      });
    },
    // 应用
    async application() {
      // true:不存在未确认的二维码 ， false: 却在未确认的二维码
      const { data } = await $req.get("/athena/map/version/checkActive", {
        mapId: this.rowData.id,
      });
      if (!data) {
        await this.$confirm(
          this.$t("lang.rms.api.result.edit.map.active.unconfirmQrCodeDetail"),
          this.$t("lang.rms.api.result.edit.map.active.unconfirmQrCodeTitle"),
          { type: "warning" },
        );
      }
      this.showDialog({
        currentComponent: "dialogApplicationMap",
        title: "lang.rms.web.map.version.mapApplication",
        rowData: this.rowData,
      });
    },
    // 导出
    exportMap() {
      $req
        .postParams(
          "/athena/map/version/exportMap",
          {
            mapId: this.rowData.id,
          },
          { headers: { "content-Type": "text/plain" } },
        )
        .then(res => {
          const data = res.data;
          const url = data.url ? data.url : data;
          if ($req.isDev) window.open($req.API_URL + url);
          else window.open(window.location.origin + url);
        });
    },
    // 封板
    issue() {
      this.showDialog({
        currentComponent: "dialogIssueMap",
        title: this.$t("lang.rms.web.map.version.mapPublishing"),
        rowData: this.rowData,
      });
    },
    // 删除地图
    deleteMap() {
      this.$geekConfirm(this.$t("lang.rms.fed.whetherOrNotToDeleteMap")).then(() => {
        this.$emit("deleting", true);
        $req
          .postParams("/athena/map/version/delete", {
            mapId: this.rowData.id,
          })
          .then(res => {
            this.$emit("deleting", false);
            this.$success(this.$t(res.msg));
            this.$emit("refreshList");
          })
          .catch(err => {
            this.$emit("deleting", false);
          });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
