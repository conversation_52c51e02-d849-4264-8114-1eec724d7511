<template>
  <div class="map-manage-qrcode">
    <gp-dialog
      width="80%"
      :visible="visible"
      :close-on-click-modal="false"
      :footer="false"
      class="geek-dialog-form"
      @close="handleDialogClosed"
    >
      <template #title>
        <span class="f16">{{ title }}</span>
      </template>
      <div class="map-manage-qrcode__form">
        <div class="select">
          <span class="select_label">{{ $t("lang.rms.fed.qrcodeStatus") }}</span>
          <gp-select v-model="selectQrStatus" @change="selectChange">
            <gp-option v-for="item in qrcodeStatusOption" :key="item.value" :label="$t(item.label)" :value="item.value" />
          </gp-select>
        </div>
        <div class="btnBox">
          <gp-button type="primary" @click="handleSearch">{{ $t("lang.rms.fed.query") }}</gp-button>
          <gp-button type="primary" :disabled="lockBtn" @click="handleConfirmAll">
            {{ $t("lang.rms.fed.allConfirm") }}
          </gp-button>
        </div>
      </div>
      <div v-loading="tableLoading" class="map-manage-qrcode__table">
        <geek-customize-table :table-config="tableConfig" :data="tableData" :page="tablePage" @page-change="pageChange">
          <template #operations="{ row }">
            <gp-button
              v-if="['0', '2'].includes(String(row.status))"
              type="text"
              @click="handleManualConfirm(row)"
              >{{ $t("lang.rms.fed.manualConfirm") }}</gp-button
            >
          </template>
        </geek-customize-table>
      </div>
    </gp-dialog>
  </div>
</template>
<script>
/**
 *  国际化：
 *  lang.rms.fed.qrcodeStatus  二维码状态
 *  lang.rms.fed.allConfirm    全部确认
 *  lang.rms.fed.manualConfirm 手工确认
 *  lang.rms.fed.confirmAgain  待二次确认
 *  lang.rms.fed.oldQrCode     原二维码
 *  lang.rms.fed.newQrCode     新二维码
 *  lang.rms.fed.realLocation  实际扫描坐标
 *  lang.rms.fed.manageQrCode  管理二维码
 *  lang.rms.fed.actualScanningAngle  实际扫描角度
 *  lang.rms.fed.errorReportingReason  报错原因
 *  lang.rms.fed.confirmQrCode  确认二维码值
 *
 */
export default {
  props: {
    visible: Boolean,
    mapId: String,
    floorId: String,
  },
  data() {
    return {
      lockBtn: true,
      tableLoading: false,
      title: this.$t("lang.rms.fed.manageQrCode"),
      selectQrStatus: "",
      tableData: [],
      qrcodeStatusObj: {},
      qrcodeStatusOption: [],
      tableExtendConfig: {
        sortNum: false,
        operate: [
          {
            event: "manualConfirm",
            label: this.$t("lang.rms.fed.manualConfirm"),
            condition({ row }) {
              return ["0", "2"].includes(String(row.status));
            },
          },
        ],
      },
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        columns: [
          { prop: "oldQrCode", width: "100", label: this.$t("lang.rms.fed.oldQrCode") },
          { prop: "qrCode", width: "100", label: this.$t("lang.rms.fed.newQrCode") },
          {
            prop: "status",
            width: "120",
            label: this.$t("lang.rms.fed.state"),
            formatter: (row, column) => {
              return this.$t(this.qrcodeStatusObj[row[column]]);
            },
          },
          {
            prop: "errorReportingReason",
            label: this.$t("lang.rms.fed.errorReportingReason"),
            formatter: (row, column) => {
              return row[column] ? this.$t(row[column]) : "";
            },
          },
          {
            prop: "coordinate",
            width: "200",
            label: this.$t("lang.rms.fed.coordinates"),
          },
          { prop: "realLocation", label: this.$t("lang.rms.fed.realLocation") },
          {
            prop: "actualScanningAngle",
            width: "140",
            label: this.$t("lang.rms.fed.actualScanningAngle"),
            formatter: (row, column) => {
              return row[column] ? `${row[column]}°` : "";
            },
          },
          {
            prop: "updateTime",
            width: "160",
            label: this.$t("lang.rms.map.qrcode.scanTime"),
            formatter: (row, column) => {
              if (!row[column]) {
                return null;
              }
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "100",
            fixed: "right",
            slotName: "operations",
          },
        ],
      },
    };
  },
  created() {
    this.getStatusDict();
    this.fetchQrInfo();
  },
  methods: {
    // 查询
    handleSearch() {
      this.tablePage.currentPage = 1;
      this.fetchQrInfo();
    },
    getStatusDict() {
      $req.post("/athena/dict/queryList", {
        dictCode: "QRCODE_STATUS"
      }).then(res => {
        const data = res.data || [];
        this.qrcodeStatusOption = data.map(item => {
          this.qrcodeStatusObj[item.dictKey] = item.dictValue;
          return { label: item.dictValue, value: item.dictKey };
        });
        this.qrcodeStatusOption.unshift({ label: "lang.rms.fed.buttonWhole", value: "" });
      })
    },
    // 查询接口
    async fetchQrInfo() {
      this.tableLoading = true;
      let params = {
        mapId: this.mapId,
        floorId: this.floorId,
        status: this.selectQrStatus,
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      const { data } = await $req.get("/athena/map/qrLog/findQrInfo", params);
      this.tableData = data.recordList || [];
      this.tablePage = Object.assign({}, this.tablePage, {
        currentPage: data.currentPage || 1,
        total: data.recordCount || 0,
      });
      this.lockBtn = false;
      this.tableLoading = false;
    },
    pageChange(page) {
      this.tablePage = page;
      this.fetchQrInfo();
    },
    // 部分确认
    async handleManualConfirm(row) {
      const { value: qrCode } = await this.$prompt("", this.$t("lang.rms.fed.confirmQrCode"), {
        inputValue: row.qrCode || row.oldQrCode,
      });
      const { code } = await $req.post("/athena/map/qrLog/confirmInfoSingle", {
        id: row.id,
        qrCode,
        mapId: Number(this.mapId),
        floorId: Number(this.floorId),
      });
      !code && this.$message.success(this.$t("lang.common.success"));
      this.fetchQrInfo(this.selectQrStatus);
    },
    // 全部确认
    handleConfirmAll() {
      if (this.lockBtn) {
        return false;
      }
      this.confirmQrCode(this.tableData.map(i => i.id));
    },
    // 确认接口
    async confirmQrCode(ids) {
      const { mapId, floorId } = this;
      const { code } = await $req.post("/athena/map/qrLog/confirmInfoAll", {
        mapId: Number(mapId),
        floorId: Number(floorId),
      });
      !code && this.$message.success(this.$t("lang.common.success"));
      this.fetchQrInfo(this.selectQrStatus);
    },
    handleDialogClosed() {
      this.$emit("update:visible", false);
    },
    selectChange() {
      this.handleSearch();
    },
  },
};
</script>
<style lang="less" scoped>
.map-manage-qrcode__form {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 25px;

  .select_label {
    display: block;
    padding-bottom: 8px;
  }
}
.btnBox {
  margin-left: 16px;
  display: flex;
  align-items: flex-end;
}
</style>

<style lang="less">
.map-manage-qrcode {
  .gp-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: calc(100% - 180px);
  }
  .gp-dialog__body {
    padding: 0 16px;
    flex: 1;
    max-height: none !important;
    display: flex;
    flex-direction: column;
  }
  .map-manage-qrcode__table {
    flex: 1;
  }
}
</style>
