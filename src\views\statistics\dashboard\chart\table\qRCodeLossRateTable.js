import Chart, { requestCache } from "../common";

/**
 * 3.4机器人二维码丢码率统计
 */
export default class QRCodeLossRateTable extends Chart {
  /**
   * 初始化图表 - 3.4机器人二维码丢码率统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('table', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人二维码丢码率频次汇总";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      },
      // 机器人类型
      // https://confluence.geekplus.cc/pages/viewpage.action?pageId=177542368
      // athena/map/version/findRobotType
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/robot/fault/detailByDay', {
      startTime,
      endTime,
      statType: 'GROUND_MISSING_COUNT',
      currentPage: 1,
      pageSize: 20,
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const dataItem = [];
    Object.values(data).forEach(item => {
      dataItem.push(...item);
    })
    let tableCloumns = [
      { label: '机器人id', prop: 'code' },
      { label: '机器人类型', prop: 'codeType' },
      { label: '总数', prop: 'sumData', width: '70px' },
      { label: '异常数', prop: 'faultData', width: '80px' },
      { label: '异常比例', prop: 'faultProportion', width: '80px' },
      { label: '时间戳', prop: 'statDateStamp', type: 'time' },
    ];

    return {
      title: this.title,
      tableCloumns,
      tableData: dataItem,
    }
  }
}