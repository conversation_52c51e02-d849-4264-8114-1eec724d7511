<template>
  <geek-main-structure class="software-control">
    <!-- 机器人软件管理 -->
    <div class="software-control-header">
      <div class="btnwarp">
        <gp-button
          v-if="checkPermission('RobotSoftwareManagerAdd', 'natural')"
          type="primary"
          @click="dialogFormVisible = true"
        >
          {{ $t("lang.rms.fed.newlyAdded") }}
        </gp-button>
        <gp-button
          v-if="checkPermission('RobotSoftwareManagerDelete', 'natural')"
          type="danger"
          @click="handleDeleteRobotControl"
        >
          {{ $t("lang.rms.fed.delete") }}
        </gp-button>
      </div>
    </div>
    <div class="software-control-panel-wrap__table">
      <gp-table :data="robotStatus" style="width: 100%; height: 100%" @selection-change="handleSelectionChange">
        <gp-table-column type="selection" width="55" />
        <gp-table-column prop="version" :label="$t('lang.rms.fed.edition')" />
        <gp-table-column prop="type" :label="$t('lang.rms.fed.type')">
          <template slot-scope="scope">
            {{ typeObj[scope.row.type] }}
          </template>
        </gp-table-column>
        <gp-table-column prop="oldName" width="130" :label="$t('lang.rms.fed.fileName')" />
        <gp-table-column prop="createTime" :formatter="timeformatter" :label="$t('lang.rms.fed.creationTime')" />
        <gp-table-column prop="updateTime" :formatter="timeformatter" :label="$t('lang.rms.fed.updateTime')" />
        <gp-table-column :label="$t('lang.rms.fed.operation')" width="120">
          <template slot-scope="scope">
            <gp-button type="text" size="small" @click="handleEdit(scope.$index, scope.row)">
              {{ $t("lang.rms.fed.edit") }}
            </gp-button>
            <gp-button type="text" size="small" @click="handleActive(scope.$index, scope.row)">
              {{ $t("lang.rms.fed.application") }}
            </gp-button>
          </template>
        </gp-table-column>
      </gp-table>
    </div>
    <div style="text-align: right; margin-top: 16px">
      <geek-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total-page="totalPage"
        @currentPageChange="currentPageChange"
        @pageSizeChange="pageSizeChange"
      />
    </div>
    <gp-dialog
      :title="$t('lang.rms.fed.addSoftware')"
      :visible.sync="dialogFormVisible"
      width="640px"
      @close="clearAddForm"
    >
      <gp-form ref="addForm" :model="addForm" label-width="80px" label-position="top">
        <gp-row :gutter="20">
          <gp-col :span="12">
            <gp-form-item :label="$t('lang.rms.fed.edition')" prop="version">
              <!-- :placeholder="x1" -->
              <gp-input v-model="addForm.version" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <gp-form-item :label="$t('lang.rms.fed.type')" prop="type">
              <gp-select v-model="addForm.type">
                <gp-option v-for="item in statusList" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </gp-select>
            </gp-form-item>
          </gp-col>
        </gp-row>
        <gp-row>
          <gp-col :span="24">
            <gp-form-item :label="$t('lang.rms.fed.describe')" prop="descr">
              <!-- :placeholder="x2" -->
              <gp-input v-model="addForm.descr" />
            </gp-form-item>
          </gp-col>
        </gp-row>
      </gp-form>
      <gp-upload
        ref="upload"
        class="upload"
        action="/athena/robot/software/upload"
        :limit="1"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :on-remove="handleRemove"
        :auto-upload="false"
        accept=".gz,.zip,.bin"
      >
        <gp-button slot="trigger" size="small" type="primary">{{ $t("lang.rms.fed.chooseAFile") }}</gp-button>
        <gp-button style="margin-left: 10px" size="small" type="success" @click="submitUpload">
          {{ $t("lang.rms.fed.upload") }}
        </gp-button>
        <div slot="tip" class="gp-upload__tip">{{ $t("lang.rms.fed.uploadOneFile") }}</div>
      </gp-upload>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="clearAddForm">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button
          type="primary"
          :disabled="addForm.version == '' || !uploadInfo.newName"
          @click="handleSubmitRobotControl"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
    <gp-dialog
      :title="$t('lang.rms.fed.pleaseEnterTheNumber') + ',' + $t('lang.rms.fed.multipleNumbersAreSeparatedByComma')"
      :visible.sync="dialogRobotVisible"
    >
      <gp-form :model="formRobot">
        <gp-form-item>
          <gp-input v-model="formRobot.ids" autocomplete="off" />
        </gp-form-item>
      </gp-form>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="dialogRobotVisible = false">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" :disabled="formRobot.ids == ''" @click="handleSubmitActive">
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
  </geek-main-structure>
</template>

<script>
import GeekPagination from "@plugins/components/geek-pagination";

export default {
  name: "SoftwareControl",
  components: { GeekPagination },
  data() {
    return {
      formRobot: {
        softwareId: "",
        ids: "",
      },
      addForm: {
        version: "",
        type: 0,
        descr: "",
      },
      isEdit: false,
      fileList: [],
      dialogFormVisible: false,
      dialogRobotVisible: false,
      totalPage: 1,
      currentPage: 1,
      pageSize: 10,

      loading: true,
      tableSelection: [],
      uploadInfo: {},
      recordCount: 0,
      pageSizes: [10, 20, 50, 100],
      typeObj: {},
      statusList: [],
      robotStatus: [],
    };
  },
  activated() {
    this.getAllRobotFirmwareType();
    this.findAllRobotControl();
  },
  methods: {
    checkButtonPermission() {
      return true;
    },
    getAllRobotFirmwareType() {
      $req.get("/athena/robot/software/allRobotFirmwareType").then(res => {
        const data = res?.data || [];
        this.statusList = data.map(item => {
          this.typeObj[item.type] = this.$t(item.name);
          return {
            value: item.type,
            label: item.name,
          };
        });
      });
    },
    findAllRobotControl() {
      const { currentPage, pageSize } = this.$data;

      $req
        .get("/athena/robot/software/findAll", {
          currentPage,
          pageSize,
          category: 0,
        })
        .then(res => {
          const data = res.data;
          this.currentPage = data.currentPage || 1;
          this.totalPage = data.pageCount;
          this.$nextTick(() => {
            this.robotStatus = res.data.recordList;
          });
        });
    },
    handleEdit(index, data) {
      this.isEdit = true;
      this.addForm = {
        version: data.version || "",
        type: data.type || 0,
        descr: data.descr || "",
        id: data.id,
      };
      this.uploadInfo = data;
      this.fileList = [
        {
          name: data.newName || "",
          url: data.path,
        },
      ];
      this.dialogFormVisible = true;
    },
    handleActive(index, data) {
      this.dialogRobotVisible = true;
      this.formRobot.softwareId = data.id;
    },
    handleSubmitActive() {
      // this.$store.dispatch('setLoading', true)
      $req
        .post("/athena/robot/software/active", {
          softwareId: this.formRobot.softwareId,
          robotIds: this.formRobot.ids.split(","),
          params: true,
        })
        .then(res => {
          // this.$store.dispatch('setLoading', false)
          if (res.code === 0) {
            this.$message.success(this.$t(res.msg));
          }
        })
        .finally(() => {
          this.dialogRobotVisible = false;
        });
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection.map(item => {
        return item.id;
      });
    },
    handleDeleteRobotControl() {
      if (this.tableSelection.length > 0) {
        // this.$store.dispatch('setLoading', true)
        $req.post("/athena/robot/software/delete", this.tableSelection).then(res => {
          this.findAllRobotControl();
          // this.$store.dispatch('setLoading', false)
        });
      }
    },
    handleSubmitRobotControl() {
      const self = this;
      // this.$store.dispatch('setLoading', true)
      const data = Object.assign({}, this.uploadInfo, this.addForm);
      if (this.isEdit) {
        $req.post("/athena/robot/software/update", data).then(res => {
          self.findAllRobotControl();
          self.clearAddForm();
          // self.$store.dispatch('setLoading', false)
          self.isEdit = false;
        });
      } else {
        $req.post("/athena/robot/software/add", data).then(res => {
          self.findAllRobotControl();
          self.clearAddForm();
          // self.$store.dispatch('setLoading', false)
        });
      }
    },
    clearAddForm() {
      this.dialogFormVisible = false;
      this.$refs.upload.clearFiles();
      this.addForm = {
        version: "",
        type: 0,
        descr: "",
      };

      this.uploadInfo = {};
      this.$refs["addForm"].resetFields();
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleUploadSuccess(res) {
      if (res.code !== 0) {
        this.$message.error($utils.Tools.transMsgLang(res.msg));
        this.$refs.upload.clearFiles();
        return;
      }
      this.uploadInfo = res.data;
    },
    handleRemove() {
      this.uploadInfo = {};
    },
    statusformatter(row, column) {
      return this.$t(this.statusList[row[column["property"]]].label);
    },
    timeformatter(row, column) {
      return new Date(row[column["property"]])?.toLocaleString();
    },
    currentPageChange(page) {
      this.currentPage = page;
      this.findAllRobotControl();
    },
    pageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.findAllRobotControl();
    },
  },
};
</script>

<style lang="less">
.software-control {
  display: flex;
  flex-direction: column;
  // height: calc(100% -20px);
  .software-control-panel-wrap__table {
    padding-top: 16px;
    flex: 1;
  }
}
</style>
