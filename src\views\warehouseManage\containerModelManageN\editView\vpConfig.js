export function getComponentByTypeMap(option = {}) {
  const modelCategory = option.extendJson?.subCategory || option.modelCategory;
  return {
    SPECIAL_PALLET: 'floorHolderSpecial',
    PPP_SHELF: 'shelf',
    SHELF_HOLDER: 'floorHolder',
    PALLET_RACK: 'trayShelf',
    PALLET: 'tray',
    SHELF: 'shelf',
    BOX: 'pickBox',
    X_PALLET: 'tray',
    X_HOLDER: 'floorHolder',
  }[modelCategory] || "";
}

export function getVpConfig(meshType, option = {}) {
  const modelCategory = option.extendJson?.subCategory || option.modelCategory;
  switch (meshType) {
    case 'pickBox':
      return {
        w: (option.width || 1000) / 1000,
        d: (option.length || 1000) / 1000,
        h: (option.height || 1000) / 1000,
      }

    case 'shelf':
      const optionAppend = {
        h: 0.3,
      };
      // debugger
      const shelfHeight = ((option.height || 300) - 300) / 1000;
      if (modelCategory === "SHELF") {
        optionAppend.floorOps = {};
        const curLayerItemHeight = shelfHeight / 3;
        [1, 2, 3].forEach(index => {
          optionAppend.floorOps[index] = {
            h: curLayerItemHeight,
            column: 3,
          }
        })
      }

      if (modelCategory === "PPP_SHELF" && option.layoutList) {
        optionAppend.floorOps = {};
        const curLayout = (option.layoutList[0]?.layout || []);
        const curLayerItemHeight = shelfHeight / curLayout.length;
        curLayout.forEach((item, index) => {
          optionAppend.floorOps[index] = {
            h: curLayerItemHeight,
            column: item.curLayerColumnNumber || 1,
          }
        });
      }

      if (modelCategory === "PPP_SHELF" && option.containerLayout) {
        optionAppend.floorOps = {};
        const curLayout = (option.containerLayout[0]?.layout || []);
        const curLayerItemHeight = shelfHeight / curLayout.length;
        curLayout.forEach((item, index) => {
          optionAppend.floorOps[index] = {
            h: curLayerItemHeight,
            column: item.layerColumns?.length || 1
          }
        });
      }

      return {
        w: (option.width || 1) / 1000,
        d: (option.length || 1) / 1000,
        h: optionAppend.bottomH,
        legOps: {
          w: (option.legWidth || 20) / 1000,
          d: (option.legLength || 20) / 1000,
        },
        ...optionAppend
      };

    case "floorHolder":
      return {
        w: (option.length || 1) / 1000,
        d: (option.width || 1) / 1000,
        h: (option.passHeight || 1) / 1000,
        legOps: {
          w: (option.legWidth || 1) / 1000,
          d: (option.legLength || 1) / 1000
        },
        trayOps: {
          w: (option.offsetY || 1) / 1000,
        }
      }

    case 'tray':
      if (!option.extendJson) {
        return {}
      }
      const isDouble = option.extendJson?.structure === 'doubleHole'
      const holeHeight = (option.extendJson?.hole?.length || 100) / 1000
      return {
        w: (option.extendJson?.palletOutsite?.length || 100) / 1000,
        d: (option.extendJson?.palletOutsite?.width || 100) / 1000,
        h: holeHeight * 1.15,
        isDouble: isDouble,
        normalPostOps: {
          w: (option.extendJson?.edgeColumn?.width || 100) / 1000,
        },
        midPostOps: {
          w: (option.extendJson?.hole?.middleColumnWidth || 100) / 1000,
        },
        holeOps: {
          w: (option.extendJson?.hole?.width || 100) / 1000,
          h: holeHeight
        },
      }

    case 'trayShelf':
      const trayShelf_floorOps = {};

      (option.extendJson?.detail || []).forEach((item) => {
        trayShelf_floorOps[item.layerNumber] = {
          h: (item.height || 10) / 1000,
          column: 1,
        }
      });

      return {
        floorOps: trayShelf_floorOps
      }

    case 'floorHolderSpecial':
      return {
        //货架腿位置
        legOps: (option.extendJson?.legs || []).map(item => {
          return {
            pos: [(item.rcX) / 1000, (item.rcY) / 1000],
            w: (item.width || 100) / 1000,
            d: (item.length || 100) / 1000,
            h: (item.substructureHeight || 10) / 1000,
          }
        }),
        trayOps: {
          w: (option.width || 1000) / 1000,
          d: (option.length || 1000) / 1000,
          h: (option.height || 10) / 1000
        }
      };

    default:
      return {}
  }
}