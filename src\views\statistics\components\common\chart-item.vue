<template>
  <div class="centent">
    <gp-date-picker
      v-if="isFilter"
      class="picker"
      v-model="filterDate"
      :clearable="false"
      type="date"
      size="mini"
      @change="filterChange"
    ></gp-date-picker>
    <div ref="chartEl" class="chart"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import "echarts-liquidfill";
import * as Diff from "diff";
window.diff = Diff;
export default {
  name: "statisticsChartList",
  props: {
    option: {
      type: Object,
      default: () => {
        return null;
      },
    },
    isFilter: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },

  data() {
    return {
      chartInstance: null,
      filterDate: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
    };
  },

  computed: {
    computedOption() {
      const { option } = this;
      if (!option) {
        return option;
      }
      const titleText = this.$t(option.title?.text || "");
      return { ...option, title: { ...option.title, text: titleText } };
    },
  },

  watch: {
    computedOption: {
      handler(newOption, oldOption) {
        const option = {};
        if (oldOption) {
          const keySet = new Set([...Object.keys(newOption), ...Object.keys(oldOption)]);
          [...keySet.values()].forEach(key => {
            const item = Diff.diffJson(newOption[key] || {}, oldOption[key] || {});
            if (item?.length > 1) {
              option[key] = newOption[key];
            }
          });

          Object.keys(option).length > 0 && this.initChart(option);
        } else {
          this.initChart(newOption);
        }
      },
      deep: true,
    },
  },

  mounted() {
    this.initChart();
  },

  destroyed() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },

  methods: {
    initChart(option) {
      const { $refs } = this;
      const curOption = option || this.computedOption;
      if (curOption && $refs.chartEl) {
        if (this.chartInstance) {
          // 更新
          const replaceMerge = Object.keys(curOption);
          this.chartInstance.setOption(curOption, {
            replaceMerge,
          });
        } else {
          // 初始化
          const chartEl = $refs.chartEl;
          this.chartInstance = echarts.init(chartEl);
          this.chartInstance.setOption(curOption);
        }
      }
    },

    filterChange(date) {
      this.$emit("filter", { date });
    },
  },
};
</script>

<style lang="less" scoped>
.centent {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  background: #fff;
  border: 0.01rem solid #eee;
  border-radius: 0.03rem;
  box-shadow: 0 0.02rem 0.12rem 0 rgb(0 0 0 / 10%);
}

.picker {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 20;
  width: 130px;
}
</style>
