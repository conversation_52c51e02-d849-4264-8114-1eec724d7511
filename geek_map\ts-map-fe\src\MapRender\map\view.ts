/* ! <AUTHOR> at 2022/07/25 */
import * as PIXI from "pixi.js";
import { Viewport } from "pixi-viewport";
import _MapIcons from "../config/icon/index";

class MapView {
  private App: PIXI.Application;
  private viewport: any;
  private mapCore: MRender.MainCore;
  private mapPositionCallback: (renderType: MRender.renderedType, data?: any) => void;

  constructor(mapCore: MRender.MainCore, $dom: HTMLElement) {
    this.mapCore = mapCore;
    this.createView($dom);
  }

  renderAll() {
    const _this = this;
    if (!_this.App) return;
    // console.info("[render] >>>>> renderAll:: this is application render All.");
    _this.App.render();
    const mapPositionCallback = _this.mapPositionCallback;
    if (!mapPositionCallback) return;

    // 下面这是实时获取下位置 目前只有stations
    const mapCore = _this.mapCore;
    const showStationPop = mapCore.mapConfig.getRenderConfig("showStationPop");
    const showChargePop = mapCore.mapConfig.getRenderConfig("showChargePop");
    // 工作站的位置更新
    if (showStationPop) {
      const positions = mapCore.mapData.station.getPositions();
      mapPositionCallback("mapPosition", { type: "stations", data: positions });
    } else {
      mapPositionCallback("mapPosition", { type: "stations", data: null });
    }

    if (showChargePop) {
      const positions = mapCore.mapData.charger.getPositions();
      mapPositionCallback("mapPosition", { type: "charger", data: positions });
    } else {
      mapPositionCallback("mapPosition", { type: "charger", data: null });
    }

    mapPositionCallback("abnormalRack", {
      type: "abnormalRack",
      data: mapCore.mapData.rack.getAllAbnormalCode(),
    });
  }

  zoom(val: number): void {
    const viewport = this.viewport;
    if (!viewport) return;
    viewport.zoomPercent(val, true);
    this.renderAll();
  }
  resize(): void {
    this.App.resize();
    this.viewport.resize();
    this.renderAll();
  }
  setMapPosition(mapPosition: MRender.mapPosition): void {
    const viewport: any = this.viewport;
    if (!viewport) return;
    viewport.setTransform(mapPosition.transX, mapPosition.transY);
    if (mapPosition.scale) viewport.setZoom(mapPosition.scale, true);
    this.renderAll();
  }
  setMapCenter(border = false) {
    const viewport: any = this.viewport;
    const { screenWidth, screenHeight } = viewport;
    let { x, y, width, height } = viewport.getLocalBounds();

    if (width <= 0) width = 1;
    if (height <= 0) height = 1;
    if (width > screenWidth) width = Math.max(screenWidth, width);
    if (height > screenHeight) height = Math.max(screenHeight, height);

    const padding = 50;
    let transX = -x + (screenWidth - width) / 2;
    let transY = -y + (screenHeight - height) / 2;
    let scale = Math.min((screenWidth - padding) / width, (screenHeight - padding) / height);
    scale = Math.floor(scale * 1000) / 1000;

    viewport.setTransform(transX, transY);
    viewport.setZoom(scale, true);

    this.mapCore.mapConfig.setRenderConfig("initMapPosition", { transX, transY, scale });
    if (border) this._renderMapBorder(x, y, width, height);
    this.renderAll();
  }

  loadResources(): Promise<any> {
    return new Promise((resolve, reject) => {
      let key: MRender.iconsNames;

      let textures: any = {};
      for (key in _MapIcons) {
        textures[key] = PIXI.Texture.from(_MapIcons[key]);
      }
      resolve(textures);
    });
  }

  setPositionChange(cb: (renderType: MRender.renderedType, data?: any) => void) {
    this.mapPositionCallback = cb;
  }

  getViewport(): Viewport {
    return this.viewport;
  }

  destroy(): void {
    this.viewport.destroy();
    this.App.destroy(true, {
      children: true,
      texture: true,
      baseTexture: true,
    });

    this.App = null;
    this.viewport = null;
    this.mapCore = null;
  }

  private createView($dom: HTMLElement) {
    const bgColor: number = this.mapCore.utils.getOriginColor("BG");
    const width = $dom.offsetWidth;
    const height = $dom.offsetHeight;

    let app = new PIXI.Application({
      width,
      height,
      resizeTo: $dom,
      antialias: true, // 使字体的边界和几何图形更加圆滑
      backgroundColor: bgColor,
      autoStart: false,
    });

    let viewport: any = new Viewport({
      screenWidth: width,
      screenHeight: height,
      passiveWheel: false,
      divWheel: $dom,
      interaction: app.renderer.plugins.interaction,
    });

    viewport.drag().pinch().wheel({ percent: 0.1 }); // .decelerate(); // 他们说不需要平滑得缓慢得结束移动~~
    viewport.on("drag-start", () => this.renderAll());
    viewport.on("moved", () => this.renderAll());
    viewport.on("drag-end", () => this.renderAll());
    viewport.name = "rms-viewport";
    viewport.sortableChildren = true; // 使zIndex属性有用

    app.stage.addChild(viewport);
    $dom.appendChild(app.view);

    this.viewport = viewport;
    this.App = app;
    this.setMapCenter();
  }

  private _renderMapBorder(x: number, y: number, width: number, height: number) {
    const borderWidth = 1,
      borderSpace = 25;

    const border: any = new PIXI.Graphics();
    border.lineStyle(borderWidth, 0x333333);
    border.drawRect(
      x - borderSpace,
      y - borderSpace,
      width + borderWidth + borderSpace * 2,
      height + borderWidth + borderSpace * 2,
    );

    this.viewport.addChild(border);
  }
}

export default MapView;
