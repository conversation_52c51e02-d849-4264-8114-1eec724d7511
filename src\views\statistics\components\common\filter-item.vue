<template>
  <div class="filterItem">
    <gp-form size="mini" label-width="80px" :model="formData">
      <gp-form-item v-for="item in filterData" :key="item.prop" :label="$t(item.label)">
        <template v-if="item.type === 'gpSelect'">
          <gp-select class="wfull" v-model="formData[item.prop]" v-bind="item.option || {}">
            <gp-option v-for="item in item.optionList" :key="item.value" :label="item.label" :value="item.value">
            </gp-option>
          </gp-select>
        </template>
        <component class="wfull" v-else :is="item.type" v-bind="item.option || {}" v-model="formData[item.prop]" />
      </gp-form-item>
      <gp-form-item>
        <gp-button type="primary" size="mini" @click="query">查询</gp-button>
      </gp-form-item>
    </gp-form>
  </div>
</template>

<script>
export default {
  name: "filterItem",
  props: {
    filterData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const formData = {};
    this.filterData.forEach(item => {
      formData[item.prop] = item.defaultValue;
    });
    return { formData };
  },
  watch: {
    filterData: {
      handler() {
        this.filterData.forEach(item => {
          this.formData[item.prop] = item.defaultValue;
        });
      },
      deep: true,
    },
  },
  methods: {
    query() {
      this.$emit("query", this.formData);
    },
  },
};
</script>

<style lang="less" scoped>
.chart-list {
  .g-flex();
  width: 100%;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0 8px;
  max-height: 100%;
  overflow-y: auto;
}

.chart-item {
  padding: 5px;
  width: 25%;

  .item-box {
    background: #fff;
    border: 1px solid #eee;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    cursor: pointer;

    > h6 {
      padding: 20px 12px;
      font-weight: 600;
      color: #666;
      text-align: center;
      font-size: 16px;
      border-bottom: 2px dashed #ccc;
    }

    > img {
      width: 100%;
      height: 160px;
      padding: 8px 0 0;
      display: block;
      text-align: center;
    }
  }
}

.wfull {
  width: 100%;
}
</style>
