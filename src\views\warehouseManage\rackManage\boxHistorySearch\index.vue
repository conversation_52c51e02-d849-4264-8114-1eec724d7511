<template>
  <section class="rack-manage-panel-wrap">
    <geek-customize-form ref="refFrom" :form-config="formConfig" inSearchPage @on-query="onQuery" @on-reset="onReset" />
    <div class="rack-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
      >
      </geek-customize-table>
    </div>
  </section>
</template>
<script>
export default {
  data() {
    let start = new Date();
    let end = new Date();
    start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
    return {
      form: {
        boxCode: "",
        dataRange: [],
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          boxCode: {
            label: "lang.rms.fed.boxCode",
            default: "",
            placeholder: "lang.rms.fed.web.boxCode.input",
            tag: "input",
          },
          dataRange: {
            label: "lang.rms.fed.updateTime",
            default: [start, end],
            valueFormat: "yyyy-MM-dd HH:ss:mm",
            type: "datetimerange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      dicts: [],
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
          },
          {
            label: "lang.rms.fed.destShelfSide",
            prop: "destShelfSide",
            formatter: (row, column) => {
              return this.$t(this.dicts.find(i => i.value === row[column])?.label || row[column]);
            },
          },
          { label: "lang.rms.fed.shelfCoding", prop: "destShelfCode" },
          {
            label: "lang.rms.fed.stationNo",
            prop: "stationId",
            formatter(row, column) {
              return row["destShelfCode"] ? "" : row[column];
            },
          },
        ],
      },
    };
  },
  activated() {
    $req.reqRMSDict(["BOX_SEARCH"]).then(res => {
      if (res?.code !== 0) return;
      const data = res.data || {};
      const dicts = data["BOX_SEARCH"] || [];

      this.dicts = dicts.map(i => {
        return { label: i.descr, value: i.fieldCode };
      });
    });
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
      this.tablePage.currentPage = 1;
      this.$refs.refFrom.setData({ dataRange: [start, end] });
      this.form = { boxCode: "", dataRange: [start, end] };
    },
    getTableList() {
      let startTime = null;
      let endTime = null;
      if (this.form.dataRange) {
        [startTime, endTime] = this.form.dataRange;
      }
      const params = {
        boxCode: this.form.boxCode,
        startTime: startTime ? new Date(startTime).valueOf() : null,
        endTime: endTime ? new Date(endTime).valueOf() : null,
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (!params.boxCode) {
        this.$warning(this.$t("lang.mb.login.required", [this.$t("lang.rms.fed.boxCode")]));
        return;
      }
      $req.get("/athena/box/movePath", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          total: result.recordCount || 0,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
