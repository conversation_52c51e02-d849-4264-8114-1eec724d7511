/* ! <AUTHOR> at 2021/08 */
import * as PIXI from "pixi.js";

class EventRectSelect {
  private mapCore: MRender.MainCore;
  private viewport: any;
  private graphicsRect: any = null;

  private isSelect = false;
  private isDrag = false;
  private startFloorId: floorId = null;
  private startPoint: location = null;
  private endPoint: location = null;
  private startData: location = null;
  private endData: location = null;
  private cb: callback = null;

  constructor(mapCore: MRender.MainCore) {
    this.mapCore = mapCore;
    this.graphicsRect = null;

    const viewport = mapCore.mapView.getViewport();
    viewport.on("pointerdown", this.start.bind(this));
    viewport.on("pointermove", this.move.bind(this));
    viewport.on("pointerup", this.end.bind(this));
    document.addEventListener("keydown", e => {
      // 按 ESC
      if (e.keyCode !== 27) return;
      this.remove();
    });
    this.viewport = viewport;
  }

  init(cb: callback) {
    const viewport = this.viewport;
    this.isSelect = true;
    this.startPoint = { x: 0, y: 0 };
    this.endPoint = { x: 0, y: 0 };
    this.startData = { x: 0, y: 0 };
    this.endData = { x: 0, y: 0 };
    this.startFloorId = null;
    this.cb = cb;

    let graphicsRect = new PIXI.Graphics();
    graphicsRect.zIndex = 999;
    viewport.addChild(graphicsRect);
    this.graphicsRect = graphicsRect;
  }

  getTriggerStatus() {
    return this.isSelect;
  }

  start(e: any) {
    if (!this.isSelect) return;
    this.isDrag = true;
    const viewport = this.viewport;
    viewport.drag({ pressDrag: false });

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 start, 没有e.data, e 出错了?");

    const mapFloors = this.mapCore.mapFloors;

    let offsetX, offsetY, bounds;
    for (let floorId in mapFloors) {
      const floorLayer = mapFloors[floorId].getLayerFloor();
      const { x, y } = eData.getLocalPosition(floorLayer);
      bounds = floorLayer.getLocalBounds();
      if (x > bounds.x && x < bounds.x + bounds.width && y > bounds.y && y < bounds.y + bounds.height) {
        offsetX = x;
        offsetY = y;
        console.log("-----floorLayer----", floorId, x, y);
        break;
      }
    }

    const { x: vX, y: vY } = eData.getLocalPosition(viewport);

    if (!this.calPosition(eData, offsetX, offsetY)) {
      this.cb && this.cb({ code: -1 }); // 超出楼层范围
      this.remove();
      return;
    }

    this.startPoint = { x: vX, y: vY };
    this.endPoint = { x: vX, y: vY };
    this.startData = { x: offsetX, y: offsetY };
    this.endData = { x: offsetX, y: offsetY };
  }

  move(e: any) {
    if (!this.isSelect || !this.isDrag) return;

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 move, 没有e.data, e 出错了?");
    const { x: vX, y: vY } = eData.getLocalPosition(this.viewport);

    this.endPoint = { x: vX, y: vY };
    this.drawRect();
  }
  end(e: any) {
    if (!this.isSelect) return;

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 end, 没有e.data, e 出错了?");
    const { x: vX, y: vY } = eData.getLocalPosition(this.viewport);
    this.endPoint = { x: vX, y: vY };

    const mapFloors = this.mapCore.mapFloors;
    const startfloorLayer = mapFloors[this.startFloorId].getLayerFloor();
    let offsetX, offsetY;
    const { x, y } = eData.getLocalPosition(startfloorLayer);
    const startFloorBounds = startfloorLayer.getLocalBounds();
    if (
      x > startFloorBounds.x &&
      x < startFloorBounds.x + startFloorBounds.width &&
      y > startFloorBounds.y &&
      y < startFloorBounds.y + startFloorBounds.height
    ) {
      offsetX = x;
      offsetY = y;
    }

    if (!this.calPosition(eData, offsetX, offsetY)) {
      this.cb && this.cb({ code: -2 }); // 不能跨楼层
      this.remove();
      return;
    }

    this.endData = { x: offsetX, y: offsetY };
    this.drawRect();
    if (this.cb) {
      console.log(this.startData, this.endData);
      const mapFloors = this.mapCore.mapFloors;
      const floorId = Number(this.startFloorId);
      const floorLayer = mapFloors[floorId].getLayerFloor();

      const { x: sx, y: sy } = this.startData;
      const { x: ex, y: ey } = this.endData;

      this.cb({
        code: 0,
        points: [
          { x: sx, y: Math.abs(sy), z: floorId },
          { x: ex, y: Math.abs(ey), z: floorId },
        ],
      });
    }

    this.remove();
  }

  remove() {
    this.isSelect = false;
    this.isDrag = false;
    this.startFloorId = null;
    this.startPoint = null;
    this.endPoint = null;
    this.cb = null;

    if (this.graphicsRect) {
      this.viewport.removeChild(this.graphicsRect);
      this.graphicsRect.destroy();
      this.graphicsRect = null;
    }
    this.viewport.drag({ pressDrag: true });

    this.mapCore.mapView.renderAll();
  }

  destroy() {
    this.mapCore = null;
    this.viewport = null;
    this.graphicsRect = null;
    this.isSelect = false;
    this.isDrag = false;
    this.startFloorId = null;
    this.startPoint = null;
    this.endPoint = null;
    this.cb = null;
  }

  private drawRect() {
    const start = this.startPoint;
    const end = this.endPoint;
    const width = end.x - start.x;
    const height = end.y - start.y;

    let graphicsRect = this.graphicsRect;
    graphicsRect.clear();
    graphicsRect.lineStyle({ color: 0xf36d68, width: 0.05 });
    graphicsRect.beginFill(0x650a5a);
    graphicsRect.drawRect(start.x, start.y, width, height);
    graphicsRect.endFill();

    this.mapCore.mapView.renderAll();
  }

  private calPosition(eData: any, x: number, y: number): boolean {
    const mapFloors = this.mapCore.mapFloors;
    let floorLayer: any, bounds;
    for (let floorId in mapFloors) {
      floorLayer = mapFloors[floorId].getLayerFloor();
      const { x, y } = eData.getLocalPosition(floorLayer);
      bounds = floorLayer.getLocalBounds();
      if (x > bounds.x && x < bounds.x + bounds.width && y > bounds.y && y < bounds.y + bounds.height) {
        if (!this.startFloorId) this.startFloorId = floorId;
        else if (this.startFloorId !== floorId) return false;
        return true;
      }
    }

    return false;
  }
}

export default EventRectSelect;
