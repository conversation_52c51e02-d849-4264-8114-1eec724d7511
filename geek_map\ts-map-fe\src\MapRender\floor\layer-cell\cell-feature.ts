/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

/** cell颜色 */
class LayerCellFeature {
  private floorId: floorId;
  private mapCore: any;
  private shader: any;
  private container: PIXI.Container;

  private meshList: Array<any> = [];
  private meshHeat: Array<any> = [];
  private meshFilter: Array<any> = [];
  private meshRoadHeat: Array<any> = [];
  private fragment: number;
  private hotColors: { [propName: number]: MRender.shaderColor };
  private pathHotColors: { [propName: number]: MRender.shaderColor };

  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "cellFeature";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.hotColors = mapCore.utils.formatHotColor();
    this.pathHotColors = mapCore.utils.formatPathHotColor();
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.shader = utils.getShader("rect");
    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(cells: Array<mCellData>, color: color16 = 0x2bef69): void {
    const _this = this;
    const utils = _this.mapCore.utils;

    const vColor = utils.getShaderRGB(color);
    const fragment = _this.fragment;
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);
      const geometries = _this.drawRect(arr, vColor);

      let mesh = utils.createMesh(geometries, _this.shader);
      mesh.name = "featureCell";
      mesh.mapType = "featureCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
  }

  renderFilter(cells: Array<mCellData>, color: color16 = 0xff0000) {
    const _this = this;
    const utils = _this.mapCore.utils;
    const vColor = utils.getShaderRGB(color);

    const fragment = _this.fragment;
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);
      const geometries = _this.drawFilter(arr, vColor);

      let mesh = utils.createMesh(geometries, _this.shader);
      mesh.name = "featureCell";
      mesh.mapType = "featureCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshFilter.push(mesh);
      _this.container.addChild(mesh);
    }
  }

  /**
   * 渲染热度
   * @returns
   */
  renderHeat(isHeat: boolean) {
    if (!isHeat) {
      this.repaint("heat");
      return;
    }
    const _this = this;
    const floorId = _this.floorId;
    const mapCore = _this.mapCore;
    const utils = mapCore.utils;

    const cells: mCellData[] = mapCore.mapData.cell.getByFloorId(floorId);
    if (!cells.length) return;

    const fragment = _this.fragment;
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);
      const geometries = _this.drawRect(arr);

      let mesh = utils.createMesh(geometries, _this.shader);
      mesh.name = "featureCell";
      mesh.mapType = "featureCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshHeat.push(mesh);
      _this.container.addChild(mesh);
    }
  }

  /**
   * 渲染路径热度
   * @returns
   */
  renderRoadHeat(isHeat: boolean, heatData: any, cb?: Function) {
    if (!isHeat) {
      this.repaint("roadHeat");
      return;
    }

    const _this = this;
    const floorId = _this.floorId;
    const mapCore = _this.mapCore;
    const utils = mapCore.utils;

    const cells: mCellData[] = mapCore.mapData.cell.getByFloorId(floorId);
    if (!cells.length) return;

    for (let i = 0, len = cells.length; i < len; i++) {
      const cell = cells[i];
      const cellCode = cell.code;
      const heat = heatData[cellCode];
      if (heat) {
        cell.roadScore = heat;
      } else {
        cell.roadScore = -1;
      }
    }

    const fragment = _this.fragment;
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);
      const geometries = _this.drawRect(arr, null, "road");

      let mesh = utils.createMesh(geometries, _this.shader);
      mesh.name = "featureCell";
      mesh.mapType = "featureCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshRoadHeat.push(mesh);
      _this.container.addChild(mesh);
    }

    cb && cb();
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(type?: "heat" | "filter" | "color" | "roadHeat"): void {
    if (!type || type === "heat") {
      this.meshHeat.forEach(mesh => mesh.destroy(true));
      this.meshHeat = [];
    }

    if (!type || type === "filter") {
      this.meshFilter.forEach(mesh => mesh.destroy(true));
      this.meshFilter = [];
    }

    if (!type || type === "color") {
      this.meshList.forEach(mesh => mesh.destroy(true));
      this.meshList = [];
    }

    if (!type || type === "roadHeat") {
      this.meshRoadHeat.forEach(mesh => mesh.destroy(true));
      this.meshRoadHeat = [];
    }
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.shader = null;
    this.container = null;
    this.mapCore = null;
    this.fragment = null;
    this.floorId = undefined;
  }

  private drawRect(arr: mCellData[], vColor?: MRender.shaderColor, heatType?: "road"): any {
    const _this = this;
    const utils = _this.mapCore.utils;
    const hotColors = _this.hotColors;

    let shaderColor = vColor;
    let geometries = [];
    let options, geometry, hotColor;
    for (let i = 0, len = arr.length; i < len; i++) {
      options = arr[i];
      if (!vColor) {
        shaderColor = options["shaderColor"];
        if (heatType === "road") {
          if (options.roadScore !== -1) {
            hotColor = hotColors[options.roadScore];
            if (hotColor) shaderColor = hotColor;
          }
        } else {
          if (options.score !== -1) {
            hotColor = hotColors[options.score];
            if (hotColor) shaderColor = hotColor;
          }
        }
      }
      geometry = utils.drawGeometry("rect", options["position"], shaderColor);
      geometries.push(geometry);
    }

    return geometries;
  }

  private drawFilter(arr: mCellData[], vColor: MRender.shaderColor): any {
    const _this = this;
    const utils = _this.mapCore.utils;

    let geometries = [];
    let options, geometry;
    for (let i = 0, len = arr.length; i < len; i++) {
      options = arr[i];
      geometry = utils.drawGeometry("rect", options["position"], vColor);
      geometries.push(geometry);
    }

    return geometries;
    // const fillStyle = this.fillStyle;
    // const lineStyle = this.lineStyle;
    // let options, hitArea;
    // for (let i = 0, len = arr.length; i < len; i++) {
    //   options = arr[i];
    //   hitArea = options["hitArea"];
    //   const x = hitArea[0] + 0.04,
    //     y = hitArea[1] + 0.04,
    //     width = hitArea[2] - 0.04 - x,
    //     height = hitArea[3] - 0.04 - y;
    //   const polygon = [x, y, x + width, y, x + width, y + height, x, y + height];
    //   graphicsGeometry.drawShape(new PIXI.Polygon(polygon), fillStyle, lineStyle);
    //   // graphicsGeometry.drawShape(new PIXI.Rectangle(x, y, width, height), fillStyle, lineStyle);
    // }
  }
}
export default LayerCellFeature;
