<template>
  <gp-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onClose"
    width="70%"
  >
    <gp-form label-position="top" label-width="80px" :model="mainTableRowData">
      <gp-row :gutter="20">
        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.nameOfParameter')">
            <gp-input :value="mainTableRowData.paramCode" :disabled="true" />
          </gp-form-item>
        </gp-col>

        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.robotParamValueType')">
            <gp-input :value="paramValueTypeList[mainTableRowData.paramValueType]" class="w_100x" :disabled="true" />
          </gp-form-item>
        </gp-col>

        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.robotType')">
            <gp-input v-model="mainTableRowData.robotType" />
          </gp-form-item>
        </gp-col>

        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.robotParamValueLimit')">
            <gp-input v-model="mainTableRowData.paramLimit" />
          </gp-form-item>
        </gp-col>

        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.robotParamUnit')">
            <gp-input v-model="mainTableRowData.unit" :disabled="true" />
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item :label="$t('lang.rms.fed.describe')">
            <gp-input v-model="mainTableRowData.descr" :disabled="true" />
          </gp-form-item>
        </gp-col>
      </gp-row>
    </gp-form>
    <span slot="footer" class="dialog-footer">
      <gp-button @click="onClose">{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
    </span>
  </gp-dialog>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      paramValueTypeList: {
        0: this.$t("lang.rms.fed.robotParamValueTypeDigit"),
        1: this.$t("lang.rms.fed.robotParamValueTypeBool"),
        2: this.$t("lang.rms.fed.robotParamValueTypeString"),
        3: this.$t("lang.rms.fed.robotParamValueTypeJson"),
      },
      mainTableRowData: {},
    };
  },
  methods: {
    open(data) {
      this.mainTableRowData = data;
      this.dialogVisible = true;
    },
    onClose() {
      this.mainTableRowData = {};
      this.dialogVisible = false;
      // this.$emit("updateMainTale");
    },
    save() {
      this.dialogVisible = false;
      this.$emit("save", this.mainTableRowData);
      this.mainTableRowData = {};
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
