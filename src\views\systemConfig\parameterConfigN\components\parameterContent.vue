<template>
  <div class="box">
    <gp-radio-group v-model="tabContent" style="margin-bottom: 20px">
      <gp-radio-button v-for="item in modules" :key="item.id" :label="item.path">
        {{ $t(item.label) }}
      </gp-radio-button>
    </gp-radio-group>
    <template v-for="item in modules">
      <gp-card v-if="tabContent == item.path" :key="item.id" :class="[{ 'new-box-card': alertTipShow }, 'box-card']">
        <div
          slot="header" class="clearfix"
        >
          <span class="title">{{ $t(item.label) }}</span>
          <span v-if="alertIsShow" class="note">
            <i class="d" />{{ $t("lang.rms.fed.redIndicatesParameterNotTakeEffectToRestart") }}
          </span>
          <gp-button style="float: right; margin-left: 10px" size="mini" :disabled="!changeData" @click="cancel">
            {{ $t("lang.rms.fed.cancel") }}
          </gp-button>
          <gp-button type="primary" style="float: right" size="mini" :disabled="!changeData" @click="apply">
            {{ $t("lang.rms.fed.application") }}
          </gp-button>
        </div>
        <parameterForm
          :path="modulesPath"
          :list="newModulesList"
          :show="show"
          @changeData="change"
          @itemsDetail="itemsDetail"
        ></parameterForm>
      </gp-card>
    </template>
  </div>
</template>

<script>
import parameterForm from "./parameterForm";
export default {
  components: {
    parameterForm,
  },
  props: {
    isApplySuccess: {
      type: Boolean,
    },
    show: {
      type: Boolean,
      default() {
        return true;
      },
    },
    path: {
      type: String,
      default() {
        return "";
      },
    },
    currentName: {
      type: String,
      default() {
        return "";
      },
    },
    modules: {
      type: Array,
      default() {
        return [];
      },
    },
    modulesList: {
      type: [Array, Object],
      default() {
        return [];
      },
    },
    alertTipShow: {
      type: Boolean,
      default() {
        return false;
      },
    },
    limitShowObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      newModulesList: [],
      oldModulesList: [],
      tabContent: "",
      modulesPath: "",
      changeData: false,
      // alertIsShow: false,
      // immediateShow: false,
      paramsObj: {},
      itemsDetailObj: {
        code: "",
        contributor: "RMS",
        value: "",
      },
    };
  },
  computed: {
    alertIsShow() {
      let flag = false;
      if (this.newModulesList) {
        for (let key in this.newModulesList) {
          this.newModulesList[key] &&
            this.newModulesList[key].forEach(item => {
              if (
                item.immediate == false &&
                item.value !== item.currentUpdateValue &&
                item.currentUpdateValue !== null
              ) {
                flag = true;
              }
            });
        }
      }
      return flag;
    },
  },
  watch: {
    currentName: {
      // 一级tab默认展示
      handler(newval, oldval) {
        this.tabContent = "";
        // console.log(newval);
      },
      deep: true,
      immediate: true,
    },
    modulesList: {
      handler(val) {
        console.log(val, "modulesList", this.currentName);
        if (val && Object.prototype.toString.call(val) === "[object Object]") {
          let path = Object.keys(val)[0];
          // if (this.isApplySuccess) {
          //   // this.modulesPath = path;
          //   path = this.modulesPath.split("/")[2];
          // } else {
          this.modulesPath = `/${this.currentName}/${path}`;
          this.tabContent = `/${this.currentName}/${path}`;
          // }

          if (val[path]) {
            this.newModulesList = JSON.parse(JSON.stringify(val[path]));
            this.oldModulesList = JSON.parse(JSON.stringify(val[path]));
            // console.log(this.newModulesList, "newModulesList");
          } else {
            this.newModulesList = {};
            this.oldModulesList = {};
          }
        }
      },
      deep: true,
      immediate: true,
    },

    tabContent(path) {
      if (!path) {
        return;
      }
      const newPath = path.split("/")[2];
      this.$emit("changeModulesPath", {
        [this.modulesPath]: JSON.parse(JSON.stringify(this.newModulesList)),
      });
      this.modulesPath = path;
      // this.newModulesList = this.modulesList[newPath];
      const modulesData = this.modulesList[path] || this.modulesList[newPath];
      this.newModulesList = JSON.parse(JSON.stringify(modulesData));
      this.oldModulesList = JSON.parse(JSON.stringify(modulesData));
    },
  },
  created() {
    // 默认展示
    // this.modulesPath = "/map/resolver";
  },
  methods: {
    giveUp() {
      this.changeData = false;
      this.newModulesList = JSON.parse(JSON.stringify(this.oldModulesList));
      this.paramsObj = {};
    },
    tabContentReset() {
      this.tabContent = "";
    },
    change(n) {
      this.changeData = n;
      this.$emit("changeData", this.changeData);
    },
    itemsDetail(items) {
      console.log(items);
      this.paramsObj[items.code] = {
        code: items.code,
        contributor: "RMS",
        errorTipShow: items.errorTipShow,
        value: items.options.defVal,
      };
      if (items.code in this.limitShowObj) {
        this.limitShowObj[items.code] = items.options.defVal;
      }
      if (items.type === "timeslot") {
        this.paramsObj[items.code].value = items.options.defVal ? JSON.stringify(items.options.defVal) : "";
      }
      for (let key in this.newModulesList) {
        // console.log(key);
        this.newModulesList[key].forEach(item => {
          // console.log(item.limitKey);
          if (item.code === items.code) {
            item.options.defVal = items.options.defVal;
            item.value = items.options.defVal;
            item.errorTipShow = items.errorTipShow;
            item.errorTipText = items.errorTipText;
          }
          if (item.limitKey) {
            const keysArr = Object.keys(item.limitKey);
            let isShow = true;
            console.log(this.limitShowObj, item.limitKey);
            keysArr.forEach(element => {
              if (this.limitShowObj[element] != item.limitKey[element]) {
                isShow = false;
              }
            });
            item.isShow = isShow;
            // let currentIndex = keysArr.indexOf(items.code)
            // if (currentIndex !== -1) {
            //   item.isShow = items.options.defVal == item.limitKey[keysArr[currentIndex]];
            // }
          }
        });
      }
    },
    apply() {
      // 未立即生效参数提示显示
      // this.$emit("alertShow", true);
      // this.alertIsShow = true;
      // this.immediateShow = true;
      let arr = [];
      for (let key in this.paramsObj) {
        arr.push(this.paramsObj[key]);
      }

      let isFlag = false;
      arr.forEach(item => {
        if (item.errorTipShow) {
          isFlag = true;
        }
      });
      if (isFlag) return;

      $req
        .post(
          "/athena/configs/update",
          arr.map(item => {
            let value = item.value;
            if (value instanceof Array) {
              value = value.join(",");
            }
            return { ...item, value };
          }),
          { intercept: false },
        )
        .then(res => {
          // console.log(res);
          if (res && res.result === "success") {
            this.$message.success(this.$t("lang.common.success"));
            this.changeData = false;
            this.$emit("applySuccess", this.modulesPath, arr);
          }
        });
    },
    cancel() {
      // 按钮恢复
      this.changeData = false;
      this.paramsObj = {};
      this.$emit("changeData", this.changeData);
      this.newModulesList = JSON.parse(JSON.stringify(this.oldModulesList));
    },
  },
};
</script>

<style>
.box .gp-radio-button__inner {
  font-size: 14px !important;
}
</style>

<style lang="less" scoped>
.box {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  // height: 100%;
  margin-left: 6px;
}
.box-card {
  flex: 1;
  border-radius: 10px;
  margin-top: -5px;
  // height: calc(100% - 100px);
  padding-bottom: 6px;
  display: flex;
  flex-direction: column;
}
.new-box-card {
  // height: calc(100% - 100px);
}
.box-card /deep/.gp-card__body {
  flex: 1;
  overflow: auto;
}
.title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #323334;
}
:deep(.gp-card__header) {
  padding: 12px 20px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  border-radius: 6px;
  margin: 0px 6px 1px 16px;
}
.note {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #999ea5;
}
</style>
