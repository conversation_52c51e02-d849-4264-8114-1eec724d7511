<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div>
    <div class="detail-dialog">
      <gp-dialog
        :title="$t('lang.rms.fed.edit')"
        width="55%"
        :visible="dialogVisible"
        :close-on-click-modal="false"
        @open="showSystem"
        @close="dialogClose"
      >
        <gp-form
          ref="detailForm"
          label-position="left"
          class="demo-form-inline"
          label-width="140px"
          :model="ruleDetail"
          :rules="rules"
        >
          <gp-input v-model="ruleDetail.id" type="hidden" />
          <!-- <gp-form-item :label="$t('lang.rms.hygeia.systemCode')" prop="systemCode">
            <gp-select
              v-model="ruleDetail.systemCode"
              :placeholder="`${$t('lang.rms.fed.pleaseChoose')}${$t('lang.rms.hygeia.systemCode')}`"
            >
              <gp-option
                v-for="item in systems"
                :key="item.dictCode"
                :label="item.dictCode"
                :value="item.dictCode"
              />
            </gp-select>
            <gp-input
              v-if="ruleDetail.systemCode == 'OTHERS'"
              v-model="ruleDetail.newSystemCode"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
            />
          </gp-form-item> -->
          <gp-form-item :label="$t('lang.rms.hygeia.ruleName')" prop="ruleName">
            <gp-input v-model="ruleDetail.ruleName" :placeholder="$t('lang.rms.hygeia.inputRuleName')" />
          </gp-form-item>
          <!-- <gp-form-item :label="$t('lang.rms.hygeia.cleanType')">
            <gp-select
              v-model="ruleDetail.ruleType"
              :placeholder="$t('lang.rms.fed.pleaseChoose')"
            >
              <gp-option
                key="DELETE_ONLY"
                :label="$t('lang.rms.hygeia.directDeleteData')"
                value="DELETE_ONLY"
              />
              <gp-option
                key="TRANSFER_FILE"
                :label="$t('lang.rms.hygeia.deleteAfterArchive2File')"
                value="TRANSFER_FILE"
              />
              <gp-option
                key="TRANSFER_DB"
                :label="$t('lang.rms.hygeia.deleteAfterArchive2Database')"
                value="TRANSFER_DB"
              />
            </gp-select>
          </gp-form-item> -->
          <gp-form-item :label="$t('lang.rms.hygeia.dataLeaveDay')" prop="dataRetainDay">
            <gp-input v-model.number="ruleDetail.dataRetainDay" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.hygeia.intervalDay')" prop="schedulerIntervalDay">
            <gp-input v-model.number="ruleDetail.schedulerIntervalDay" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.hygeia.cleanTime')" prop="schedulerFixedTime">
            <gp-input v-model="ruleDetail.schedulerFixedTime" :placeholder="$t('lang.rms.hygeia.inputTimeFormatter')" />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.hygeia.maxExeMinutes')" prop="schedulerMaxTime">
            <gp-input
              v-model.number="ruleDetail.schedulerMaxTime"
              :placeholder="$t('lang.rms.hygeia.maxPercentTime')"
            />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.hygeia.ruleOrder')" prop="ruleOrder">
            <gp-input v-model.number="ruleDetail.ruleOrder" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
          </gp-form-item>
          <gp-form-item :label="$t('lang.rms.hygeia.ruleState')">
            <gp-radio v-model="ruleDetail.ruleStatus" label="enabled">{{ $t("lang.rms.fed.enable") }}</gp-radio>
            <gp-radio v-model="ruleDetail.ruleStatus" label="disabled">{{ $t("lang.rms.fed.disable") }}</gp-radio>
          </gp-form-item>
          <!-- <gp-card class="box-card">
            <div slot="header" class="clearfix">
              <span>{{ $t('lang.rms.hygeia.originDatabaseSetting') }}</span>
            </div>
            <div>
              <gp-form-item
                label-width="120px"
                prop="sourceDbInfo.host"
                :rules="{ required: true, message: $t('lang.rms.hygeia.originDatabaseIP'), trigger: 'blur' }"
              >
                <template slot="label">
                  {{ $t('lang.rms.hygeia.databaseIP') }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.sourceDbInfo.host"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databaseIP')}`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="sourceDbInfo.port"
                :rules="{ required: true, message: $t('lang.rms.hygeia.originDatabasePort'), trigger: 'blur' }"
              >
                <template slot="label">
                  {{ $t('lang.rms.hygeia.databasePort') }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.sourceDbInfo.port"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databasePort')}（mysql${$t('lang.venus.common.dict.dataDealType.default')}3306）`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="sourceDbInfo.schema"
                :rules="{ required: true, message: $t('lang.rms.hygeia.originDatabaseSchema'), trigger: 'blur' }"
              >
                <template slot="label">
                  {{ $t('lang.rms.hygeia.databaseSchema') }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.sourceDbInfo.schema"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databaseSchema')}`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="sourceDbInfo.userName"
                :rules="{ required: true, message: $t('lang.rms.hygeia.originDatabaseUserName'), trigger: 'blur' }"
              >
                <template slot="label">
                  {{ $t('lang.mb.login.userName') }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.sourceDbInfo.userName"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                  />
                </div>
              </gp-form-item>
              <gp-form-item :label="$t('lang.rms.hygeia.passwordType')">
                <gp-radio v-model="ruleDetail.sourceDbInfo.passwordType" :label="0">{{ $t('lang.rms.hygeia.passwordStore') }}</gp-radio>
                <gp-radio v-model="ruleDetail.sourceDbInfo.passwordType" :label="1">{{ $t('lang.rms.hygeia.fileStore') }}</gp-radio>
              </gp-form-item>
              <gp-form-item
                v-if="ruleDetail.sourceDbInfo.passwordType === 0"
                label-width="120px"
                prop="sourceDbInfo.password"
              >
                <template slot="label">
                  {{ $t('lang.rms.fed.password') }}
                </template>
                <div class="inputWidth">
                  <gp-input v-model="ruleDetail.sourceDbInfo.password" :placeholder="$t('lang.rms.hygeia.inputDatabasePassword')" />
                </div>
              </gp-form-item>
              <div v-if="ruleDetail.sourceDbInfo.passwordType === 1">
                <gp-form-item
                  label-width="120px"
                  prop="sourceDbInfo.passwordFile"
                >
                  <template slot="label">
                    {{ $t('lang.rms.hygeia.passwordFile') }}
                  </template>
                  <div class="inputWidth">
                    <gp-input v-model="ruleDetail.sourceDbInfo.passwordFile" :placeholder="$t('lang.rms.hygeia.inputDatabasePasswordFile')" />
                  </div>
                </gp-form-item>
              </div>
            </div>
          </gp-card> -->
          <!--          归档文件路径-->
          <gp-form-item
            v-if="ruleDetail.ruleType == 'TRANSFER_FILE'"
            :label="$t('lang.rms.hygeia.archiveFilePath')"
            prop="targetFileInfo"
          >
            <gp-input
              v-model="ruleDetail.targetFileInfo"
              :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.archiveFilePath')}`"
            />
          </gp-form-item>
          <gp-card v-if="ruleDetail.ruleType == 'TRANSFER_DB'" class="box-card">
            <div slot="header" class="clearfix">
              <span>{{ $t("lang.rms.hygeia.targetDatabaseSetting") }}</span>
            </div>
            <div>
              <gp-form-item
                label-width="120px"
                prop="targetDbInfo.host"
                :rules="{
                  required: ruleDetail.ruleType === 'TRANSFER_DB',
                  message: $t('lang.rms.hygeia.targetDatabaseIp'),
                  trigger: 'blur',
                }"
              >
                <template slot="label">
                  {{ $t("lang.rms.hygeia.databaseIP") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.host"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databaseIP')}`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="targetDbInfo.port"
                :rules="{
                  required: ruleDetail.ruleType === 'TRANSFER_DB',
                  message: $t('lang.rms.hygeia.targetDatabasePort'),
                  trigger: 'blur',
                }"
              >
                <template slot="label">
                  {{ $t("lang.rms.hygeia.databasePort") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.port"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databasePort')}（mysql${$t(
                      'lang.venus.common.dict.dataDealType.default',
                    )}3306）`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="targetDbInfo.schema"
                :rules="{
                  required: ruleDetail.ruleType === 'TRANSFER_DB',
                  message: $t('lang.rms.hygeia.targetDatabaseSchema'),
                  trigger: 'blur',
                }"
              >
                <template slot="label">
                  {{ $t("lang.rms.hygeia.databaseSchema") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.schema"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.hygeia.databaseSchema')}`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                label-width="120px"
                prop="targetDbInfo.userName"
                :rules="{
                  required: ruleDetail.ruleType === 'TRANSFER_DB',
                  message: $t('lang.rms.hygeia.targetDatabaseUserName'),
                  trigger: 'blur',
                }"
              >
                <template slot="label">
                  {{ $t("lang.rms.fed.userName") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.userName"
                    :placeholder="`${$t('lang.rms.fed.pleaseEnter')}`"
                  />
                </div>
              </gp-form-item>
              <gp-form-item :label="$t('lang.rms.hygeia.passwordType')">
                <gp-radio v-model="ruleDetail.targetDbInfo.passwordType" :label="0">{{
                  $t("lang.rms.hygeia.passwordStore")
                }}</gp-radio>
                <gp-radio v-model="ruleDetail.targetDbInfo.passwordType" :label="1">{{
                  $t("lang.rms.hygeia.fileStore")
                }}</gp-radio>
              </gp-form-item>
              <gp-form-item
                v-if="ruleDetail.targetDbInfo.passwordType === 0"
                label-width="120px"
                prop="sourceDbInfo.password"
              >
                <template slot="label">
                  {{ $t("lang.rms.fed.password") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.password"
                    :placeholder="$t('lang.rms.hygeia.inputDatabasePassword')"
                  />
                </div>
              </gp-form-item>
              <gp-form-item
                v-if="ruleDetail.targetDbInfo.passwordType === 1"
                label-width="120px"
                prop="sourceDbInfo.password"
              >
                <template slot="label">
                  {{ $t("lang.rms.hygeia.passwordFile") }}
                </template>
                <div class="inputWidth">
                  <gp-input
                    v-model="ruleDetail.targetDbInfo.passwordFile"
                    :placeholder="$t('lang.rms.hygeia.inputDatabasePasswordFile')"
                  />
                </div>
              </gp-form-item>
            </div>
          </gp-card>
          <gp-card class="box-card">
            <div slot="header" class="clearfix">
              <span>{{ $t("lang.rms.hygeia.cleanTableSetting") }}</span>
            </div>
            <div>
              <gp-table :data="ruleDetail.items" style="width: 100%" class="ruleDetailTabe">
                <gp-table-column width="250px" :label="$t('lang.rms.hygeia.tableName')">
                  <template slot="header">
                    <span><span class="red">*</span>{{ $t("lang.rms.hygeia.tableName") }}</span>
                  </template>
                  <template slot-scope="scope">
                    <gp-form-item
                      :prop="'items.' + scope.$index + '.sourceTable'"
                      :rules="[
                        { required: true, message: $t('lang.rms.hygeia.inputTableName'), trigger: ['blur', 'change'] },
                      ]"
                    >
                      <gp-input
                        v-model="scope.row.sourceTable"
                        class="mt-20"
                        :placeholder="$t('lang.rms.hygeia.inputTableName')"
                      />
                    </gp-form-item>
                  </template>
                </gp-table-column>

                <gp-table-column :label="$t('lang.rms.hygeia.timeField')">
                  <template slot="header">
                    <span><span class="red">*</span>{{ $t("lang.rms.hygeia.timeField") }}</span>
                  </template>
                  <template slot-scope="scope">
                    <gp-form-item
                      :prop="'items.' + scope.$index + '.sourceTableTimeColumn'"
                      :rules="[
                        {
                          required: true,
                          message: $t('lang.rms.hygeia.inputTableTimeColumn'),
                          trigger: ['blur', 'change'],
                        },
                      ]"
                    >
                      <gp-input
                        v-model="scope.row.sourceTableTimeColumn"
                        class="mt-20"
                        :placeholder="$t('lang.rms.hygeia.inputTableTimeColumn')"
                      />
                    </gp-form-item>
                  </template>
                </gp-table-column>

                <gp-table-column prop="ruleItemOrder" :label="$t('lang.rms.hygeia.order')" width="80">
                  <template slot-scope="scope">
                    <gp-input
                      v-model="scope.row.ruleItemOrder"
                      :placeholder="$t('lang.rms.hygeia.noticeDeleteOrder')"
                    />
                  </template>
                </gp-table-column>
                <gp-table-column fixed="right" :label="$t('lang.rms.fed.textOperation')" width="80">
                  <template slot-scope="scope">
                    <gp-button
                      v-if="scope.$index == 0"
                      type="text"
                      size="small"
                      @click="addRow(scope.row, scope.$index)"
                      >{{ $t("lang.rms.fed.add") }}</gp-button
                    >
                    <gp-button
                      v-if="scope.$index != 0"
                      type="text"
                      size="small"
                      @click.native.prevent="deleteRow(scope.$index, ruleDetail.items)"
                      >{{ $t("lang.rms.fed.delete") }}</gp-button
                    >
                  </template>
                </gp-table-column>
              </gp-table>
            </div>
          </gp-card>
          <div style="color: red">
            {{ $t("lang.rms.hygeia.notice") }}<br />
            {{ $t("lang.rms.hygeia.notRecycle") }}<br />
            {{ $t("lang.rms.hygeia.notHighBusy") }}<br />
            {{ $t("lang.rms.hygeia.baseTime") }}<br />
            {{ $t("lang.rms.hygeia.optimizeTable") }}<br />
          </div>
        </gp-form>
        <span slot="footer" class="dialog-footer">
          <gp-button @click="dialogClose">{{ $t("lang.rms.fed.cancel") }}</gp-button>
          <gp-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.save") }}</gp-button>
        </span>
      </gp-dialog>
    </div>
  </div>
</template>

<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    ruleDetail: {
      type: Object,
      default() {
        return {};
      },
    },
    dialogVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      systems: [],
      // rules: {
      //   systemCode: [
      //     { required: true, message: this.$t('lang.rms.hygeia.selectSystemCode'), trigger: ['change'] }
      //   ],
      //   ruleName: [
      //     { required: true, message: this.$t('lang.rms.hygeia.inputRuleName'), trigger: ['change'] },
      //     { min: 1, max: 60, message: this.$t('lang.rms.hygeia.one2Sixty'), trigger: ['change'] }
      //   ],
      //   dataRetainDay: [
      //     { type: 'number', required: true, message: this.$t('lang.rms.hygeia.inputLeaveDay'), trigger: 'blur' }
      //   ],
      //   schedulerIntervalDay: [
      //     { type: 'number', required: true, message: this.$t('lang.rms.hygeia.inputCleanSequence'), trigger: 'blur' }
      //   ],
      //   schedulerFixedTime: [
      //     { required: true, message: this.$t('lang.rms.hygeia.inputTimeFormatter'), trigger: 'blur' },
      //     { pattern: /^[\d]{2}:[\d]{2}:[\d]{2}$/, message: this.$t('lang.rms.hygeia.errorTimeFormatter'), trigger: 'blur' }
      //   ],
      //   schedulerMaxTime: [
      //     { type: 'number', required: true, message: this.$t('lang.rms.hygeia.maxPercentTime'), trigger: 'blur' }
      //   ],
      //   ruleOrder: [
      //     { type: 'number', required: false, message: this.$t('lang.rms.hygeia.ruleOrderIsNumber'), trigger: 'blur' }
      //   ],
      //   targetFileInfo: [
      //     { required: this.ruleDetail.ruleType === 'TRANSFER_FILE', message: this.$t('lang.rms.hygeia.inputArchiveFileName'), trigger: 'blur' }
      //   ],
      //   sourceDbInfo: {
      //     passwordFile: { required: true, message: this.$t('lang.rms.hygeia.inputPasswordFilePath'), trigger: ['blur', 'change'] }}
      // }
    };
  },
  // 监听属性 类似于data概念
  computed: {
    rules() {
      const r = {
        systemCode: [{ required: true, message: this.$t("lang.rms.hygeia.selectSystemCode"), trigger: "blur" }],
        ruleName: [
          { required: true, message: this.$t("lang.rms.hygeia.inputRuleName"), trigger: "blur" },
          { min: 1, max: 60, message: this.$t("lang.rms.hygeia.one2Sixty"), trigger: "blur" },
        ],
        dataRetainDay: [
          { type: "number", required: true, message: this.$t("lang.rms.hygeia.inputLeaveDay"), trigger: "blur" },
        ],
        schedulerIntervalDay: [
          { type: "number", required: true, message: this.$t("lang.rms.hygeia.inputCleanSequence"), trigger: "blur" },
        ],
        schedulerFixedTime: [
          { required: true, message: this.$t("lang.rms.hygeia.inputTimeFormatter"), trigger: "blur" },
          {
            pattern: /^[\d]{2}:[\d]{2}:[\d]{2}$/,
            message: this.$t("lang.rms.hygeia.errorTimeFormatter"),
            trigger: "blur",
          },
        ],
        schedulerMaxTime: [
          { type: "number", required: true, message: this.$t("lang.rms.hygeia.maxPercentTime"), trigger: "blur" },
        ],
        ruleOrder: [
          { type: "number", required: false, message: this.$t("lang.rms.hygeia.ruleOrderIsNumber"), trigger: "blur" },
        ],
        targetFileInfo: [
          {
            required: this.ruleDetail.ruleType === "TRANSFER_FILE",
            message: this.$t("lang.rms.hygeia.inputArchiveFileName"),
            trigger: "blur",
          },
        ],
        sourceDbInfo: {
          passwordFile: {
            required: true,
            message: this.$t("lang.rms.hygeia.inputPasswordFilePath"),
            trigger: ["blur", "change"],
          },
        },
      };
      return r;
    },
  },
  // 监控data中的数据变化
  watch: {},
  created() {
    console.log(this.ruleDetail);
    this.showSystem();
  },
  // 方法集合
  methods: {
    addRow(row, index) {
      // eslint-disable-next-line vue/no-mutating-props
      this.ruleDetail.items.push({
        sourceTable: "",
        timeColumn: "",
        order: 0,
      });
    },
    deleteRow(index, rows) {
      rows.splice(index, 1);
    },
    onSubmit() {
      this.$refs["detailForm"].validate(valid => {
        if (valid) {
          if (this.ruleDetail.systemCode === "OTHERS") {
            // eslint-disable-next-line vue/no-mutating-props
            this.ruleDetail.systemCode = this.ruleDetail.newSystemCode;
          }
          this.$emit("onsubmit", { ...this.ruleDetail });
        }
        // else {
        //   this.$message.error(this.$t('lang.rms.hygeia.processInputError'))
        //   return false
        // }
      });
    },
    dialogClose() {
      this.$emit("update:dialogVisible", false);
    },
    showSystem() {
      $req.get("/athena/clean/rule/systems").then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.systems = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-dialog {
  text-align: left;
}
:deep(.ruleDetailTabe .gp-form-item__content) {
  margin-left: 0px !important;
}
.red {
  color: #f56c6c;
  font-size: 15px;
  margin-right: 3px;
}
.mt-20 {
  margin-top: 20px;
}
.box-card {
  margin-bottom: 20px;
}
</style>
