import Chart, { requestCache } from "../common";

/**
 * 平均送货架时长
 */
export default class DeliveryShelfLifeCred extends Chart {
  /**
   * 初始化图表
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "平均送货架时长";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      }
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/job/sumTransportDataByStationIds', {
      startTime,
      endTime,
      stationIds: [0],
      statType: 'JOB_TRANSPORT_TIME',
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const type2Data = data.type2Data;
    const number = type2Data.deliverStage[0].toFixed(2)

    return {
      id: 'deliverStage',
      title: this.title || '',
      number: number,
      append: '秒',
      color: "#27c3c2",
    }
  }
}