<template>
  <geek-main-structure>
    <gp-tabs :value="activeName" @tab-click="tabsNavChange" ref="myTabs">
      <gp-tab-pane
        v-for="item in permissionNavList"
        :label="$t(item.text)"
        :name="item.id"
        :key="item.id"
      ></gp-tab-pane>
    </gp-tabs>
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import PalletRackManage from "./palletRackManage";
import PalletPositionManage from "./palletPositionManage";
export default {
  components: { PalletRackManage, PalletPositionManage },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabPalletRackPage",
          id: "PalletRackManage",
          text: "auth.rms.palletRackManage.palletRackManage",
        },
        {
          permissionName: "TabPalletLatticePage",
          id: "PalletPositionManage",
          text: "auth.rms.palletPositionManage.palletLatticeManage",
        },
        // {
        //   permissionName: "TabPalletLatticePage",
        //   id: "PalletSearchManage",
        //   text: "auth.rms.palletPositionManage.palletSearchManage",
        // },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "palletManage"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(target) {
      this.activeName = this.$refs.myTabs.$data.currentName;
    },
  },
};
</script>
<style lang="less" scoped>
.pallet-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>

<style lang="less">
.pallet-manage-panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);
  .pallet-manage-panel-wrap__table {
    flex: 1;
  }
}
</style>
