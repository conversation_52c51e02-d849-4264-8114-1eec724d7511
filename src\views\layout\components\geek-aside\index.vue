<template>
  <gp-aside class="geek-aside" :class="{ 'is-open': sidebarCollapse }" width="auto">
    <div class="aside-menu-container">
      <geek-aside-menu :sidebarCollapse="sidebarCollapse" @with-collapse-change="$emit('handleSidebarStatus')" />
    </div>
  </gp-aside>
</template>

<script>
import { mapState } from "vuex";
import GeekAsideMenu from "./geek-aside-menu";

export default {
  name: "GeekAside",
  components: { GeekAsideMenu },
  props: ["isShowGeekIcon", "sidebarCollapse", "sidebarHidden"],
  computed: {
    ...mapState(["menuList"]),
  },
};
</script>

<style lang="less" scoped>
@title-height: @g-header-height;
@aside-max-width: @g-slide-max-width; //240px
@aside-min-width: @g-slide-min-width; //36px

@aside-bg: @g-aside-bg;
@title-bg: @g-nav-bg;
.geek-aside {
  position: relative;
  height: calc(100%);
  // width: @aside-max-width !important;
  // flex: 0 0 @aside-max-width !important;
  background: @aside-bg;
  overflow: hidden;
  z-index: 9;
  .title {
    .g-flex();
    justify-content: flex-start;
    padding: 3px 0 0 12px;
    height: @title-height;
    .g-box-shadow-bottom(@title-bg);

    img {
      width: 90px;
      flex: 0 0 90px;
      margin-right: 16px;
    }
    .text {
      font-weight: 600;
      font-size: 22px;
      color: #2194d3;
      flex: 1;
    }
  }
  .collapse-btn {
    position: absolute;
    right: 0;
    top: 3px;
    height: 40px;
    width: 38px;
    background: url(~@imgs/layout/icon-collapse-bai.png) no-repeat 55% 50%;
    background-size: 20px;
    transform: rotate(0deg);
    transition: 0.38s;
    transform-origin: 50% 50%;
    cursor: pointer;
    z-index: 9;
  }

  .aside-menu-container {
    height: 100%;
  }
}

.geek-aside.is-open {
  .title {
    display: none;
  }
  .collapse-btn {
    transform: rotate(90deg);
  }
}
</style>
