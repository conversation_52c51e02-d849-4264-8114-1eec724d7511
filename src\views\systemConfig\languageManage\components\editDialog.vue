<template>
  <gp-dialog
    :title="title"
    :visible.sync="showDialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="540px"
    border
    center
  >
    <gp-form ref="languageForm" label-width="150px" class="ui-languageform">
      <gp-form-item :label="$t('lang.rms.fed.languagePackageFile')" prop="pass">
        <gp-upload
          ref="languageUpload"
          class="language-upload"
          action=""
          accept=".xlsx"
          :auto-upload="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :http-request="uploadLanguage"
          :file-list="fileList"
        >
          <gp-button icon="gp-icon-upload" type="primary">{{ $t("lang.rms.fed.upload") }}</gp-button>
        </gp-upload>
      </gp-form-item>
      <gp-form-item :label="$t('lang.rms.fed.languageType')" prop="checkPass">
        <gp-select v-model="languageCode">
          <gp-option
            v-for="item in languageOption"
            :key="item.languageCode"
            :value="item.languageCode"
            :label="item.languageName"
          />
        </gp-select>
      </gp-form-item>
    </gp-form>
    <div slot="footer">
      <gp-button @click="closeDialog">{{ $t("lang.common.cancel") }}</gp-button>
      <gp-button 
        type="primary"
        :loading="uploading"
        :disabled="fileList.length === 0 || !languageCode"
        @click="submit"
      >
        {{ $t("lang.rms.fed.save") }}
      </gp-button>
    </div>
  </gp-dialog>
</template>
<script>
export default {
  name: "editDialog",
  props: {
    languageOption: Array,
  },
  computed: {
    title() {
      return this.$t("lang.rms.fed.upload") + this.$t("lang.rms.fed.languagePackage")
    }
  },
  data() {
    return {
      languageCode: "",
      fileList: [],
      uploading: false,
    };
  },
  methods: {
    open() {
      this.uploading = false;
      this.fileList = [];
      this.languageCode = "";
      this.showDialog = true;
    },

    closeDialog() {
      this.uploading = false;
      this.fileList = [];
      this.languageCode = "";
      this.showDialog = false;
    },
    // 保存
    save() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const editData = this.formData;
          const newData = {
            systemCode: editData.systemCode,
            isSendEmail: editData.isSendEmail,
            isMaintenance: editData.isMaintenance,
            messageGroup: editData.messageGroup,
            delaySendEmailInterval: Number(editData.delaySendEmailInterval),
            callbackDuration: Number(editData.callbackDuration),
            exceptionDuration: Number(editData.exceptionDuration),
            status: editData.status,
            callbackDisable: editData.callbackDisable,
          };
          $req.post("/athena/fault/message/updateFaultMessage", newData).then(res => {
            if (res.code === 0) {
              this.$success(this.$t(res.msg));
              this.closeDialog();
              this.$emit("updateTableList");
            }
          });
        } else {
          return false;
        }
      });
    },
    submit() {
      this.uploading = true;
      this.$refs.languageUpload.submit();
    },
    clear() {
      this.languageCode = "";
      this.fileList = [];
    },
    uploadLanguage(params) {
      const formData = new FormData();
      formData.append("file", params.file);
      formData.append("languageCode", this.languageCode);

      $req
        .post("/athena/api/coreresource/i18n/importI18nItem", formData)
        .then(res => {
          this.uploading = false;
          if (res.code === 0) {
            this.$success(this.$t(res.msg));
            this.setLocalLang({ apiUrl: this.languageCode });
            this.clear();
          } else {
            this.$error(this.$t(res.msg));
          }
        })
        .catch(e => {
          this.uploading = false;
        });
    },
    handleChange(file, fileList) {
      this.fileList = [file];
    },
    handleRemove() {
      this.fileList = [];
    },
    setLocalLang(lang) {
      const { Data } = $utils;
      const { apiUrl } = lang;
      let localLang = Data.getLocalLang();
      if (apiUrl === localLang) {
        Data.setI18nMessage($app.$i18n, apiUrl, true);
      }
    },
  },
};
</script>
<style scoped></style>
