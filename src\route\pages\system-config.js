/* ! <AUTHOR> at 2022/09/21 */

/** 系统配置 */
export default [
  /** ************** 系统参数配置 ****************/
  {
    path: "/systemConfig/parameterConfigOuter",
    name: "parameterConfigOuter",
    meta: {
      title: "auth.rms.paramsConfigure.page",
      auth: "auth.rms.paramsConfigure.page",
      pid: "/systemConfig",
      noPermissionGuest: true,
    },
    // component: () => import("@views/systemConfig/parameterConfig"),
    component: () => import("@views/systemConfig/parameterConfigN"),
  },
  /** ************** 机器人参数配置 ****************/
  // {
  //   path: "/systemConfig/robotParamConfig",
  //   name: "robotParamConfig",
  //   meta: {
  //     title: "auth.rms.robotParamConfig.page",
  //     auth: "auth.rms.robotParamConfig.page",
  //     pid: "/systemConfig",
  //     noPermissionGuest: true,
  //   },
  //   component: () => import("@views/systemConfig/robotParameterConfig"),
  // },
  /** ************** 消息配置 ****************/
  {
    path: "/systemConfig/noticeConfig",
    name: "noticeConfig",
    meta: {
      title: "auth.rms.page.menu.noticeConfig",
      auth: "auth.rms.page.menu.noticeConfig",
      pid: "/systemConfig",
      noPermissionGuest: true,
    },
    component: () => import("@views/systemConfig/noticeConfig"),
  },
  /** ************** 语言包配置/管理 ****************/
  {
    path: "/systemConfig/languageManage",
    name: "languageManage",
    meta: {
      title: "auth.rms.i18nControllerManage.page",
      auth: "auth.rms.i18nControllerManage.page",
      pid: "/systemConfig",
      noPermissionGuest: true,
    },
    component: () => import("@views/systemConfig/languageManage"),
  },
  /** ************** 机器人事件类型 ****************/
  {
    path: "/systemConfig/robotEventType",
    name: "robotEventType",
    meta: {
      title: "auth.rms.robotEventTypeControllerManage.page",
      auth: "auth.rms.robotEventTypeControllerManage.page",
      pid: "/systemConfig",
      noPermissionGuest: true,
    },
    component: () => import("@views/systemConfig/robotEventType"),
  },
  /** ************** 急停控制器配置 ****************/
  {
    path: "/systemConfig/stopControllerManage",
    name: "stopControllerManage",
    component: () => import("@views/systemConfig/stopControllerManage"),
    meta: {
      title: "auth.rms.stopControllerManage.page",
      auth: "auth.rms.stopControllerManage.page",
      pid: "/systemConfig",
      noPermissionGuest: true,
    },
  },
];
