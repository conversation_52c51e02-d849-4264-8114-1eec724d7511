<template>
  <geek-main-structure class="software-control-panel-wrap">
    <div class="software-control-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @selectionChange="handleSelection"
        @page-change="pageChange"
        @row-add="rowAdd"
        @rows-del="handleDeleteRobotControl"
        @row-edit="rowEdit"
        @row-application="rowApplication"
      />
    </div>

    <gp-dialog
      :title="$t('lang.rms.fed.addSoftware')"
      :visible.sync="dialogFormVisible"
      width="640px"
      :before-close="handleClose"
    >
      <gp-form ref="addForm" :model="addForm" label-width="80px" label-position="top">
        <gp-row :gutter="20">
          <gp-col :span="12">
            <gp-form-item :label="$t('lang.rms.fed.edition')" prop="version">
              <!-- :placeholder="x1" -->
              <gp-input v-model="addForm.version" />
            </gp-form-item>
          </gp-col>
          <gp-col :span="12">
            <gp-form-item :label="$t('lang.rms.fed.type')" prop="type">
              <gp-select v-model="addForm.type">
                <gp-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
              </gp-select>
            </gp-form-item>
          </gp-col>
        </gp-row>
        <gp-row>
          <gp-col :span="24">
            <gp-form-item :label="$t('lang.rms.fed.describe')" prop="descr">
              <!-- :placeholder="x2" -->
              <gp-input v-model="addForm.descr" />
            </gp-form-item>
          </gp-col>
        </gp-row>
      </gp-form>
      <gp-upload
        ref="upload"
        class="upload"
        action="/athena/robot/software/upload"
        :limit="1"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :on-remove="handleRemove"
        :auto-upload="false"
        accept=".gz,.zip,.bin"
      >
        <gp-button slot="trigger" size="small" type="primary">
          {{ $t("lang.rms.fed.chooseAFile") }}
        </gp-button>
        <gp-button style="margin-left: 10px" size="small" type="success" @click="submitUpload">
          {{ $t("lang.rms.fed.upload") }}
        </gp-button>
        <div slot="tip" class="gp-upload__tip">{{ $t("lang.rms.fed.uploadOneFile") }}</div>
      </gp-upload>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="clearAddForm">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button
          type="primary"
          :disabled="addForm.version == '' || !uploadInfo.newName"
          @click="handleSubmitRobotControl"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
    <gp-dialog
      :title="$t('lang.rms.fed.chargerId') + ',' + $t('lang.rms.fed.multipleNumbersAreSeparatedByComma')"
      :visible.sync="dialogRobotVisible"
    >
      <gp-form :model="formRobot">
        <gp-form-item>
          <gp-input v-model="formRobot.ids" autocomplete="off" />
        </gp-form-item>
      </gp-form>
      <div slot="footer" class="dialog-footer">
        <gp-button @click="dialogRobotVisible = false">{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" @click="handleSubmitActive">
          {{ $t("lang.rms.fed.confirm") }}
        </gp-button>
      </div>
    </gp-dialog>
  </geek-main-structure>
</template>

<script>
export default {
  name: "SoftwareControl",
  data() {
    return {
      formRobot: {
        softwareId: "",
        ids: undefined,
      },
      addForm: {
        version: "",
        type: 0,
        descr: "",
      },
      isEdit: false,
      fileList: [],
      dialogFormVisible: false,
      dialogRobotVisible: false,

      uploadInfo: {},
      statusList: [
        {
          value: 0,
          label: this.$t("lang.rms.fed.masterControl"),
        },
      ],
      robotHeaderList: [
        {
          prop: "version",
          label: this.$t("lang.rms.fed.edition"),
        },
        {
          prop: "type",
          label: this.$t("lang.rms.fed.type"),
        },
        {
          prop: "oldName",
          label: this.$t("lang.rms.fed.fileName"),
        },

        {
          prop: "createTime",
          label: this.$t("lang.rms.fed.creationTime"),
        },
        {
          prop: "updateTime",
          label: this.$t("lang.rms.fed.updateTime"),
        },
      ],

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
        },
        actions: [
          {
            label: "lang.rms.fed.newlyAdded",
            type: "primary",
            handler: "row-add",
          },
          {
            label: "lang.rms.fed.delete",
            type: "danger",
            handler: "rows-del",
          },
        ],
        columns: [
          { label: "lang.rms.fed.edition", prop: "version", width: "80" },
          {
            label: "lang.rms.fed.type",
            prop: "type",
            width: "80",
            formatter: this.statusformatter,
          },
          { label: "lang.rms.fed.fileName", prop: "oldName" },

          { label: "lang.rms.fed.creationTime", prop: "createTime", formatter: this.timeformatter },
          { label: "lang.rms.fed.updateTime", prop: "updateTime", formatter: this.timeformatter },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.edit",
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.application",
                handler: "row-application",
              },
            ],
          },
        ],
      },
      tableSelection: [],
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.dialogFormVisible = true;
    },
    rowEdit(data) {
      this.dialogFormVisible = true;
      this.isEdit = true;
      this.addForm = {
        version: data.version || "",
        type: data.type || 0,
        descr: data.descr || "",
      };
      this.uploadInfo = data;
      this.fileList = [
        {
          name: data.newName || "",
          url: data.path,
        },
      ];
    },
    rowApplication(data) {
      this.dialogRobotVisible = true;
      this.formRobot.softwareId = data.id;
    },
    handleSelection(selections) {
      this.tableSelection = selections.map(item => item.id);
    },
    handleSubmitActive() {
      if (!this.formRobot.ids) {
        this.$message.error(this.$t("lang.rms.fed.pleaseEnterChargerId"));
        return false;
      }
      this.dialogRobotVisible = false;
      $req.post("/athena/robot/software/active", {
        softwareId: this.formRobot.softwareId,
        chargeIds: this.formRobot.ids.split(","),
      });
    },

    handleDeleteRobotControl() {
      if (this.tableSelection.length > 0) {
        $req.post("/athena/robot/software/delete", this.tableSelection).then(res => {
          this.getTableList();
        });
      } else {
        this.$tips(this.$t("lang.rms.fed.pleaseSelectOperateVersion"));
      }
    },
    handleSubmitRobotControl() {
      const self = this;
      this.uploadInfo.category = 1;
      const data = Object.assign({}, this.uploadInfo, this.addForm);
      if (this.isEdit) {
        $req.post("/athena/robot/software/update", data).then(res => {
          self.getTableList();
          self.clearAddForm();
          self.isEdit = false;
        });
      } else {
        $req.post("/athena/robot/software/add", data).then(res => {
          self.getTableList();
          self.clearAddForm();
        });
      }
    },
    clearAddForm() {
      this.dialogFormVisible = false;
      this.$refs.upload.clearFiles();
      this.addForm = {
        version: "",
        type: 0,
        descr: "",
      };

      this.uploadInfo = {};
      this.$refs["addForm"].resetFields();
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleUploadSuccess(res) {
      if (res.code !== 0) {
        this.$message.error($utils.Tools.transMsgLang(res.msg));
        this.$refs.upload.clearFiles();
        return;
      }
      this.uploadInfo = res.data;
    },
    handleRemove() {
      this.uploadInfo = {};
    },
    statusformatter(row, column) {
      return this.statusList[row[column]].label;
    },
    timeformatter(row, column) {
      return new Date(row[column])?.toLocaleString();
    },

    handleClose() {
      this.clearAddForm();
    },

    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    getTableList() {
      $req
        .get("/athena/robot/software/findAll", {
          pageSize: this.tablePage.pageSize,
          currentPage: this.tablePage.currentPage,
          category: 1,
        })
        .then(res => {
          let result = res.data || {};
          this.tableData = result.recordList;
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            total: result.recordCount || 0,
          });
        });
    },
  },
};
</script>

<style scoped>
.gp-select {
  width: 100%;
}
.btnwarp {
  padding: 0 0 30px;
}
.upload {
  width: 300px;
}
</style>
<style lang="less">
.software-control-panel-wrap {
  display: flex;
  flex-direction: column;
  // height: calc(100% -20px);
  .software-control-panel-wrap__table {
    flex: 1;
  }
}
</style>
