import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 任务量趋势图
 */
export default class SumCountByTimeListLine extends Chart {
  /**
   * 初始化图表 - 任务量趋势图
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "任务量趋势图";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      },
      {
        label: '工作站',
        prop: 'stationIds',
        type: 'gpSelect',
        optionList: [],
        defaultValue: [],
        option: {
          multiple: true
        }
      },
      {
        label: '刻度区间',
        prop: 'timeRange',
        type: 'gpSwitch',
        defaultValue: true,
        option: {
          activeText:"5分钟",
          inactiveText:"1小时"
        }
      },
    ]
  }

  async getStationList() {
    const { data } = await requestCache("/athena/station/findAll");
    const propItem = this.filterList.find(item => item.prop === 'stationIds');
    this.stationList = (data || []).map(item => {
      return {
        label: item.id,
        value: item.id
      }
    })
    this.stationList.unshift({
      label: '全部工作站',
      value: 0
    })
    propItem.optionList = this.stationList;
    
  }

  // 数据准备
  async preDataReady() {
    await this.getStationList();
  }

  async request(params) {
    const date = params?.date || [];

    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 6;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);
    let stationIds = [-1];
    
    if (params.stationIds?.length) {
      stationIds = params.stationIds;
    } 

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/job/sumCountByTimeList', {
      startTime,
      endTime,
      statType: 'JOB_COUNT',
      stationIds,
      timeRange : params.timeRange ? 5 : 60,
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData =  data.xAxis || [];
    const type2Data = data.type2Data || {};
    const { assignedCount, canceledCount, completedCount, executingCount, newCount } = type2Data;
    const { tooltipFormatterToHtml } = this;
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData.map(item => {
        return $utils.Tools.formatDate(item, "yyyy-MM-dd hh:mm:ss")
      }) },
      yAxis: { type: 'value' },
      legend: {
        data: ['未分配任务数', '取消任务数', '已分配任务数', '执行中任务数', '完成任务数']
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        formatter(params, ticket, callback) {
          const time = params[0].name;
          return tooltipFormatterToHtml(`时间: ${time}`, params)
        },
      },
      grid: {
        left: 50,
        right: 20,
        bottom: 30,
        top: 50
      },
      series: [
        { data: newCount, type: 'line', name: '未分配任务数', },
        { data: canceledCount, type: 'line', name: '取消任务数', },
        { data: assignedCount, type: 'line', name: '已分配任务数', },
        { data: executingCount, type: 'line', name: '执行中任务数', },
        { data: completedCount, type: 'line', name: '完成任务数', },
      ]
    })
  }
}