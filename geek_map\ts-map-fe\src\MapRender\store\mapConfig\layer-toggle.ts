/* ! <AUTHOR> at 2023/04/21 */

class LayerToggle {
  private configs: MRender.layerToggleOptions = {
    robotTrail: false,
    robotOccupy: false,
    cellHeat: false,
    cellLocation: false,
    mapLoad: false,
    mapUnload: false,
    shelfHeat: false,
    realtimeObstacle: false,
    robotArea: false,
    rackAbnormalLattice: false,
    rackLockedBox: false,
    roadHeat: false,
  };

  setConfig(configs: MRender.layerToggleOptions) {
    const curConfigs = this.configs;

    let nowConfigs: any = null;
    let key: keyof MRender.layerToggleOptions;
    for (key in curConfigs) {
      if (!configs.hasOwnProperty(key)) continue;
      if (!nowConfigs) nowConfigs = {};
      nowConfigs[key] = configs[key];
    }
    Object.assign(this.configs, nowConfigs);
  }

  getConfig(key?: keyof MRender.layerToggleOptions): boolean | MRender.layerToggleOptions {
    let returnData: any;
    if (key) returnData = this.configs[key];
    else returnData = this.configs;

    return returnData;
  }

  uninstall() {
    this.configs = {
      robotTrail: false,
      robotOccupy: false,
      cellHeat: false,
      cellLocation: false,
      mapLoad: false,
      mapUnload: false,
      shelfHeat: false,
      realtimeObstacle: false,
      robotArea: false,
      rackAbnormalLattice: false,
      rackLockedBox: false,
      roadHeat: false,
    };
  }

  destroy() {
    this.uninstall();
  }
}
export default LayerToggle;
