<template>
  <div class="verify-con">
    <div v-for="(v,k) in verifyInfo" class="verify-item">
      <div class="fn-id">
        <span>id：{{ k }}</span>
      </div>
      <div v-for="(item) in v" class="attr-list">
        <span>row：{{ item.index + 1 }}</span>
        <span>name：{{ item.attrName.join(",") }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "VerifyDialog",
  props: {
    verifyInfo: {
      type: Object,
      require: true,
    },
  },
};
</script>

<style scoped lang="scss">
.verify-con {
  //font-weight: 900 !important;
  .verify-item {
    span{
      display: block;
      font-weight: 900 !important;
    }
    color: #f56c6c;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 24px;
    .fn-id{

    }
  }
}
</style>
