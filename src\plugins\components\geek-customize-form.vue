<template>
  <div :class="{ 'geek-customize-form': true, 'is-searchpage': inSearchPage }">
    <!-- v-bind会自动将form的配置属性赋值过来 -->
    <gp-form
      ref="customizeForm"
      v-bind="attrs"
      :model="formData"
      :rules="rules"
      :class="{ 'form-block': attrs && !attrs.inline }"
    >
      <template v-for="(config, prop) in configs">
        <gp-form-item v-if="config.slotName" :key="prop" :prop="prop" :label="$t(config.label)" v-bind="config.attr">
          <slot :name="config.slotName" :config="config" />
        </gp-form-item>
        <div v-else-if="config.slotTitle" :key="prop" :prop="prop" :label="$t(config.label)" v-bind="config.attr">
          <slot :name="config.slotTitle" :config="config" />
        </div>
        <gp-form-item
          v-else
          v-show="!config.isHidden"
          :key="prop"
          :prop="prop"
          :label="$t(config.label)"
          v-bind="config.attr"
        >
          <!-- 文本框 类-->
          <component
            v-if="
              config.tag === 'input' ||
              config.tag === 'input-number' ||
              config.tag === 'time-picker' ||
              config.tag === 'date-picker' ||
              config.tag === 'time-select' ||
              config.tag === 'switch'
            "
            :is="'gp-' + config.tag"
            v-model="formData[prop]"
            v-bind="config.attr"
            clearable
            :placeholder="_dynamicPlaceholder(config)"
            :start-placeholder="$t(config['start-placeholder'])"
            :end-placeholder="$t(config['end-placeholder'])"
          />
          <!-- 选择框类 -->
          <gp-radio-group
            v-if="config.tag === 'radio-group'"
            v-model="formData[prop]"
            :placeholder="_dynamicPlaceholder(config)"
          >
            <gp-radio v-for="item in config.options" :key="item.value" :label="$t(item.value)">
              {{ $t(item.label) }}
            </gp-radio>
          </gp-radio-group>
          <gp-checkbox-group v-if="config.tag === 'checkbox-group'" v-model="formData[prop]">
            <gp-checkbox v-for="item in config.options" :key="item.value" :label="$t(item.value)">
              {{ $t(item.label) }}
            </gp-checkbox>
          </gp-checkbox-group>
          <gp-select
            v-if="config.tag === 'select'"
            v-model="formData[prop]"
            v-bind="config.attr"
            clearable
            :collapse-tags="config.collapseTags || false"
            :placeholder="_dynamicPlaceholder(config)"
          >
            <gp-option
              v-for="item in config.options"
              :key="item.value"
              :value="item.value"
              :label="`${item.preText ? $t(item.preText) : ''}${$t(item.label)}${
                item.appendText ? $t(item.appendText) : ''
              }`"
              :disabled="item.hasOwnProperty('disStatus') ? item.disStatus : false"
            />
          </gp-select>
        </gp-form-item>
      </template>
      <gp-form-item v-if="formConfig.operations && formConfig.operations.length > 0" class="operation-button">
        <gp-button
          v-for="item in formConfig.operations"
          v-bind="item"
          :key="item.label"
          :size="item.mini ? item.mini : 'mini'"
          :icon="item.icon ? item.icon : ''"
          :disabled="item.hasOwnProperty('disabled') ? item.disabled : false"
          @click="_operationHandler(item.handler)"
        >
          {{ $t(item.label) }}
        </gp-button>
      </gp-form-item>
    </gp-form>
  </div>
</template>
<script>
export default {
  name: "GeekCustomizeForm",
  props: {
    formConfig: {
      required: true,
      configs: {
        required: true,
      },
      operations: {
        default: () => [],
      },
    },
    inSearchPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      attrs: {},
      rules: {},
      configs: {},
      formData: {},
    };
  },

  watch: {
    formConfig: {
      deep: true,
      handler(obj) {
        this._queryFormFun(obj);
      },
    },
  },
  created() {
    this._queryFormFun(this.formConfig, true);
  },
  methods: {
    setData(formData) {
      for (let key in formData) {
        this.formData[key] = formData[key];
      }
    },
    getData() {
      return this.formData;
    },
    reset() {
      for (let key in this.formData) {
        this.formData[key] = "";
      }
      this.$refs.customizeForm.resetFields();
    },
    validate() {
      const $form = this.$refs.customizeForm;
      const formData = this.getData();
      return new Promise((resolve, reject) => {
        $form.validate(valid => {
          if (valid) {
            resolve(formData);
          } else {
            console.log("geek customizeForm error submit!!");
            return false;
          }
        });
      });
    },

    _queryFormFun(formConfig, isInit = false) {
      const attrs = formConfig.attrs || {};
      const fConfigs = formConfig.configs || {};
      const formProps = Object.keys(fConfigs);

      // attrs
      this.attrs = Object.assign({ labelPosition: "top" }, attrs);

      // configs
      const excludeKeys = ["slotName", "label", "default", "tag", "rules", "options", "placeholder"];
      let configs = {};
      formProps.forEach(prop => {
        let item = fConfigs[prop];
        let attr = {};
        for (let key in item) {
          if (excludeKeys.includes(key)) continue;
          attr[key] = item[key];
        }
        configs[prop] = Object.assign({}, item, { attr });
      });
      this.configs = configs;

      if (isInit) {
        // rules、formData
        let rules = {};
        let formData = {};
        formProps.forEach(prop => {
          const item = fConfigs[prop];
          const rule = item.rules;
          if (rule) rules[prop] = rule;
          formData[prop] = item.default;
        });
        this.rules = rules;
        this.formData = formData;
      }
    },

    _dynamicPlaceholder(config) {
      let placeHolderLang = config.placeholder;
      if (placeHolderLang) return this.$t(placeHolderLang);

      if (
        config.tag === "select" ||
        config.tag === "time-picker" ||
        config.tag === "date-picker" ||
        config.tag === "time-select"
      ) {
        placeHolderLang = "lang.rms.fed.pleaseChoose";
      } else {
        placeHolderLang = "lang.rms.fed.pleaseEnterContent";
      }
      return this.$t(placeHolderLang);
    },
    _operationHandler(handler) {
      if (handler === "on-reset") {
        this.reset();
      }
      this.$emit(handler, this.formData);
    },
  },
};
</script>

<style lang="less">
.geek-customize-form {
  &.is-searchpage {
    padding-bottom: 5px;
  }
  .gp-form-item {
    margin-bottom: 6px;
    vertical-align: bottom;
  }
  .gp-form-item__label {
    flex-shrink: 0;
  }

  .gp-date-editor.gp-input {
    width: 100%;
  }

  .form-block {
    .gp-form-item {
      .g-flex();
      justify-content: flex-start;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    // .gp-form-item__label {
    //   font-weight: 600;
    //   line-height: 1.3;
    // }
    .gp-form-item__content {
      width: 100%;
      margin-left: 5px !important;
      .gp-input-number--small,
      .gp-select {
        width: 100%;
      }
    }
  }

  .operation-button {
    margin-bottom: 8px;
    vertical-align: bottom;
    min-width: 188px;
  }
}
</style>
