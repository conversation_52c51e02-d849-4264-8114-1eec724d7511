import Chart, { requestCache } from "../common";

/**
 * 2.3.5路径运行平均速度
 */
export default class AverageSpeedCred extends Chart {
  /**
   * 初始化图表 - 2.3.5路径运行平均速度
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "路径运行平均速度";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'gpDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        valType: 'timeStamp',
        option: {
          type: "datetimerange"
        }
      }
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/path/plan/stat/sumByDay', {
      startTime,
      endTime,
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const defData = data.DEFAULT.DEFAULT.DEFAULT.DEFAULT || {};

    let number = 0;
    const keys = Object.keys(defData);
    keys.forEach(itemKey => {
      number += (defData[itemKey].pathAverageSpeed || 0);
    });

    const pathAverageSpeed = number / keys.length;

    return {
      id: 'averageSpeedCred',
      title: this.title || '',
      number: pathAverageSpeed,
      append: '秒',
      color: "#8543e0",
    }
  }
}