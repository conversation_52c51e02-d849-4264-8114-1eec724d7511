let dashRequestTemporaryStorage = [];

/**
* 请求是否读缓存, 为了减少请求次数
* @param {*} url 
* @param {*} params 
* @returns 
*/
export async function requestCache(url, params) {
  // 清除10秒外的请求缓存
  const curTime = + new Date;
  dashRequestTemporaryStorage = dashRequestTemporaryStorage.filter(item => curTime - item.time < 10000);

  const paramsTxt = JSON.stringify(params);
  const filterItem = dashRequestTemporaryStorage.find(item => {
    return url === item.url && paramsTxt === item.params;
  });

  if (filterItem) {
    return await new Promise((resolve) => {
      if (filterItem.wait) {
        filterItem.waitHandelList.push(resolve);
      } else {
        resolve(filterItem.data);
      }
    });
  }

  const tsData = { url, params: paramsTxt, data: null, time: (+ new Date), wait: true, waitHandelList: [] }
  dashRequestTemporaryStorage.push(tsData);

  // 先搜索
  try {
    const result = await $req.post(url, params, { intercept: false });
    // 缓存
    tsData.wait = false;
    tsData.data = result;
    tsData.waitHandelList.forEach(item => item(result));
    return result;
  } catch (error) {
    throw error
  }
}

export const parseEchartOption = (options) => {
  // 如果options.series中数据大于30条, 增加dataZoom
  if (options.series && options.series.length) {
    const seriesLen = options.series[0].data.length;
    if (seriesLen > 30) {
      options.dataZoom = [
        {
          type: "slider",
          xAxisIndex: 0,
          filterMode: "none",
        },
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "none",
        },
      ];
    }
  }

  // 如果没有tooltip, 增加tooltip
  if (!options.tooltip) {
    options.tooltip = {
      trigger: 'axis',
      confine: true,
      extraCssText: 'overflow-y:auto;max-height:500px;pointer-events:auto !important;z-index: 2',
    }
  }

  return options;
}

export default class Chart {
  constructor(type, option) {
    this.type = type;
    this.x = option.x;
    this.y = option.y;
    this.width = option.width;
    this.height = option.height;
    this.intervalTimer = option.intervalTimer || 5 * 60000;
    this.filterList = [];
  }

  // 请求前对filterList的检查
  async requestChartData(params = {}) {
    const { filterList } = this;

    const handlerOption = {};
    filterList.forEach(filterItem => {
      if (filterItem.prop === 'date') {
        const dateFilterType = filterItem.option?.type;
        const valType = filterItem.valType; // 时间戳 timeStamp
        if (Array.isArray(params.date) && dateFilterType !== 'datetimerange') {
          if (valType === 'timeStamp') {
            handlerOption.date = +new Date(params.date[0]);
          } else {
            handlerOption.date = $utils.Tools.formatDate(params.date[0], valType);
          }
        }
        if (!Array.isArray(params.date) && dateFilterType === 'datetimerange') {
          // 当天的0点和24点
          handlerOption.date = [new Date(params.date).setHours(0, 0, 0, 0), new Date(params.date).setHours(23, 59, 59, 0)];
          if (valType !== 'timeStamp') {
            handlerOption.date = handlerOption.date.map(item => $utils.Tools.formatDate(item, valType));
          }
        }
      }
    });

    debugger;
    // const requestData = {};
    // if (isFilterParams) {
    //   (filterList || []).forEach(filterKey => {
    //     const value = params[filterKey];
    //     if (!value) return;
  
    //     switch (filterKey) {
    //       case 'date':
    //         requestData[filterKey] = $utils.Tools.formatDate(value, "yyyy-MM-dd");
    //         break;
    //       default:
    //         requestData[filterKey] = value;
    //         break;
    //     }
    //   })
    // } else {
    //   return params;
    // }
    
    return { ...(params || {}), ...handlerOption };
  }

  async preDataReady() { return {} }

  // 请求及请求的处理
  async request(params) {
    return {};
  }

  // 地图数据处理
  async handler(params) {
    return {};
  }

  tooltipFormatterToHtml(title, params) {
    return [
      title,
      ...params.map(item => {
        return [
          `<span style="width: 11px; height: 11px; background: ${item.color}; border-radius: 12px; margin-right: 5px; display: inline-block;"></span>`,
          `<span>${item.seriesName}: ${item.value}</span>`
        ].join('');
      })
    ].join('<br />');
  }

  sortChartDataAt0(option) {
    const list = Object.entries(option)
    list.sort((itemA, itemB) => {
      if (itemB[0] === 'OVERTIME') return 1;
      if (itemB[0] === 'PROCESSING') return -1;
      return itemA[0] - itemB[0]
    })

    const xAxisData = list.map(item => item[0]);
    const seriesData = list.map(item => item[1]);
    return { xAxisData, seriesData }
  }
}