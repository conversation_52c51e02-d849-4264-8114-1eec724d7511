<template>
  <!-- 编辑任务 -->
  <gp-form
    class="mform"
    ref="mformRef"
    label-position="right"
    label-width="140px"
    size="mini"
    :model="editTaskData"
    :rules="editTaskRules"
  >
    <gp-form-item :label="$t('lang.rms.fed.shapedShelfModelName')" prop="modelName">
      <gp-input class="w200" v-model="editTaskData.modelName" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 外部编号 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="editDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- sizeType -->
    <gp-form-item prop="sizeTypes" :label="$t('lang.rms.fed.supportedSizeType')">
      <sizeTypeInput :value.sync="editTaskData.sizeTypes" @change="sizeTypesChange" />
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.web.container.supportMove')">
      <gp-select class="w200" v-model="editTaskData.move" :placeholder="$t('lang.rms.fed.choose')">
        <gp-option :label="$t('lang.rms.web.container.canNotMove')" :value="0" />
        <gp-option :label="$t('lang.rms.web.container.canMove')" :value="1" />
      </gp-select>
    </gp-form-item>
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        :placeholder="$t('lang.rms.fed.choose')"
        @change="needSendRobotChange"
      >
        <gp-option :label="$t('lang.rms.fed.no')" :value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" :value="1" />
      </gp-select>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.containerManage.sendModelId.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text"><gp-icon name="gp-icon-question" /></gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="editDisabled || String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>
    <div class="modelTitle">{{ $t("lang.rms.fed.shelfSize") }}</div>
    <gp-row>
      <gp-col :span="8">
        <gp-form-item prop="width" :label="`${$t('lang.rms.fed.length')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.width"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="length" :label="`${$t('lang.rms.fed.width')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.length"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="height" :label="`${$t('lang.rms.fed.shelfHeight')}(mm)`" label-width="110px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.height"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
    </gp-row>
    <div class="modelTitle">{{ $t("lang.rms.fed.shelfFoot") }}</div>
    <div v-for="(item, index) in editTaskData.extendJson.legs" :key="index">
      <div class="delLegsItem">
        {{ $t("lang.rms.fed.shelfFoot") }}{{ index + 1 }}
        <gp-icon name="gp-icon-delete" class="delLegsIcon" @click="delLegs(index)"></gp-icon>
      </div>
      <gp-row>
        <gp-col :span="8">
          <gp-form-item
            :prop="`extendJson.legs[${index}].width`"
            :label="`${$t('lang.rms.fed.length')}(mm)`"
            label-width="110px"
          >
            <gp-input-number step-strictly size="mini" v-model="item.width" :min="1" :max="9000000"></gp-input-number>
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item
            :prop="`extendJson.legs[${index}].length`"
            :label="`${$t('lang.rms.fed.width')}(mm)`"
            label-width="110px"
          >
            <gp-input-number step-strictly size="mini" v-model="item.length" :min="1" :max="9000000"></gp-input-number>
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item
            :prop="`extendJson.legs[${index}].substructureHeight`"
            :label="$t('lang.rms.fed.baseHeight')"
            label-width="110px"
          >
            <gp-input-number
              step-strictly
              size="mini"
              v-model="item.substructureHeight"
              :min="1"
              :max="9000000"
            ></gp-input-number>
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item
            :prop="`extendJson.legs[${index}].rcX`"
            :label="$t('lang.rms.fed.coordinateFromXCenter')"
            label-width="110px"
          >
            <gp-input-number
              step-strictly
              size="mini"
              v-model="item.rcX"
              :min="-9000000"
              :max="9000000"
            ></gp-input-number>
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item
            :prop="`extendJson.legs[${index}].rcY`"
            :label="$t('lang.rms.fed.coordinateFromYCenter')"
            label-width="110px"
          >
            <gp-input-number
              step-strictly
              size="mini"
              v-model="item.rcY"
              :min="-9000000"
              :max="9000000"
            ></gp-input-number>
          </gp-form-item>
        </gp-col>
        <gp-col :span="8">
          <gp-form-item :prop="`extendJson.legs[${index}].isParams0`" :label="$t('是否需要识别')" label-width="110px">
            <gp-switch v-model="item.isParams0" />
          </gp-form-item>
        </gp-col>
      </gp-row>
    </div>
    <div>
      <gp-button type="primary" class="addLegsBtn" @click="addLegs"
        >{{ $t("lang.rms.fed.add") }}{{ $t("lang.rms.fed.shelfFoot") }}</gp-button
      >
    </div>
  </gp-form>
</template>

<script>
import { mapMutations, mapState, mapActions } from "vuex";
import sizeTypeInput from "./sizeTypeInput.vue";
const getDefaultData = () => {
  return {
    width: 100, // 宽
    length: 100, // 长
    height: 10, // 高
    modelType: "",
    sizeTypes: "",
    move: 0,
    modelName: "",
    categoryId: "",
    sendModelId: 0,
    needSendRobot: 0,
    modelCategory: "HETEROTYPE_SHELF",
    extendJson: {
      legs: [
        {
          length: 1, // 长
          width: 1, // 宽
          rcX: 0, // 距离中心X轴坐标
          rcY: 0, // Y轴坐标
          substructureHeight: 1, // 底座高度
        },
      ],
    },
  };
};

export default {
  data() {
    return {
      // 编辑任务数据
      editTaskData: getDefaultData(),
      // loading
      saveLoading: false,
      sizeTypeParamTip: null,
    };
  },
  components: {
    sizeTypeInput,
  },
  computed: {
    ...mapState("containerModal", ["shelfCategoryDict", "editData"]),
    editDisabled() {
      return Number(this.editData?.builtIn) === 1;
    },

    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };

      const rule = {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        // sizeTypes: [requiredRule],
        modelType: [requiredRule],
        width: [requiredRule],
        length: [requiredRule],
        height: [requiredRule],
      };

      this.editTaskData.extendJson.legs.forEach((item, index) => {
        rule[`extendJson.legs[${index}].width`] = [requiredRule];
        rule[`extendJson.legs[${index}].length`] = [requiredRule];
        rule[`extendJson.legs[${index}].substructureHeight`] = [requiredRule];
        rule[`extendJson.legs[${index}].rcX`] = [requiredRule];
        rule[`extendJson.legs[${index}].rcY`] = [requiredRule];
      });

      return rule;
    },
  },
  created() {
    this.fetchShelfCategory();
    if (this.editData.id) {
      this.editTaskData = JSON.parse(JSON.stringify(this.editData));
    }
  },
  watch: {
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
    "editTaskData.sizeTypes"(val) {
      let valList = val ? val.split(",") : [];
      if (val) this.sizeTypesChange(valList);
    },
  },
  methods: {
    ...mapActions("containerModal", ["fetchShelfCategory"]),
    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null;
        return;
      }
      const reg = /^[a-zA-Z]{0,15}$/;
      if (data) {
        data.forEach(item => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null;
          } else {
            this.sizeTypeParamTip = this.$t("lang.rms.fed.enterEnglish15Characters");
          }
        });
      }
    },
    addLegs() {
      this.editTaskData.extendJson.legs.push({
        length: 1, // 长
        width: 1, // 宽
        rcX: 0, // 距离中心X轴坐标
        rcY: 0, // Y轴坐标
        substructureHeight: 1, // 底座高度
      });
    },
    delLegs(index) {
      const legList = this.editTaskData.extendJson.legs;
      legList.splice(index, 1);
    },

    async validateData() {
      try {
        await this.$refs.mformRef.validate();
        return this.editTaskData;
      } catch (error) {
        return false;
      }
    },
    needSendRobotChange(value) {
      if (value) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.mform {
  flex: 1;
  position: relative;
  overflow: auto;
  padding-bottom: 10px;
}

.editTask {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  width: 350px;
}

.sizeType {
  min-width: 200px;
  max-width: 100%;
}

.floor {
  text-align: center;
}

:deep(.gp-radio) {
  margin-right: 15px;
}

.modelTitle {
  font-weight: 600;
  height: 26px;
  margin: 5px 0;
  text-align: left;
  font-size: 14px;
  padding: 3px 12px;
  background: #eee;
}

.addLegsBtn {
  width: 200px;
}

.delLegsItem {
  position: relative;
  font-size: 16px;
  font-weight: 900;
  padding-bottom: 10px;

  .delLegsIcon {
    margin-left: 10px;
    color: #f56c6c;
    cursor: pointer;
  }
}
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}

.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
