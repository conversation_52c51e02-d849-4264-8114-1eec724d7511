<template>
  <span class="shrinkHandle" @click="trigger">
    <span v-if="isExtend" class="mapFont map-font-shouqikuaijin"></span>
    <span v-else class="mapFont map-font-zhankai"></span>
  </span>
  <div class="attrPanel" :class="{ isExtend, unExtend: !isExtend }">
    <!-- 设备编辑, 设备中的电梯会导致多选 -->
    <template v-if="isDevice">
      <PPPStationPanel
        v-if="resData && isPPPStation"
        ref="nodeRef"
        :title="curTitle"
        :curData="resData"
        @trigger="trigger"
      />
      <XDevicePanel v-if="resData && isXDevice" :title="curTitle" :curData="resData" @trigger="trigger" />
    </template>
    <!-- 如果没有 -->
    <template v-else>
      <InfoPanel />
    </template>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAttrStore } from "@packages/store/attr";
import { ref, Ref, watch, computed, ComputedRef } from "vue";
import InfoPanel from "./components/info/index.vue";
import PPPStationPanel from "./components/pppStation/index.vue";
import XDevicePanel from "./components/xDevice/index.vue";
import { triggerEventListener } from "@packages/hook/useEvent";

import { getComponentStore } from "@packages/store/piniaSync";
import {
  NODE_DEVICE,
  NODE_TYPEKEY_MAP,
  DEVICE_STATION,
  DEVICE_CONVEYOR,
  DEVICE_FORK,
  DEVICE_LIFT,
  DEVICE_STACKER,
  DEVICE_MOVE_FORK,
} from "@packages/configure/dict/nodeType";

import { useAppStore } from "@packages/store/app";

import { findPppStation, queryChildDeviceInfo } from "@packages/api/map";
import { useEditMap } from "@packages/hook/useEdit";

const appStore = useAppStore();
const attrStore = storeToRefs(useAttrStore());
let resData: Ref<any> = ref(null);

const curNodeType: ComputedRef<string | "undefined"> = computed(() => {
  return attrStore.curNodeTypeByIndex.value || "";
});

/**
 * 是否是一个设备
 */
const isDevice: ComputedRef<boolean> = computed(() => {
  console.log();
  return curNodeType.value === NODE_DEVICE;
});

/**
 * 是否PPP工作站
 */

const isPPPStation: ComputedRef<boolean> = computed(() => {
  if (!isDevice.value) return false;
  const curSelectNode = attrStore.curNodeDataByIndex.value || {};
  // 这里使用any是因为ts无法识别curSelectNode中包含了NODE_TYPEKEY_MAP[curNodeType.value], 实际上始终是包含的
  const type = (curSelectNode as any)[NODE_TYPEKEY_MAP[curNodeType.value]]; //设备类型为 工作站 STATION
  const stationLayout = (curSelectNode as any)["type"]; //7是 PPP工作站
  return type === DEVICE_STATION && stationLayout === 7;
});

/**
 * 是否 输送线
 */

const isXDevice: ComputedRef<boolean> = computed(() => {
  if (!isDevice.value) return false;

  const curSelectNode = attrStore.curNodeDataByIndex.value || {};
  console.log("curSelectNode", curSelectNode);

  const type = (curSelectNode as any)[NODE_TYPEKEY_MAP[curNodeType.value]]; //设备类型为 工作站 STATION
  const deviceType = (curSelectNode as any)["deviceType"]; //7是 PPP工作站
  return (
    deviceType === DEVICE_CONVEYOR
    // ||
    // deviceType === DEVICE_FORK ||
    // deviceType === DEVICE_LIFT ||
    // deviceType === DEVICE_STACKER ||
    // deviceType === DEVICE_MOVE_FORK
  );
});

const getFormData = async () => {
  const curSelectNode = attrStore.curNodeDataByIndex.value || {};
  if (isPPPStation.value) {
    const stationId = (curSelectNode as any)["stationId"];
    //如果是 pppStation 请求一个接口撒
    const res = await findPppStation({
      mapId: appStore.mapId,
      stationId,
    });

    if (res?.code === 0) {
      resData.value = res.data;
    }
  }
  //四向车
  if (isXDevice.value) {
    const deviceCode = (curSelectNode as any)["deviceCode"];
    const cellCode = (curSelectNode as any)["cellCode"];

    //如果是 四向车设备 请求一个接口撒
    const res = await queryChildDeviceInfo({
      mapId: appStore.mapId,
      floorId: appStore.floorId,
      deviceCode,
      cellCode,
    });

    if (res?.code === 0) {
      res.data.cellCode = cellCode;
      resData.value = res.data;
    }
    console.log("四向车curSelectNode ", curSelectNode);
  }
};

const curTitle: ComputedRef<string> = computed(() => {
  if (isPPPStation.value) return "lang.rms.map.deviceEdit.pppStationConfig";
  if (isXDevice.value) {
    const curSelectNode = attrStore.curNodeDataByIndex.value || {};

    const deviceType = (curSelectNode as any)["deviceType"]; //7是 PPP工作站

    switch (deviceType) {
      case DEVICE_CONVEYOR:
        return "lang.rms.map.deviceEdit.conveyor";
      case DEVICE_FORK:
        return "lang.rms.map.deviceEdit.fork";
      case DEVICE_LIFT:
        return "lang.rms.map.deviceEdit.lift";
      case DEVICE_MOVE_FORK:
        return "lang.rms.map.deviceEdit.moveFork";
      case DEVICE_STACKER:
        return "lang.rms.map.deviceEdit.stacker";
      default:
        return "";
    }
  }

  return "";
});

// 展开与收起
const isExtend = ref(true);

function trigger() {
  if (isExtend.value) {
    isExtend.value = false;
    const editMap = useEditMap();
    editMap.value?.resetAllSelected();
  } else {
    isExtend.value = true;
  }
  setTimeout(() => {
    triggerEventListener("map:resize");
  }, 210);
}

// 当前选择的元素改变, 则自动展开
watch(
  attrStore.curNodeTypeByIndex,
  node => {
    if (
      attrStore.selectNodes.value.length === 1 &&
      !attrStore.isMultipleSelectMode.value &&
      !attrStore.isSingleSelectMode.value &&
      !isExtend.value
    ) {
      isExtend.value = true;
    }
    //获取设备相关的 DATA
    getFormData();

    setTimeout(() => {
      triggerEventListener("map:resize");
    }, 50);
  },
  {
    deep: true,
  },
);
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 4px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
  position: relative;
  transition: all 0.2s;

  &.unExtend {
    max-width: 0px;
  }

  &.isExtend {
    max-width: 600px;
  }
}

.shrinkHandle {
  position: absolute;
  width: 20px;
  height: 30px;
  border: 1px solid #409eff;
  border-radius: 4px;
  background: #fff;
  top: 50%;
  left: -16px;
  transform: translate(0, -50%);
  border-right: none;
  z-index: 2;
  font-size: 0;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  color: #409eff;
}
</style>

<style lang="scss">
.attrPanel {
  .attr-tabs {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__content {
      padding-top: 10px;
      padding-bottom: 30px;
    }
  }
}
</style>
