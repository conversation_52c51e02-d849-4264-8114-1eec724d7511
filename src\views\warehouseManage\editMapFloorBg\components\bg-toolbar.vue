<template>
  <div class="toolbar">
    <!-- 5.6.x 版本禁用了底图编辑功能 -->
    <!-- <template v-for="(item, index) in toolbar">
      <span
        v-show="exportBgShow"
        :key="item.name"
        class="toolbar-item"
        :class="[
          item.icon,
          item.unselectedClass,
          {
            'is-active': index === currentActive && !item.unselectedStatus,
            dis: item.unselectedStatus && !hasOperateHistory,
          },
        ]"
        @click.stop="toolbarSelect(index, item)"
      >
        {{ $t(item.lang) }}
      </span>
    </template> -->
    <gp-button v-show="exportBgShow" type="primary" size="mini" class="save-btn" @click.stop="$emit('updateBgImage')">
      {{ $t("lang.rms.fed.uploadDisplayImage") }}
    </gp-button>
    <gp-button v-show="exportBgShow" type="primary" size="mini" class="" @click.stop="$emit('change-bg')">
      {{ $t("lang.rms.fed.updateResource") }}
    </gp-button>
    <gp-button v-show="exportBgShow" type="primary" size="mini" @click.stop="$emit('export')">
      {{ $t("lang.rms.fed.exportResource") }}
    </gp-button>
    <gp-button type="primary" size="mini" :class="exportBgShow ? '' : 'save-btn'" @click.stop="$emit('save')">
      {{ $t("lang.rms.fed.save") }}
    </gp-button>
    <gp-button type="primary" size="mini" @click.stop="$emit('exit')">
      {{ $t("lang.rms.fed.exit") }}
    </gp-button>
  </div>
</template>

<script>
export default {
  name: "BgToolbar",
  props: {
    hasOperateHistory: {
      type: Boolean,
      default: false,
    },
    exportBgShow: {
      type: Boolean,
      default: false,
    },
    currentActiveInit: {
      type: String,
    },
  },
  data() {
    return {
      currentActive: 0,
      toolbar: [
        {
          name: "移动",
          lang: "lang.rms.fed.movement",
          action: "movement",
        },
        {
          name: "旋转",
          lang: "lang.rms.fed.rotate",
          action: "rotate",
        },
        {
          name: "撤销",
          lang: "lang.rms.fed.revoke",
          action: "revoke",
          unselectedClass: "unselected",
          unselectedStatus: true,
        },
        {
          name: "墙",
          lang: "lang.rms.fed.wall",
          action: "wall",
        },
        {
          name: "地面",
          lang: "lang.rms.fed.ground",
          action: "ground",
        },
        {
          name: "尺子",
          lang: "lang.rms.fed.ruler",
          action: "ruler",
          icon: "ruler-icon",
        },
      ],
    };
  },
  computed: {},
  watch: {
    currentActiveInit(val) {
      this.currentActive = 0;
    },
  },
  activated() {
    Object.assign(this.$data, this.$options.data());
  },
  methods: {
    toolbarSelect(index, item) {
      if (item.unselectedStatus) {
        // this.currentActive = -1;
        this.$emit("change", item.action);
      } else {
        if (this.currentActive !== index) {
          this.currentActive = index;
          this.$emit("change", item.action);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.toolbar {
  .g-flex();
  justify-content: flex-start;
  height: 40px;
  padding: 0 20px;
  border-bottom: 1px solid #eee;

  > .toolbar-item {
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 15px;
    padding: 5px 14px;
    margin-right: 12px;
    cursor: pointer;

    &.ruler-icon {
      padding-left: 30px;
      background: url(~@imgs/monitor/bg-edit-ruler.png) no-repeat 10px 50%;
      background-size: 16px;
    }

    &.is-active {
      color: #3a8ee6;
      border-color: #3a8ee6;
    }

    &.unselected {
      &:active {
        background: #d6eafb;
        color: rgb(64, 158, 255);
      }

      &.dis {
        cursor: not-allowed;
        color: #ddd;
        border-color: #ddd;

        &:active {
          color: #ddd;
          border-color: #ddd;
          background: unset;
        }
      }
    }
  }

  > .save-btn {
    font-size: 13px;
    margin-left: auto;
    text-align: right;
    background: #409eff;
    color: #fff;
    border-radius: 3px;
    padding: 0 10px 1px;
    cursor: pointer;

    &:active {
      background: #147bf3;
    }
  }
}

.textRight {
  flex: 1;
  text-align: right;
}
</style>
