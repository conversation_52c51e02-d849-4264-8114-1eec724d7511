/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled } from "@ant-design/icons";
import { Drawer, Button, Input, Popover, Row, Col } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import ListCurrent from "./list-current";
import StationDetail from "./station-detail";
import ListTargetHandle from "./list-target-handle";
import ListTargetPoppickShelf from "./list-target-poppick-shelf";
import ListTargetCurrentStation from "./list-target-current-station";

const { Search } = Input;
type PropsOrderData = { isCurrent: boolean };
type operationType = "updateShelf" | "moveShelf" | "angleShelf" | "updateBox";
type SearchDataType = { type: "box"; value: string; subCode?: code };

let clickTimes = 0; // 点击货架次数
function OrderStation(props: PropsOrderData) {
  const { t } = useTranslation();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [stationClickTimes, setStationClickTimes] = useState<number>(0);
  const [stationId, setStationId] = useState("");
  const [stationData, setStationData] = useState<stationData>(null);
  const [operation, setOperation] = useState<operationType>(undefined);
  const [targetType, setTargetType] = useState<"toStation" | "toShelf" | "">("");
  const [shelfType, setShelfType] = useState<"poppick">("poppick");
  const [shelfCode, setShelfCode] = useState<string>("");

  const [currentSelect, setCurrentSelect] = useState<any>(null);
  const [rackCode, setRackCode] = useState<string>("");
  const [currentRack, setCurrentRack] = useState<rackData>(null);
  const [targetRack, setTargetRack] = useState<rackData>(null);
  const [searchData, setSearchData] = useState<SearchDataType>(null);

  const [targetShelf, setTargetShelf] = useState<shelfData>(null);
  const [isCurrent, setIsCurrent] = useState(true);
  const [targetSelect, setTargetSelect] = useState<code>(""); // target select latticeCode
  const [chooseTargetType, setChooseTargetType] = useState<boolean>(false);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      const layer = params?.layer;
      switch (layer) {
        case "station":
          if (data) {
            setStationData(data);
          } else {
            setStationData(null);
            setDrawerOpen(false);
          }
          break;
        case "poppick":
          console.log(props.isCurrent, wsData);
          setShelfType(layer);
          if (data) setTargetShelf(data);
          else setTargetShelf(null);
          break;
      }
    });

    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent]);

  // 地图点击
  useEffect(() => {
    if (!props.isCurrent) return;

    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      let code: string;
      const map2D = getMap2D();
      switch (data?.layer) {
        case "station":
          code = data.code.toString();
          setStationData(null);
          setStationId(code);
          setClickTimes();

          map2D.mapWorker.reqQuery({ layer: "station", code });
          break;
        case "poppick":
          code = data.code.toString();
          setShelfType(data?.layer);
          setTargetShelf(null);

          setClickTimes();
          map2D.mapWorker.reqQuery({ layer: "poppick", code });
          break;
      }
    });

    return () => {
      $eventBus.off("mapClick");
    };
  }, [props.isCurrent]);

  // 清空数据 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;

    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["station"]);
    return () => {
      clearStation();
      setDrawerOpen(false);
      setClickTimes(0);
    };
  }, [props.isCurrent]);

  useEffect(() => {
    const parkList = stationData?.parkList || [];
    if (!parkList.length) setDrawerOpen(false);
    else setDrawerOpen(true);
  }, [stationClickTimes]);

  useEffect(() => {
    if (!props.isCurrent) return;
    const code: any = currentRack?.rackCode || "";
    if (code !== rackCode) setRackCode(code);
  }, [currentRack?.rackCode]);

  const controlHandler = (cmd: string) => {
    switch (cmd) {
      case "lockLattice": // 锁定货位
        latticeOperateFn("LOCK_LATTICE");
        return;
      case "unlockLattice": // 解锁货位
        latticeOperateFn("UNLOCK_LATTICE");
        return;
      case "lockBox": // 锁定货箱
        boxOperateFn("LOCK_BOX");
        return;
      case "unlockBox": // 解锁货箱
        boxOperateFn("UNLOCK_BOX");
        return;

      case "toStation": // 更新货箱到 当前工作站
        setOperation("updateBox");
        setTargetType("toStation");
        isCurrentStation(true);
        break;
      case "toShelf": // 更新货箱 到货架
        setOperation("updateBox");
        setTargetType("toShelf");
        isCurrentStation(false);
        break;
    }
  };

  // 货位操作: 锁定/解锁
  const latticeOperateFn = (instruction: string) => {
    if (!["lattice", "box"].includes(currentSelect?.type)) return;

    const map2D = getMap2D();
    const msgType = "BoxInstructionRequestMsg";
    map2D.mapWorker.reqSocket(msgType, { instruction, latticeCodeList: currentSelect.latticeCodes }).then(res => {
      if (res.msgType !== "BoxInstructionResponseMsg") return;
      _$utils.wsCmdResponse(res?.body || {});
    });
    setOperation(undefined);
    setTargetType("");
    isCurrentStation(true);
  };
  // 货箱操作: 锁定/解锁
  const boxOperateFn = (instruction: string) => {
    if (currentSelect?.type !== "box") {
      return;
    }
    const map2D = getMap2D();
    const msgType = "WarehouseInstructionRequestMsg";
    map2D.mapWorker.reqSocket(msgType, { instruction, boxCode: currentSelect.boxCode }).then(res => {
      if (res.msgType !== "WarehouseInstructionResponseMsg") return;
      _$utils.wsCmdResponse(res?.body || {});
    });
    setOperation(undefined);
    setTargetType("");
    isCurrentStation(true);
  };

  const setClickTimes = (n?: number) => {
    clickTimes += 1;
    if (clickTimes > 10) {
      clickTimes = 1;
    }
    if (n === 0) {
      clickTimes = 0;
    }
    setStationClickTimes(clickTimes);
  };

  // 工作站清除
  const clearStation = () => {
    setStationId("");
    setStationData(null);
    setCurrentSelect(null);
    setDrawerOpen(false);
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
  };

  const handleOpenChange = (newOpen: boolean) => {
    setChooseTargetType(newOpen);
  };

  const stationSearch = (value: string) => {
    if (!value) return;
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "station", code: value });
    map2D.mapRender.trigger("click", { station: [value] });
    map2D.mapRender.setEleCenter({ layer: "station", code: value });
  };

  const isCurrentStation = (flag: boolean) => {
    // props.onIsCurrent(flag);
    setIsCurrent(flag);
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <Search
        value={stationId}
        placeholder={t("lang.rms.fed.stationId")}
        onSearch={stationSearch}
        enterButton
        allowClear={{ clearIcon: <CloseCircleFilled onClick={clearStation} /> }}
        onChange={e => setStationId(e.target.value)}
      />

      <div className="component-btn-group">
        <Button type="primary" disabled={currentSelect?.type !== "box"} block onClick={() => controlHandler("lockBox")}>
          {t("lang.rms.fed.lockBox")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "box"}
          block
          onClick={() => controlHandler("unlockBox")}
        >
          {t("lang.rms.fed.unlockBox")}
        </Button>
        <Button
          type="primary"
          disabled={!["lattice", "box"].includes(currentSelect?.type)}
          block
          onClick={() => controlHandler("lockLattice")}
        >
          {t("lang.rms.fed.lockLattice")}
        </Button>
        <Button
          type="primary"
          disabled={!["lattice", "box"].includes(currentSelect?.type)}
          block
          onClick={() => controlHandler("unlockLattice")}
        >
          {t("lang.rms.fed.UnlockLattice")}
        </Button>

        <Popover
          content={
            <div style={{ maxWidth: 200 }}>
              <Button
                type="primary"
                disabled={!["box"].includes(currentSelect?.type)}
                block
                onClick={() => {
                  controlHandler("toStation");
                  setChooseTargetType(false);
                }}
                style={{ marginBottom: 10, maxWidth: 200, wordWrap: "break-word" }}
              >
                {t("lang.rms.fed.updateBoxToCurrentStation")}
              </Button>

              <Button
                type="primary"
                disabled={!["box"].includes(currentSelect?.type)}
                block
                onClick={() => {
                  controlHandler("toShelf");
                  setChooseTargetType(false);
                }}
                style={{ maxWidth: 200, wordWrap: "break-word" }}
              >
                {t("lang.rms.fed.updateBoxToShelf")}
              </Button>
            </div>
          }
          title={t("lang.rms.fed.selectTaskType")}
          trigger="click"
          placement="left"
          open={chooseTargetType}
          onOpenChange={handleOpenChange}
        >
          <Button
            type="primary"
            disabled={!["box"].includes(currentSelect?.type)}
            onClick={() => setChooseTargetType(true)}
            block
            style={{ width: "48%", marginTop: "6px" }}
          >
            {t("lang.rms.fed.updateBox")}
          </Button>
        </Popover>
      </div>

      <ListTargetHandle
        visible={targetType === "toShelf" && currentSelect?.type === "box"}
        shelfData={targetShelf}
        currentSelect={currentSelect}
        targetSelect={targetSelect}
        onClear={() => {
          setTargetSelect("");
          setTargetShelf(null);
        }}
        onCancel={() => {
          isCurrentStation(true);
          setTargetSelect("");
          setOperation(undefined);
          setCurrentSelect(null);
          setTargetType("");
          const stationId = stationData?.stationId;
          console.log("stationId", stationId);
          if (!stationId) return;
          const map2D = getMap2D();
          map2D.mapRender.trigger("click", { station: [stationId] });
        }}
      />
      {targetType === "toStation" && currentSelect?.type === "box" && (
        <ListTargetCurrentStation
          stationData={stationData}
          currentSelect={currentSelect}
          onClear={() => {
            setTargetSelect("");
            setTargetShelf(null);
          }}
          onCancel={() => {
            isCurrentStation(true);
            setTargetSelect("");
            setOperation(undefined);
            setCurrentSelect(null);
            setTargetType("");
            const stationId = stationData?.stationId;
            console.log("stationId", stationId);
            if (!stationId) return;
            const map2D = getMap2D();
            map2D.mapRender.trigger("click", { station: [stationId] });
          }}
        />
      )}
      <StationDetail stationData={stationData} currentSelect={currentSelect} />

      <Drawer
        title={null}
        placement="left"
        mask={false}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        className="map2d-poppick-drawer"
        width={300}
      >
        <ListCurrent
          stationData={stationData}
          setCurrentSelect={value => {
            setCurrentSelect(value);
            setTargetType("");
          }}
        />
        <ListTargetPoppickShelf
          shelfData={targetShelf}
          currentSelect={currentSelect?.boxCode}
          targetSelect={targetSelect}
          setTargetSelect={setTargetSelect}
        />
      </Drawer>
    </div>
  );
}

export default OrderStation;
