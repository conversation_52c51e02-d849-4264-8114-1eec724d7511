/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled } from "@ant-design/icons";
import { Button, Input, Select } from "antd";
import { getMap2D } from "../../../../singleton";

const { Search } = Input;
const { Option } = Select;
type PropsOrderData = {
  visible: boolean;
  shelfData: shelfData;
  currentSelect: { type: "box" | "lattice"; latticeCodes: Array<code>; boxCode: code; boxSide: string };
  targetSelect: code;
  onClear: () => void;
  onCancel: () => void;
};
function StationOrderTargetHandle(props: PropsOrderData) {
  const { t } = useTranslation();
  const [shelfCode, setShelfCode] = useState<code>("");
  const [boxSide, setBoxSide] = useState<code>("");

  // 地图点击 地图切换可点击层
  useEffect(() => {
    if (!props.visible) return;

    const map2D = getMap2D();
    // map2D.mapRender.enableMultiClick(true);
    map2D.mapRender.triggerLayers(["shelf"]);

    return () => {
      map2D.mapRender.enableMultiClick(false);
      map2D.mapRender.clearSelects("shelf"); // TODO：只需要 清空 货架的选中，因为取消还会 继续选中station
      map2D.mapRender.triggerLayers(["station"]);
    };
  }, [props.visible]);

  useEffect(() => {
    if (!props.visible) return;
    return () => {
      handleCancel();
    };
  }, [props.visible]);

  useEffect(() => {
    if (!props.visible) return;
    setShelfCode(props.shelfData?.shelfCode || "");
  }, [props.shelfData]);

  useEffect(() => {
    if (!props.currentSelect) return;
    setBoxSide(props.currentSelect?.boxSide || "");
  }, [props.currentSelect]);

  // poppick搜索
  const poppickSearch = (value: string) => {
    if (!value) return;

    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "poppick", code: value });
    map2D.mapRender.trigger("click", { poppick: [value] });
    map2D.mapRender.setEleCenter({ layer: "poppick", code: value });
  };

  const controlHandler = () => {
    if (!shelfCode) return;
    const boxCode = props.currentSelect?.boxCode;
    const latticeCode = props.targetSelect;
    if (!boxCode || !latticeCode) return;

    const reqMsg = "BoxInstructionRequestMsg";
    const resMsg = "BoxInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        instruction: "UPDATE_BOX_LOCATION",
        latticeCode,
        boxCode,
        boxSide,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    handleCancel();
  };

  const handleCancel = () => {
    setShelfCode("");
    props.onClear();
    props.onCancel();
  };

  return (
    props.visible && (
      <div className="map2d-target-rack-box">
        <h6 style={{ padding: "12px 0 5px", fontSize: "14px", fontWeight: 700 }}>
          {t("lang.rms.fed.currentOperation")}: {t("lang.rms.fed.update")}
        </h6>

        <div className="search-box">
          <p style={{ fontSize: "14px", paddingBottom: 6 }}>{t("lang.rms.fed.clickAndQueryTargetRack")}：</p>
          <Search
            value={shelfCode}
            placeholder={t("lang.rms.fed.rackCode")}
            enterButton
            onSearch={poppickSearch}
            allowClear={{
              clearIcon: <CloseCircleFilled onClick={() => props.onClear()} />,
            }}
            onChange={e => setShelfCode(e.target.value)}
          />
          <p style={{ fontSize: "14px", paddingTop: 3, paddingBottom: 3 }}>
            {t("lang.rms.fed.clickAndQueryLattice")}：
          </p>
          <Input size="small" readOnly={true} value={props.targetSelect} placeholder={t("lang.rms.fed.boxCode")} />
          <p style={{ fontSize: "14px", paddingTop: 3, paddingBottom: 3 }}>{t("lang.rms.fed.boxSides")}：</p>
          <Select allowClear style={{ width: "100%" }} value={boxSide} onChange={value => setBoxSide(value)}>
            <Option value="B">B</Option>
            <Option value="F">F</Option>
            <Option value="L">L</Option>
            <Option value="R">R</Option>
          </Select>
        </div>

        <div style={{ paddingTop: 6, display: "flex", justifyContent: "flex-end" }}>
          <Button type="primary" disabled={false} size="small" onClick={controlHandler}>
            {t("lang.rms.fed.confirm")}
          </Button>
          <Button disabled={false} size="small" onClick={handleCancel} style={{ marginLeft: 8 }}>
            {t("lang.rms.fed.cancel")}
          </Button>
        </div>
      </div>
    )
  );
}

export default StationOrderTargetHandle;
