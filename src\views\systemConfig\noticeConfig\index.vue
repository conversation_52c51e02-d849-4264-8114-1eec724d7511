<template>
  <geek-main-structure>
    <gp-tabs :value="activeName" @tab-click="tabsNavChange" ref="myTabs">
      <gp-tab-pane
        v-for="item in permissionNavList"
        :label="$t(item.text)"
        :name="item.id"
        :key="item.id"
      ></gp-tab-pane>
    </gp-tabs>

    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import ExceptionConfig from "./exceptionConfig";
import NoticeManagement from "./noticeManagement";
export default {
  components: { ExceptionConfig, NoticeManagement },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabNoticeConfigFaultPage",
          id: "ExceptionConfig",
          text: "lang.rms.page.menu.exceptionConfig", // 故障配置
        },
        {
          permissionName: "TabNoticeConfigNoticePage",
          id: "NoticeManagement",
          text: "lang.rms.page.menu.noticeManagement", // 通知管理
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "menuNoticeConfig"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    // tabsNavChange(id) {
    //   if (this.activeName === id) return;
    //   this.activeName = id;
    // },
    tabsNavChange(target) {
      this.activeName = this.$refs.myTabs.$data.currentName;
    },
  },
};
</script>
<style lang="scss" scoped>
.notice-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
<style lang="less">
.notice-config-log-panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);
  .notice-config-log-panel-wrap__table {
    flex: 1;
  }
}
</style>
