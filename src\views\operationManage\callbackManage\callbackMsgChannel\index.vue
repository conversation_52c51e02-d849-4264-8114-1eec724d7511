<template>
  <section class="callback-manage-panel-wrap">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <div class="callback-manage-panel-wrap__table" style="padding-top: 16px">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-add="rowAdd"
        @row-edit="rowEdit"
        @row-disable="rowDisable"
        @row-enable="rowEnable"
        @row-config-tag="rowConfigTag"
        @row-del="rowDel"
      />
    </div>

    <callbackMsgChannelTagDialog
      v-if="channelTagDialog"
      :channel-tag-dialog.sync="channelTagDialog"
      :item-channel-data="itemData"
      @save="save"
      @update:channelTagDialog="getTableList()"
    />

    <gp-dialog
      v-if="channelEditDialog"
      :title="$t(dialogTitle)"
      :visible.sync="channelEditDialog"
      :before-close="closeChannelEditDialog"
      center
      :append-to-body="true"
      :close-on-click-modal="false"
      width="60%"
    >
      <gp-form
        ref="ruleForm"
        label-position="top"
        label-width="80px"
        :model="itemData"
        :rules="rules"
        class="padding_20"
      >
        <gp-card shadow="never">
          <gp-row :gutter="20">
            <gp-col :span="8">
              <gp-form-item
                :label="$t('lang.rms.fed.channelId')"
                :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
                prop="channelId"
              >
                <gp-input v-if="isEdit" v-model="itemData.channelId" type="text" readonly="readonly" />
                <gp-input v-else v-model="itemData.channelId" type="text" />
              </gp-form-item>
            </gp-col>
            <gp-col :span="5">
              <gp-form-item
                :label="$t('lang.rms.fed.channelType')"
                :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
                prop="channelType"
              >
                <gp-select v-model="itemData.channelType" :placeholder="$t('lang.rms.fed.pleaseChoose')">
                  <gp-option v-for="(item, key) in channelTypeList" :key="key" :label="item" :value="key" />
                </gp-select>
              </gp-form-item>
            </gp-col>
            <gp-col :span="5">
              <gp-form-item
                :label="$t('lang.rms.fed.enable')"
                :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
                prop="enable"
              >
                <gp-select v-model="itemData.enable" :placeholder="$t('lang.rms.fed.pleaseChoose')">
                  <gp-option v-for="(item, key) in enableList" :key="key" :label="$t(item)" :value="key" />
                </gp-select>
              </gp-form-item>
            </gp-col>
            <gp-col :span="6">
              <gp-form-item
                :label="$t('lang.rms.fed.callbackMaxRetryTimes')"
                :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
                prop="maxRetryTimes"
              >
                <gp-input v-model="itemData.maxRetryTimes" type="number" />
              </gp-form-item>
            </gp-col>
            <gp-col :span="8">
              <gp-form-item
                :label="$t('lang.rms.fed.callbackMaxRetryTimeout')"
                :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
                prop="maxRetryTimeout"
              >
                <gp-input v-model="itemData.maxRetryTimeout" type="number" />
              </gp-form-item>
            </gp-col>
            <gp-col :span="10">
              <gp-form-item :label="$t('lang.rms.fed.channelUrl')" prop="channelUrl" :rules="channelUrlRule">
                <gp-input v-model="itemData.channelUrl" type="text" />
              </gp-form-item>
            </gp-col>
            <!-- <gp-col :span="6">
              <gp-form-item :label="$t('lang.fed.rms.wheEnableInternalCallback')" prop="enableSelfCallback">
                <gp-switch v-model="itemData.enableSelfCallback" @change="searchText"></gp-switch>
              </gp-form-item>
            </gp-col> -->
          </gp-row>
        </gp-card>
      </gp-form>

      <p class="seniorSwitch">
        <span>
          {{ $t("lang.rms.fed.advancedEditor") }}
          <gp-tooltip class="item" effect="dark" :content="$t('lang.rms.fed.callbackFuncMsg0')" placement="top-start">
            <i class="icon gp-icon-question"></i>
          </gp-tooltip>
          <gp-switch v-model="isSenior"></gp-switch>
        </span>
      </p>

      <seniorCascaderPanel ref="seniorCascaderPanelRef" v-show="isSenior" :isSenior="isSenior" :option="itemData" />

      <span slot="footer" class="dialog-footer">
        <gp-button @click="closeChannelEditDialog">{{ $t("lang.common.cancel") }}</gp-button>
        <gp-button type="primary" :loading="loadingSave" @click="save">{{ $t("lang.rms.fed.save") }}</gp-button>
      </span>
    </gp-dialog>
  </section>
</template>
<script>
import callbackMsgChannelTagDialog from "./callbackMsgChannelTagDialog";
import seniorCascaderPanel from "./seniorCascaderPanel.vue";
/**
 * 这里的高级编辑使用 gp-cascader-panel, 但是加入查询筛选之后会出现很多问题
 * 这里需要对这个功能打一些补丁
 */

export default {
  name: "CallbackChannel",
  components: {
    callbackMsgChannelTagDialog,
    seniorCascaderPanel,
  },
  data() {
    const channelTypeList = {
      HTTP: "HTTP",
      WEBSOCKET: "WEBSOCKET",
      SOCKET: "SOCKET",
      RPC: "RPC",
      DMP: "DMP",
    };
    const isGuest = this.isRoleGuest();
    return {
      form: {
        channelId: "",
        channelType: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          channelId: {
            label: "lang.rms.fed.channelId",
            default: "",
            tag: "input",
          },
          channelType: {
            label: "lang.rms.fed.channelType",
            default: "",
            options: Object.entries(channelTypeList).map(([label, value]) => ({ label, value })),
            tag: "select",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          {
            label: "lang.rms.fed.channelId",
            prop: "channelId",
            minWidth: "130",
          },
          {
            label: "lang.rms.fed.channelType",
            prop: "channelType",
            minWidth: "140",
          },
          {
            label: "lang.rms.fed.enable",
            prop: "enable",
            minWidth: "120",
            formatter: (row, column) => {
              switch (row[column]) {
                case 1:
                  return this.$t("lang.rms.fed.enable");
                case 0:
                  return this.$t("lang.rms.fed.disable");
                default:
                  return row[column];
              }
            },
          },
          {
            label: "lang.rms.fed.callbackMaxRetryTimes",
            prop: "maxRetryTimes",
            minWidth: "120",
          },
          {
            label: "lang.rms.fed.callbackMaxRetryTimeout",
            prop: "maxRetryTimeout",
            minWidth: "130",
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "180",
                operations: [
                  // 启用
                  {
                    label: "lang.rms.fed.enable",
                    handler: "row-enable",
                    continue(row) {
                      return row.enable === 0;
                    },
                  },
                  // 禁用
                  {
                    label: "lang.rms.fed.chargerDisable",
                    handler: "row-disable",
                    continue(row) {
                      return row.enable === 1;
                    },
                  },
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                  // {
                  //   label: "lang.rms.fed.callbackConfigChannelTag",
                  //   handler: "row-config-tag",
                  // },
                  {
                    label: "lang.rms.fed.delete",
                    handler: "row-del",
                    type: "danger",
                  },
                ],
              },
        ),
      },

      itemData: {},
      enableList: {
        1: "lang.rms.fed.enable",
        0: "lang.rms.fed.disable",
      },
      channelTypeList: channelTypeList,
      isEdit: false,
      channelEditDialog: false,
      channelTagDialog: false,
      rules: {
        channelId: [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }],
      },
      loadingSave: false,
      isSenior: false,
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? "lang.rms.fed.edit" : "lang.rms.fed.add";
    },
    channelUrlRule() {
      const HTTP = "HTTP";
      return this.itemData.channelType === HTTP
        ? [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }]
        : [];
    },
  },
  activated() {
    this.getTableList();
  },
  methods: {
    // 编辑
    rowAdd() {
      this.isEdit = false;
      this.isSenior = false;
      this.channelEditDialog = true;
      this.channelTagDialog = false;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs["ruleForm"].resetFields();
        this.itemData = { maxRetryTimes: "10", maxRetryTimeout: "10", channelType: "RPC", enable: "1" };
      });
    },
    // 编辑
    rowEdit(row) {
      this.isEdit = true;
      this.isSenior = false;
      this.channelTagDialog = false;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.itemData = { ...row };
        this.itemData.enable = row.enable + "";
        this.channelEditDialog = true;
      });
    },
    rowDisable(row) {
      this.$confirm(
        this.$t("lang.rms.fed.disableCallbackMsg"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      ).then(() => {
        $req.post(`/athena/apiCallback/enabled?id=${row.id}&enabled=0`).then(res => {
          if (res?.code === 0) {
            this.$success(this.$t(res.msg));
            this.getTableList();
          }
        });
      }).catch((err) => {
        console.log(err)
      })
    },
    rowEnable(row) {
      this.$confirm(
        this.$t("lang.rms.fed.enabledCallbackMsg"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      ).then(() => {
        $req.post(`/athena/apiCallback/enabled?id=${row.id}&enabled=1`).then(res => {
          if (res?.code === 0) {
            this.$success(this.$t(res.msg));
            this.getTableList();
          }
        });
      }).catch((err) => {
        console.log(err)
      })
    },
    // 配置标签
    rowConfigTag(row) {
      this.itemData = JSON.parse(JSON.stringify(row));
      this.channelTagDialog = true;
      this.channelEditDialog = false;
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete"))
        .then(() => {
          const msgChannelIds = [];
          msgChannelIds.push(row.id);
          const para = { msgChannelIds };
          $req.post("/athena/apiCallback/deleteMsgChannel", para).then(res => {
            if (res?.code === 0) {
              this.$success(this.$t(res.msg));
              this.getTableList();
            }
          });
        })
        .catch(() => {});
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { channelId, channelType } = this.form;
      const { currentPage, pageSize } = this.tablePage;

      let data = { language: $utils.Data.getLocalLang() };
      if (channelId) data.channelId = channelId;
      if (channelType) data.channelType = channelType;
      $req
        .post(`/athena/apiCallback/msgChannelPageList?currentPage=${currentPage}&pageSize=${pageSize}`, data)
        .then(res => {
          let result = res?.data;
          if (!result) return;
          this.tableData = result.recordList || [];
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            total: result.recordCount || 0,
          });
        });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },

    getSaveCascderData() {
      debugger;
      return this.$refs.seniorCascaderPanelRef.getSaveCascderData();
    },

    // 保存
    save() {
      if (this.loadingSave) return;
      // channelTagDialog
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            this.saveHandel();
          } else {
            this.$geekConfirm(this.$t("lang.rms.fed.callbackFuncMsg1"))
              .then(async () => {
                this.saveHandel();
              })
              .catch(() => {});
          }
        }
      });
    },
    saveHandel() {
      this.loadingSave = true;
      const data = this.itemData;
      data.msgChannelId = this.itemData.id;
      const optionList = this.getSaveCascderData();
      data.optionList = optionList;

      // 保存通道配置
      $req
        .post("/athena/apiCallback/saveMsgChannel", data, { intercept: false })
        .then(json => {
          if (json.code === 0) {
            this.$message.success(this.$t(json.msg));
            this.channelEditDialog = false;
            this.getTableList();
            this.loadingSave = false;
          } else {
            this.loadingSave = false;
            const msgs = json.msg.split(",");
            if (msgs.length > 1) {
              const [msg, ...msgParams] = msgs;
              $app.$error(this.$t(msg, msgParams));
            } else {
              $app.$error(this.$t(json.msg));
            }
          }
        })
        .catch(() => {
          this.loadingSave = false;
        });
    },
    closeChannelEditDialog() {
      this.channelEditDialog = false;
      this.itemData = {};
    },
    // 这里处理双击事件
    handlerDoubleClick(node) {
      // 禁用锁定
      if (node.isDisabled) {
        return;
      }

      if (!node.checked) {
        // 当前是未选中或半选中, 需要选中当前值
        const nodeChilds = this.getCasTreeChildPaths(node);
        this.addSelectItemByCasc(nodeChilds);
      } else {
        // 当前是选中, 需要移除当前值
        const nodeChilds = this.getCasTreeChildPaths(node);
        this.removeSelectItemByCasc(nodeChilds);
      }
    },
    updateCascData(selectList) {
      // 这里对变更进行处理, 仅对局部区域变更
      const { prevCascaderSelects } = this;

      // 这次结果和上次结果做对比, 得到新增或移除的内容
      const curSecletStrList = selectList.map(item => item.join(","));
      const preSelectStrList = prevCascaderSelects.map(item => item.join(","));

      // 新增的值
      const addSelectList = curSecletStrList.filter(itemA => !preSelectStrList.includes(itemA));
      // 移除的值
      const removeSelectList = preSelectStrList.filter(itemA => !curSecletStrList.includes(itemA));

      if (addSelectList?.length) {
        this.addSelectItemByCasc(addSelectList.map(item => item.split(",")));
      }

      if (removeSelectList?.length) {
        this.removeSelectItemByCasc(removeSelectList.map(item => item.split(",")));
      }
    },

    addSelectItemByCasc(addList) {
      const curCascaderSelects = [...this.curCascaderSelects];
      addList.forEach(addItem => {
        const addItemStr = addItem.join(",");
        const isAdd = !this.curCascaderSelects.find(curItem => curItem.join(",") === addItemStr);
        isAdd && curCascaderSelects.push(addItem);
        const isAddCasca = !this.cascaderSelects.find(curItem => curItem.join(",") === addItemStr);
        isAddCasca && this.cascaderSelects.push(addItem);
      });
      this.prevCascaderSelects = curCascaderSelects;
      this.curCascaderSelects = curCascaderSelects;
    },

    removeSelectItemByCasc(removeList) {
      const removeStrList = removeList.map(item => item.join(","));
      this.cascaderSelects = this.cascaderSelects.filter(item => !removeStrList.includes(item.join(",")));
      this.curCascaderSelects = this.curCascaderSelects.filter(item => !removeStrList.includes(item.join(",")));
      this.prevCascaderSelects = this.curCascaderSelects;
    },

    getCasTreeChildPaths(node, list = []) {
      // 禁用和半选中
      if (!(node.isDisabled && !node.checked)) {
        list.push(node.path);
      }

      if (node.children?.length) {
        node.children.forEach(itemNode => {
          this.getCasTreeChildPaths(itemNode, list);
        });
      }

      return list;
    },

    cascaderPanelOptionsByFilter() {
      const { callbackQueryText, cascaderDataList, itemData, cascaderSelects } = this;
      let cascaderPanelOptions = cascaderDataList;

      // 筛选查询文本
      if (callbackQueryText) {
        cascaderPanelOptions = this.getQueryCascaderDataList(cascaderDataList, callbackQueryText) || [];
      }

      // 筛选内部回调
      // if (!itemData.enableSelfCallback) {
      cascaderPanelOptions = this.filterEnableSelfDataList(cascaderPanelOptions, "SELF_CALLBACK");
      // }

      // 重置当前选中项
      const selects = [];

      // 需要根据 cascaderDataList, 得到该path下所属的所有path, 并判断这些path是否为true, 全部为true则选中
      const handlerCascList = (cascList, paths = []) => {
        cascList.forEach(cascItem => {
          let path = [...paths, cascItem.id];
          if (cascItem.children?.length) {
            handlerCascList(cascItem.children, path);
          } else {
            if (this.getChildPathIsActiveByPath(path)) {
              selects.push(path);
            }
          }
        });
      };

      handlerCascList(cascaderPanelOptions);

      this.curCascaderDataList = cascaderPanelOptions;
      this.curCascaderSelects = selects;
      this.prevCascaderSelects = selects;
    },

    getChildPathByPath(path) {
      const { cascaderDataList } = this;
      const [statPath, ...endPath] = path;
      let item = cascaderDataList.find(item => item.id === statPath);
      endPath.forEach(pathItem => {
        item = item.children.find(childItem => childItem.id === pathItem);
      });
      return item;
    },

    // 检查这个path是否全选中了, 用来修正筛选可能导致选中消失的问题
    getChildPathIsActiveByPath(path) {
      if (path.length) {
        let flag = false;
        const childPathItem = this.getChildPathByPath(path);
        const cascaderSelectsStr = this.cascaderSelects.map(item => item.join(","));

        function next(childPathItem, p = []) {
          if (childPathItem.children?.length) {
            childPathItem.children.forEach(item => next(item, [...p, item.id]));
          } else {
            /**
             * 现在的逻辑是, 筛选之后, 子项目只要有一个是选中状态, 那筛到的这个就是选中状态
             */
            const pt = p.join(",");
            if (cascaderSelectsStr.find(item => item === pt)) {
              flag = true;
            }
          }
        }

        next(childPathItem, path);
        return flag;
      }
      return false;
    },

    /**
     * 这个函数能够过滤 模糊搜索的 queryText
     * @param {*} cascaderDataList
     * @param {*} queryText
     */
    getQueryCascaderDataList(cascaderDataList, queryText) {
      return cascaderDataList
        .filter(item => {
          return this.getIsCascaderDataByQuery(item, queryText);
        })
        .map(({ children, ...item }) => {
          if (children?.length) {
            item.children = this.getQueryCascaderDataList(children, queryText);
          }
          return item;
        });
    },
    /**
     * 这个函数能够过滤 模糊搜索的 queryText
     * @param {*} cascaderDataList
     * @param {*} queryText
     */
    filterEnableSelfDataList(cascaderDataList, filterKey) {
      return cascaderDataList
        .filter(item => {
          return item.key !== filterKey;
        })
        .map(({ children, ...item }) => {
          if (children?.length) {
            item.children = this.filterEnableSelfDataList(children, filterKey);
          }
          return item;
        });
    },

    /**
     * 检查当前数据是否匹配queryText
     */
    getIsCascaderDataByQuery(cascaderDataItem, queryText) {
      let flag = false;

      if (this.$t(cascaderDataItem.description).includes(queryText)) {
        return true;
      }

      const is = list => {
        list.forEach(({ children, description }) => {
          if (children?.length) {
            is(children);
          }

          if (this.$t(description).includes(queryText)) {
            flag = true;
          }
        });
      };

      cascaderDataItem.children?.length && is(cascaderDataItem.children);

      return flag;
    },

    searchText() {
      const { cascaderPanelRef } = this.$refs;
      if (cascaderPanelRef) {
        cascaderPanelRef.activePath = [];
        //  this.cascaderPanelOptionsByFilter();
        this.setFilterPanelIds();
      }
    },

    /**
     * 得到一个当前需要展示的id列表
     */
    setFilterPanelIds() {
      const { callbackQueryText, cascaderDataList, itemData, cascaderSelects } = this;
      let cascaderPanelOptions = cascaderDataList;

      // 筛选查询文本
      if (callbackQueryText) {
        cascaderPanelOptions = this.getQueryCascaderDataList(cascaderDataList, callbackQueryText) || [];
      }

      // 筛选内部回调
      // if (!itemData.enableSelfCallback) {
      cascaderPanelOptions = this.filterEnableSelfDataList(cascaderPanelOptions, "SELF_CALLBACK");
      // }

      // 重置当前选中项
      const selects = [];

      // 需要根据 cascaderDataList, 得到该path下所属的所有path, 并判断这些path是否为true, 全部为true则选中
      const handlerCascList = (cascList, paths = []) => {
        cascList.forEach(cascItem => {
          let path = [...paths, cascItem.id];
          if (cascItem.children?.length) {
            handlerCascList(cascItem.children, path);
          } else {
            if (this.getChildPathIsActiveByPath(path)) {
              selects.push(path);
            }
          }
        });
      };

      handlerCascList(cascaderPanelOptions);

      this.curCascaderDataList = cascaderPanelOptions;
      this.curCascaderSelects = selects;
      this.prevCascaderSelects = selects;
    },

    expandChange(list) {
      debugger;
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.ml-10 {
  margin-left: 10px;
}

.w_100x {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}

.seniorSwitch {
  padding: 5px;
  text-align: right;
}

.icon {
  font-size: 14px;
  color: #777;
}

.title {
  font-size: 16px;
  height: 32px;
  line-height: 32px;
  font-weight: 900;
}
</style>
