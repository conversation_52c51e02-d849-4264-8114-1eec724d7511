<template>
  <geek-main-structure class="play-bk-con" v-loading="isLoading">
    <!--上方工作条-->
    <section class="top-bar">
      <gp-date-picker
        v-model="date"
        type="datetimerange"
        :start-placeholder="$t('lang.rms.fed.startTime')"
        :end-placeholder="$t('lang.rms.fed.endTime')"
        :disabled-date="
          time => {
            return time.getTime() > Date.now();
          }
        "
        @change="dateChange"
        size="small"
        class="playback-time-picker"
      />

      <gp-button style="margin-left: 20px" type="primary" @click="search">{{ $t("lang.rms.fed.query") }}</gp-button>
      <gp-button @click="reset">{{ $t("lang.rms.fed.reset") }}</gp-button>
      <div class="right-con">
        <tool-bar @clickFn="clickFn"></tool-bar>
        <gp-select size="mini" v-model="nowFloor" style="width: 80px" @change="changeFloor">
          <gp-option v-for="(item, index) in floorIds" :value="item.value" :key="index">{{ item.label }}</gp-option>
        </gp-select>
      </div>
    </section>
    <!--右侧面板-->
    <right-panel
      @typeChange="typeChange"
      @showTaskDetail="showTaskDetail"
      @getSearchData="getSearchData"
      @selectMap="selectMap"
      :selectInfo="selectInfo"
      :searchList="searchList"
    ></right-panel>
    <!--下方播放条-->
    <section class="bottom-play">
      <div class="play-btn" @click="playCtrl">
        <div :class="playIcon"></div>
      </div>
      <div class="speed-ctrl">
        <gp-select v-model="speed" @change="changeSpeed">
          <gp-option v-for="(item, index) in speedArr" :label="item.label" :value="item.value" :key="index">{{
            item.label
          }}</gp-option>
        </gp-select>
      </div>
      <div class="slider-con">
        <gp-slider
          :min="min"
          :max="max"
          :step="step"
          :marks="marks"
          v-model="playTime"
          :format-tooltip="formatDate"
          :disabled="sliderDisabled"
          @change="changeSlider"
        ></gp-slider>
      </div>
    </section>
    <div id="mapBk"></div>
    <!--任务详情 @debugModelSearch="debugModelSearch"-->
    <task-detail v-if="taskDetailVisible" :visible.sync="taskDetailVisible" :initRow="dialogInitRow"></task-detail>
  </geek-main-structure>
</template>

<script>
// import { MapRender, MapWorker } from "./monitor2d.min.js";
// import Root from "@geek_map/ts-map-fe/packages/views/root.tsx";
import { MapRender, MapWorker } from "@geek_map/ts-map-fe/libs/monitor2d.min.js";
// import { MapRender, MapWorker } from "@geek_map/ts-map-fe/src/index";
import RightPanel from "./RightPanel";
import ToolBar from "./ToolBar";
//任务详情
import TaskDetail from "./TaskDetail/taskDetail";
let mapRender = null;
let cacheData = {};
let changeSliderFlag = false;
//提前缓存的时间
let cacheTime = 120 * 1000;
export default {
  name: "index",
  components: { RightPanel, ToolBar, TaskDetail },
  data() {
    return {
      isLoading: false,
      //当前日期
      date: [],
      playTime: 0,
      isPlay: false,
      timeRange: [],
      pickerDateOps: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      //当前选中的类型
      selectType: null,
      //地图总楼层
      floorIds: [],
      nowFloor: 1,
      //所有单元格数据
      floorInfo: [],
      //缓存的地图数据
      cacheData: [],
      clickInfo: null,
      //滑块的最大最小值
      min: 0,
      max: 0,
      step: 1000,
      speed: 1000, //单位秒
      speedArr: [
        {
          label: "×1",
          value: 1000,
        },
        {
          label: "×5",
          value: 200,
        },
        {
          label: "×10",
          value: 100,
        },
      ],
      //播放列表
      playList: [],
      playInterval: null,
      //单位毫秒
      isLoadingData: false,
      taskDetailVisible: false,
      dialogInitRow: {}, // 任务详情内容。
      //提供搜索数据
      searchList: [],
    };
  },
  computed: {
    playIcon() {
      return this.isPlay ? "stop-btn" : "start-btn";
    },
    marks() {
      const obj = {};
      let count = 10;
      if (this.min === 0 && this.max === 0) return { 0: "00:00:00" };
      const diff = Math.floor((this.max - this.min) / 1000);
      if (diff < count) count = diff;
      // if((this.max - this.min) < count)
      const eachTime = (this.max - this.min) / count;
      for (let c = 0; c <= count; c++) {
        const timestamp = this.min + eachTime * c;
        obj[timestamp] = this.formatDate(timestamp);
      }
      return obj;
    },
    sliderDisabled() {
      return !this.playList.length;
    },
    //获取选中的时间段
    getTimeRange() {
      const val = this.date;
      this.timeRange= val.map(t => new Date(t).getTime())
      return this.timeRange;
    },
    selectInfo() {
      const playTime = this.playTime;
      let info = null;
      if (!this.clickInfo) return info;
      const { layer, code } = this.clickInfo;
      if (layer === "cell") {
        info = cacheData?.cells[code];
      }
      if (layer === "station") {
        info = cacheData?.stations[code];
      }
      if (layer === "charger") {
        info = cacheData?.chargers[code];
      }
      if (layer === "robot") {
        info = cacheData?.robots[code];
      }
      if (layer === "shelf") {
        info = cacheData?.shelves[code];
      }
      return info;
    },

  },
  watch: {
    async playTime(timestamp) {
      const len = this.playList.length;
      if (!len || changeSliderFlag) return;
      //这一组数据的最后一个时间戳
      const startT = this.playList[0].playTime;
      const endT = this.playList[len - 1].playTime;
      if (endT - timestamp < cacheTime / 2 && endT > timestamp) {
        if (this.isLoadingData) return;
        console.log("提前缓存");
        const range = [timestamp, timestamp + cacheTime];
        this.isLoadingData = true;
        const postObj = {
          playTimeFrom: range[0],
          playTimeTo: range[1],
          playSpeed: 1,
          playbackId: "20230516123000-20230516130000",
          resourceType: 0,
          floorId: 1,
        };
        $req.post("/athena/engine/tools/playback/play", postObj).then(res => {
          if (res.data) {
            this.playList = this.playList.concat(res.data.playList);
            //如果playList总数大于200，则清除一些
            if (this.playList.length > 200) {
              this.playList = this.playList.slice(150);
            }
          }
          this.isLoadingData = false;
        });
      } else if (timestamp >= endT) {
        console.log("向前缓存");
        if (this.isLoadingData) return;
        //当播放时间大于该组数据最大值时
        this.clearInterval();
        const range = [endT, endT + cacheTime];
        this.isLoading = true;
        const res = await this.getMapData(range[0], range[1]);
        if (res.data) {
          this.playList = res.data.playList;
          //先渲染第一帧
          const fistTime = this.playList[0].playTime;
          this.renderFrame(fistTime);
        }
        this.isLoading = false;
        if (this.isPlay) {
          this.isPlay = false;
          this.playCtrl();
        }
      } else if (timestamp < startT) {
        console.log("向后缓存");
        if (this.isLoadingData) return;
        //当播放时间小于该组数据最小值时
        this.clearInterval();
        // this.isPlay = false
        const range = [timestamp, timestamp + cacheTime];
        this.isLoading = true;
        const res = await this.getMapData(range[0], range[1]);
        if (res.data) {
          this.playList = res.data.playList;
          //先渲染第一帧
          const fistTime = this.playList[0].playTime;
          this.renderFrame(fistTime);
        }
        this.isLoading = false;
        if (this.isPlay) {
          this.isPlay = false;
          this.playCtrl();
        }
      }
    },
    date: {
      handler(val) {
        if (!val?.length) return;
        this.timeRange = val.map(t => new Date(t).getTime());
      },
      immediate: true,
    },
  },
  created() {
    this.date = this._getDefaultDate();
    this.resizeEvent().bindEvent();
  },
  activated() {
    this.initMap();
    const dom = document.getElementsByClassName("gp-slider__button-wrapper")[0];
    dom.onmousedown = () => {
      changeSliderFlag = true;
    };
    dom.onmouseup = () => {
      changeSliderFlag = false;
    };
  },
  methods: {
    selectMap({ selectType, searchVal }) {
      this.clickInfo = { layer: selectType, code: searchVal };
      if (mapRender) {
        mapRender.trigger("click", { [selectType]: [searchVal] });
        mapRender.setEleCenter({ layer: selectType, code: searchVal });
      }
    },
    resizeEvent() {
      const resizeMap = () => {
        mapRender && mapRender.resize();
      };
      const bindEvent = () => {
        window.addEventListener("resize", resizeMap, false);
      };
      const unbindEvent = () => {
        window.removeEventListener("resize", resizeMap, false);
      };
      return {
        bindEvent,
        unbindEvent,
      };
    },
    async showTaskDetail(jobId) {
      this.isLoading = true;
      const paramsObj = {
        jobId,
      };
      const { data } = await $req.get("/athena/engine/tools/job/showJobDetail", {
        ...paramsObj,
      });
      this.taskDetailVisible = true;
      this.dialogInitRow = { ...data };
      this.isLoading = false;
    },
    getSearchData(searchParams) {
      const { selectType, searchVal } = searchParams;
      if (!searchVal) {
        this.searchList = [];
        mapRender.clearSelects();
        return;
      }
      let res;
      if (selectType === "robot") {
        res = this.cacheMapData().get("robots");
      }
      if (selectType === "cell") {
        res = this.cacheMapData().get("cells");
      }
      if (selectType === "shelf") {
        res = this.cacheMapData().get("shelves");
      }
      this.searchList = Object.keys(res)
        .filter(id => id.includes(searchVal))
        .slice(0, 10);
    },
    changeFloor() {
      this.reset();
    },
    changeSpeed() {
      if (this.isPlay) {
        this.isPlay = false;
        this.clearInterval();
        this.playCtrl();
      }
    },
    clickFn(item) {
      const { name, isActive } = item;
      switch (name) {
        case "zoom-in": // Map缩小
          mapRender.zoom(-0.2);
          break;
        case "zoom-out": // Map放大
          mapRender.zoom(0.2);
          break;
        case "map-reset": // 重置
          mapRender.setMapPosition();
          break;
        case "show-unload-road-direct": // 显隐空载路线
          mapRender.toggleLayer("unload", isActive);
          mapRender.rerender();
          break;
        case "show-load-road-direct": // 显隐负载路线
          mapRender.toggleLayer("load", isActive);
          mapRender.rerender();
          break;
      }
    },
    typeChange(type) {
      if (!mapRender) return;
      mapRender.triggerLayers([type]);
      mapRender.clearSelects();
      this.clickInfo = null;
      this.searchList = [];
    },
    //获取当前时分秒，以毫秒为单位
    getTime(timeStamp) {
      const date = new Date(timeStamp);
      const h = date.getHours();
      const m = date.getMinutes();
      const s = date.getSeconds();
      const t = (h * 60 * 60 + m * 60 + s) * 1000;
      return t;
    },
    //检查时间
    checkTime(d) {
      const oneHour = 60 * 60 * 1000;
      const [start, end] = this.getTimeRange;
      const range = end - start;
      if (range > oneHour) {
        // 请注意：生成日志回放的时间区间不能大于1小时！
        // this.$message.error(this.$t("lang.rms.fed.replay.checkTimeMsg"));
        this.$message.error(this.$t("lang.rms.fed.playback.maxQueryTime"));
        return false;
      }
      if (!this.date) {
        this.$message.error(this.$t("lang.rms.fed.playback.requireTime"));
        return false;
      }
      return true;
    },
    //把时间戳格式化
    formatDate(timestamp) {
      if (timestamp === 0) return "00:00:00";
      const date = new Date(timestamp);
      const hour = date.getHours();
      const min = date.getMinutes();
      const s = date.getSeconds();
      const arr = [hour, min, s].map(t => {
        if (t < 10) t = "0" + t;
        return t.toString();
      });
      return arr.join(":");
    },

    initMap() {
      this.isLoading = true;
      const $dom = document.getElementById("mapBk");
      if (!mapRender && $dom) {
        mapRender = new MapRender($dom);
        mapRender.ready(async () => {
          const initData = await intiMapData();
          console.log(initData);
          const { floors, stations, chargers, shelves } = initData;
          // console.log(floorData)
          if (mapRender) {
            mapRender.renderFloors(floors);
            const displayObj = {
              isInitFinish: true,
              stations,
              chargers,
              shelves,
            };
            mapRender.renderDisplays(displayObj);
            this.isLoading = false;
          }
        });
        mapRender.rendered(renderType => {
          if (renderType !== "floorRendered") return;
          mapRender.triggerLayers(["robot"]);
        });
        mapRender.click(data => {
          this.clickInfo = data;
        });

        mapRender.init();
        //初始化地图
        const intiMapData = async () => {
          const [from, to] = this.timeRange;
          const res = await this.getMapData(from, to, 1);
          // const res = await this.getMapData(from,from + 30 * 1000,1)
          const { code, data } = res;
          const { playList } = data;
          const { displayChargers, displayWorkStations, displayShelves } = playList[0];
          // console.log(playList[0])
          const map = playList[0].map;
          const { floorIds, floors } = map;
          // this.nowFloor = floorIds[0]
          this.floorIds = floorIds.map(floor => {
            return { label: floor, value: floor };
          });
          // this.floorIds.unshift({label:'全部',value:'all'})
          // this.nowFloor = this.floorIds[0]
          // const nowFloorId = floorIds[0]
          //把数据处理下
          const handlerObj = {};
          for (let floorId in floors) {
            handlerObj[floorId] = floors[floorId];
            handlerObj[floorId]["cells"] = floors[floorId]["mapCells"];
            handlerObj[floorId]["segments"] = floors[floorId]["mapSegments"];
          }
          //处理cell数据并保存
          const currentMapCells = floors[this.nowFloor].cells;
          const cellObj = {};
          currentMapCells.forEach(item => {
            const { cellCode } = item;
            cellObj[cellCode] = item;
          });
          const nowFloorStations = this.handlerNowFloorData(displayWorkStations);
          const nowFloorChargers = this.handlerNowFloorData(displayChargers);
          const nowFloorShelves = this.handlerNowFloorData(displayShelves);
          this.cacheMapData().save("cells", cellObj);
          this.cacheMapData().save("stations", nowFloorStations);
          this.cacheMapData().save("chargers", nowFloorChargers);
          this.cacheMapData().save("shelves", nowFloorShelves);
          return {
            floors: { [this.nowFloor]: handlerObj[this.nowFloor] },
            shelves: { [this.nowFloor]: Object.values(nowFloorShelves) },
            chargers: { [this.nowFloor]: Object.values(nowFloorChargers) },
            stations: { [this.nowFloor]: Object.values(nowFloorStations) },
          };
        };
      }
    },
    handlerNowFloorData(displayData) {
      // if(this.nowFloor === 'all') return displayData
      const handlerObj = {};
      for (let i in displayData) {
        const itemData = displayData[i];
        const { location } = itemData;
        if (location && location.z === this.nowFloor) {
          handlerObj[i] = itemData;
        }
      }
      return handlerObj;
    },
    playCtrl() {
      const finalTime = new Date(this.getTimeRange[1]).getTime();
      if (!this.playList.length) {
        this.$message({
          message: this.$t("lang.rms.fed.notExistData"),
          type: "warning",
        });
        return;
      }
      this.isPlay = !this.isPlay;
      if (this.isPlay) {
        this.clearInterval();
        this.playInterval = setInterval(() => {
          if (this.playTime >= finalTime) {
            this.clearInterval();
            this.isPlay = false;
          }
          this.playTime += this.step;
          this.renderFrame(this.playTime);
        }, this.speed);
      } else {
        this.clearInterval();
      }
    },
    clearInterval() {
      if (this.playInterval) {
        clearInterval(this.playInterval);
        this.playInterval = null;
      }
    },
    //渲染每一帧
    renderFrame(timestamp) {
      const frameArr = this.playList.filter(f => f.playTime === timestamp);
      if (frameArr.length) {
        const frame = frameArr[0];
        this.updateMap(frame);
      }
    },
    //更新地图数据
    updateMap(frame) {
      const { displayCells, displayChargers, displayRobots, displayShelves, displayWorkStations } = frame;
      const nowFloorStations = this.handlerNowFloorData(displayWorkStations);
      const nowFloorChargers = this.handlerNowFloorData(displayChargers);
      const nowFloorShelves = this.handlerNowFloorData(displayShelves);
      const nowFloorRobots = this.handlerNowFloorData(displayRobots);
      //缓存数据
      this.cacheMapData().save("cells", displayCells);
      this.cacheMapData().save("robots", nowFloorRobots);
      this.cacheMapData().save("stations", nowFloorStations);
      this.cacheMapData().save("chargers", nowFloorChargers);
      this.cacheMapData().save("shelves", nowFloorShelves);
      const updateDisplay = {
        cells: { [this.nowFloor]: Object.values(displayCells) },
        robots: { [this.nowFloor]: Object.values(nowFloorRobots) },
        // shelves:{[this.nowFloor]:Object.values(displayShelves)},
        shelves: { [this.nowFloor]: Object.values(this.cacheMapData().get("shelves")) },
        stations: { [this.nowFloor]: Object.values(nowFloorStations) },
        chargers: { [this.nowFloor]: Object.values(nowFloorChargers) },
      };
      mapRender.updateDisplays(updateDisplay);
    },
    //请求数据
    async getMapData(from, to, resourceType = 0) {
      
      this.isLoadingData = true;
      const postObj = {
        playTimeFrom: from,
        playTimeTo: to,
        playSpeed: 1,
        playbackId: "20230516123000-20230516130000",
        // "fullResource": true,
        resourceType,
        floorId: 1,
      };
      const res = await $req.post("/athena/engine/tools/playback/play", postObj);
      this.isLoadingData = false;
      return res;
    },
    //请求下一阶段数据
    //FIXME 因为是增量更新，会导致地图中存在此数据，但是查不到相关信息的情况，所以对每个楼层的数据进行缓存，用来查询
    cacheMapData() {
      const save = (type, data = {}) => {
        //如果不存在，赋空
        if (!cacheData[type]) cacheData[type] = {};
        // //shelves特殊处理，增量更新，其他全量覆盖
        // if(type === 'shelves'){
        //   cacheData[type] = Object.assign({},cacheData[type],data)
        // }else{
        //   cacheData[type] = data
        // }
        cacheData[type] = Object.assign({}, cacheData[type], data);
        // cacheData = {...cacheData}
      };
      const clear = () => {
        cacheData = {};
      };
      const get = type => {
        return cacheData[type] || {};
      };
      return {
        get,
        save,
        clear,
      };
    },
    async changeSlider(timestamp) {
      // this.renderFrame(timestamp)
      this.isLoading = true;
      changeSliderFlag = true;
      //记录是否处于播放状态，如果是先暂停等数据返回再开始
      const isPlaying = this.isPlay;
      if (isPlaying) {
        this.clearInterval();
        this.isPlay = false;
      }
      const from = timestamp,
        to = timestamp + 150 * 1000;
      const { data } = await this.getMapData(from, to, 2);
      const { playList } = data;
      this.playList = playList;
      //拿第二帧全量数据
      const frame = playList[1];
      this.updateMap(frame);
      if (isPlaying) {
        this.playCtrl();
      }
      changeSliderFlag = false;
      this.isLoading = false;
    },

    dateChange(val) {
      if (!val?.length) return;

      const start = new Date(val[0]);
      const end = new Date(val[1]);

      const startTime = start.getTime();
      const endTime = end.getTime();

      const oneHour = 60 * 60 * 1000;
      if (endTime - startTime > oneHour) {
        end.setHours(start.getHours() + 1);
        this.date = [start, end];
      }
    },

    _getDefaultDate() {
      const end = new Date();
      const nowTimestamp = Date.now();
      const fiveMinAgoTimestamp = nowTimestamp - 5 * 60 * 1000;
      const start = new Date(fiveMinAgoTimestamp);
      return [start, end];
    },

    async search() {
      this.isPlay = false;
      this.clearInterval();
      this.clickInfo = null;
      if (mapRender) mapRender.clearSelects();
      const isLegal = this.checkTime();
      if (!isLegal) return;
      this.isLoading = true;
      const [from, to] = this.getTimeRange;
      this.min = from;
      this.max = to;
      this.playTime = from;
      const res = await this.getMapData(from, to, 2);
      if (res.data) {
        // const sliceList = res.data.playList.slice(2)
        const orlPlayList = res.data.playList;
        orlPlayList.shift();
        this.playList = orlPlayList;
        //先渲染第一帧
        const fistTime = this.playList[0].playTime;
        this.renderFrame(fistTime);
      }
      this.isLoading = false;
    },

    //重置
    reset() {
      this.clickInfo = null;
      this.isPlay = false;
      this.date = this._getDefaultDate();
      this.playList = [];
      this.speed = 1000;
      this.min = 0;
      this.max = 0;
      this.playTime = 0;
      this.cacheMapData().clear();
      // if(mapRender) mapRender.clearSelects()
      if (mapRender) mapRender.destroy();
      mapRender = null;
      this.initMap();

      // mapRender.repaint()
    },
  },
  deactivated() {
    if (mapRender) mapRender.destroy();
    mapRender = null;
    this.resizeEvent().unbindEvent();
  },
  // destroyed() {
  //   if(mapRender)mapRender.destroy()
  // }
};
</script>

<style scoped lang="less">
.play-bk-con {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .top-bar {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    z-index: 100;
    display: flex;
    align-items: center;
    background: #fff;
    padding: 10px;
    .right-con {
      display: flex;
      align-items: center;
      flex: 1;
    }
  }
  .right-panel {
    position: absolute;
    top: 50px;
    right: 0;
    width: 300px;
    height: 100%;
    z-index: 100;
  }
  .bottom-play {
    position: absolute;
    display: flex;
    background: #ffffff;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    z-index: 100;
    .play-btn {
      flex: 0 0 40px;
      margin: 10px;
      height: 40px;
      cursor: pointer;
      background: #d0d0d0;
      border-radius: 12px;
      box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.3);
      //box-sizing: border-box;
      //background-color: #2c3e50;
      .start-btn {
        width: 100%;
        height: 100%;
        background: url("./icon/play.png") no-repeat center;
        background-size: 80%;
      }
      .stop-btn {
        width: 100%;
        height: 100%;
        background: url("./icon/stop.png") no-repeat center;
        background-size: 110%;
      }
    }
    .speed-ctrl {
      display: flex;
      align-items: center;
      width: 100px;
    }
    .slider-con {
      padding: 0 40px;
      flex: 1;
    }
  }
  #mapBk {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
  }
}
</style>
