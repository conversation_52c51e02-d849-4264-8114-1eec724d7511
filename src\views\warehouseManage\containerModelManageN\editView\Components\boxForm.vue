<template>
  <!-- 编辑任务 -->
  <gp-form
    class="mform"
    ref="mformRef"
    label-position="right"
    label-width="140px"
    size="mini"
    :model="editTaskData"
    :rules="editTaskRules"
  >
    <gp-form-item :label="$t('lang.rms.fed.containerBoxModel')" prop="modelName">
      <gp-input class="w200" v-model="editTaskData.modelName" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 容器类型 -->
    <gp-form-item :label="$t('lang.rms.web.container.containerType')" prop="modelType">
      <gp-input class="w200" v-model="editTaskData.modelType" :disabled="editDisabled"></gp-input>
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        :placeholder="$t('lang.rms.fed.choose')"
        @change="needSendRobotChange"
      >
        <gp-option :label="$t('lang.rms.fed.no')" :value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" :value="1" />
      </gp-select>
    </gp-form-item>

    <gp-form-item :label="$t('lang.rms.containerManage.sendModelId.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text"><gp-icon name="gp-icon-question" /></gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        step-strictly
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>

    <div class="modelTitle">{{ $t("lang.rms.fed.boxSize") }}</div>
    <gp-row>
      <gp-col :span="8">
        <gp-form-item prop="length" :label="`${$t('lang.rms.fed.length')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.length"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="width" :label="`${$t('lang.rms.fed.width')}(mm)`" label-width="80px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.width"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <gp-form-item prop="height" :label="`${$t('lang.rms.fed.high')}(mm)`" label-width="110px">
          <gp-input-number
            step-strictly
            size="mini"
            v-model="editTaskData.height"
            :min="1"
            :max="9000000"
          ></gp-input-number>
        </gp-form-item>
      </gp-col>
    </gp-row>
  </gp-form>
</template>
<script>
import { mapMutations, mapState, mapActions } from "vuex";

export default {
  props: {},
  components: {},
  created() {
    if (this.editData.id) {
      this.editTaskData = JSON.parse(JSON.stringify(this.editData));
    }
  },
  data() {
    return {
      editTaskData: {
        modelName: "",
        modelType: "",
        needSendRobot: 0,
        sendModelId: "",
        modelCategory: "BOX",
        width: 1000,
        length: 1000,
        height: 1000,
      },
    };
  },
  watch: {
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
  },
  computed: {
    ...mapState("containerModal", ["shelfCategoryDict", "editData"]),
    editDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };
      return {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          requiredRule,
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        width: [requiredRule],
        height: [requiredRule],
        length: [requiredRule],
      };
    },
  },
  methods: {
    async validateData() {
      try {
        await this.$refs.mformRef.validate();
        return this.editTaskData;
      } catch (error) {
        return false;
      }
    },
    needSendRobotChange(value) {
      if (value) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.modelTitle {
  font-weight: 600;
  height: 26px;
  margin: 5px 0;
  text-align: left;
  font-size: 14px;
  padding: 3px 12px;
  background: #eee;
  margin-bottom: 20px;
}

.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
