<template>
  <gp-form ref="form" size="mini" :rules="editTaskRules" :model="editTaskData" label-width="140px">
    <!-- 货架模型名称 -->
    <gp-form-item :label="$t('lang.rms.fed.groundSupportModelAlias')" prop="modelName">
      <gp-input
        class="w200"
        v-model="editTaskData.modelName"
        :disabled="viewDisabled"
        size="mini"
        maxlength="32"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg0") }}</span>
    </gp-form-item>

    <!-- 容器类型 -->
    <gp-form-item prop="modelType" :label="$t('lang.rms.web.container.containerType')">
      <gp-input
        class="w200"
        v-model="editTaskData.modelType"
        :disabled="viewDisabled"
        size="mini"
        maxlength="15"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
      />
      <span v-if="isBuiltIn" class="containerMsg">{{ $t("lang.rms.fed.systemBuiltInMsg1") }}</span>
    </gp-form-item>

    <!-- 是否下发给机器人 -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <gp-select
        class="w200"
        v-model="editTaskData.needSendRobot"
        :placeholder="$t('lang.rms.fed.choose')"
        @change="val => handleNeedSendRobot(val)"
      >
        <gp-option :label="$t('lang.rms.fed.no')" value="0" />
        <gp-option :label="$t('lang.rms.fed.yes')" value="1" />
      </gp-select>
    </gp-form-item>

    <!-- 下发模型ID -->
    <gp-form-item :label="$t('lang.rms.containerManage.needSendRobot.msg')">
      <template #label>
        <gp-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <gp-button type="text"><gp-icon name="gp-icon-question" /></gp-button>
        </gp-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
      </template>
      <gp-input-number
        step-strictly
        class="w200"
        v-model="editTaskData.sendModelId"
        :disabled="String(editTaskData.needSendRobot) === '0'"
        :min="0"
        size="mini"
        :step="1"
      />
    </gp-form-item>
    <p class="modelTitle">{{ $t("lang.rms.fed.groundSupport") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="length"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.length"
            :min="dockModelMap[dockModelType].shelfMinHLen"
            :max="dockModelMap[dockModelType].shelfMaxHLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="width"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.width"
            :min="dockModelMap[dockModelType].shelfMinWLen"
            :max="dockModelMap[dockModelType].shelfMaxWLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>
    <p class="modelTitle">{{ $t("lang.rms.fed.groundSupportLeg") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="legLength"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.legLength"
            :min="0"
            :max="dockModelMap[dockModelType].shelfMaxHLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="legWidth"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.legWidth"
            :min="0"
            :max="dockModelMap[dockModelType].shelfMaxWLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>

    <p class="modelTitle">{{ $t("lang.rms.fed.container.pass") }}:</p>
    <gp-row>
      <gp-col :span="8">
        <!-- 长 -->
        <gp-form-item
          prop="passLength"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passLength"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxLen"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 宽 -->
        <gp-form-item
          prop="passWidth"
          :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width') + '(mm):'"
          label-width="80px"
        >
          <gp-input-number
            step-strictly
            v-model="editTaskData.passWidth"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxWidth"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
      <gp-col :span="8">
        <!-- 高 -->
        <gp-form-item prop="passHeight" :label="$t('lang.rms.fed.high') + '(mm):'" label-width="80px">
          <gp-input-number
            step-strictly
            v-model="editTaskData.passHeight"
            :min="0"
            :max="dockModelMap[dockModelType].passMaxHeight"
            size="mini"
            :step="1"
          />
        </gp-form-item>
      </gp-col>
    </gp-row>

    <p class="modelTitle">{{ $t("lang.rms.fed.palletPosition") }}:</p>
    <gp-row>
      <gp-col :span="12">
        <!-- 长 -->
        <gp-form-item prop="offsetY" :label="$t('lang.rms.fed.yAxisDeviation') + '(mm):'" label-width="110px">
          <gp-input-number step-strictly v-model="editTaskData.offsetY" :min="0" :max="100000" size="mini" :step="1" />
        </gp-form-item>
      </gp-col>
    </gp-row>
  </gp-form>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {},
  data() {
    return {
      // 编辑任务数据
      editTaskData: {
        id: "",
        modelCategory: "SHELF_HOLDER",
        modelType: "", // 地图支架类别
        mapId: null,
        modelName: "",
        length: 0,
        width: 0,
        legLength: 0,
        legWidth: 0,
        offsetY: 0,
        needSendRobot: "0",
        sendModelId: "",
        passLength: 0,
        passWidth: 0,
      },

      // loading
      dockModelType: "def",
      dockModelMap: {
        M100: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 800,
          shelfMaxWLen: 1100,
          shelfMaxHLen: 900,
          shelfMinWLen: 600,
          shelfMinHLen: 600,
        },
        M1000: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1220,
          shelfDefHLen: 1020,
          shelfMaxWLen: 1500,
          shelfMaxHLen: 1500,
          shelfMinWLen: 1020,
          shelfMinHLen: 1020,
        },
        def: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 1000,
          shelfMaxWLen: 50000,
          shelfMaxHLen: 50000,
          shelfMinWLen: 500,
          shelfMinHLen: 500,
          passMaxLength: 50000,
          passMaxWidth: 50000,
          passMaxHeight: 50000,
        },
      },
    };
  },
  computed: {
    ...mapState("containerModal", ["editData", "maxModelId"]),
    viewDisabled() {
      return this.editData?.used || Number(this.editData?.builtIn) === 1;
    },
    isBuiltIn() {
      return Number(this.editData?.builtIn) === 1;
    },
    editTaskRules() {
      const requiredRule = {
        required: true,
        message: this.$t("lang.rms.fed.pleaseEnter"),
        trigger: "blur",
      };

      return {
        modelName: [
          requiredRule,
          {
            pattern: /^.{1,12}$/,
            message: this.$t("lang.rms.fed.limitLength", ["", "12"]),
            trigger: "blur",
          },
        ],
        modelType: [
          requiredRule,
          {
            // 正则 只允许输入英文，数字，限制15字符之内
            pattern: /^[a-zA-Z0-9_-]{1,15}$/,
            message: this.$t("lang.rms.fed.enter15Characters"),
            trigger: "blur",
          },
        ],
        length: [requiredRule],
        width: [requiredRule],
        legLength: [requiredRule],
        legWidth: [requiredRule],
        offsetY: [requiredRule],
        passLength: [requiredRule],
        passWidth: [requiredRule],
        passHeight: [requiredRule],
      };
    },
  },
  activated() {
    this.dockModelType = "def";
  },
  created() {
    if (this.editData.id) {
      this.editTaskData = { ...this.editData };
    }
  },
  watch: {
    dockModelType(value) {
      const { dockModelMap, editTaskData } = this;
      const item = dockModelMap[value];
      editTaskData.legLength = item.legDefHLen;
      editTaskData.legWidth = item.legDefWLen;
      editTaskData.length = item.shelfDefHLen;
      editTaskData.width = item.shelfDefWLen;
    },
    editTaskData: {
      handler(val) {
        this.$emit("updateValue", val);
      },
      deep: true,
    },
  },
  methods: {
    handleNeedSendRobot(val) {
      if (val) {
        $req.get("/athena//shelfModel/getMaxId").then(res => {
          if (res.code === 0) {
            this.editTaskData.sendModelId = res.data;
          }
        });
      }
    },

    async validateData() {
      try {
        await this.$refs.form.validate();
        return this.editTaskData;
      } catch (error) {
        return false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.labelTitle {
  text-align: right;
  padding-right: 8px;
  font-size: 14px;
}

.modelTitle {
  font-weight: 600;
  height: 26px;
  margin: 5px 0;
  text-align: left;
  font-size: 14px;
  padding: 3px 12px;
  background: #eee;
}
.containerMsg {
  text-indent: 5px;
  color: red;
}
</style>
