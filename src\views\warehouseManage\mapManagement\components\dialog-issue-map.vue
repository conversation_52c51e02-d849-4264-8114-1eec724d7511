<template>
  <div>
    <gp-dialog
      :title="dialogIssueMap.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="580px"
      border
      @close="close"
    >
      <gp-link type="primary" :underline="false" class="tips">
        {{ $t("lang.rms.web.map.version.mapPublishingTips") }}
      </gp-link>
      <gp-form label-position="right">
        <gp-form-item :label="$t('lang.rms.web.map.version.mapChange')" label-width="40px" prop="remark">
          <gp-input v-model="remark" type="textarea" />
        </gp-form-item>
      </gp-form>

      <div slot="footer">
        <gp-button @click="visible = false" plain>{{ $t("lang.rms.fed.cancel") }}</gp-button>
        <gp-button type="primary" :disabled="disable" @click="submit">
          {{ $t("lang.rms.web.map.version.release") }}
        </gp-button>
      </div>
    </gp-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";

export default {
  name: "DialogIssueMap",
  data() {
    return {
      remark: "",
      disable: false,
    };
  },
  computed: {
    ...mapState("mapManagement", ["dialogIssueMap"]),
    visible: {
      get() {
        return this.dialogIssueMap.visible;
      },
      set(val) {
        const { visible } = this.dialogIssueMap;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogIssueMap.rowData;
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["hideDialog"]),
    close() {
      this.remark = "";
    },
    submit() {
      this.disable = true;
      $req
        .post("/athena/map/version/release", {
          mapId: this.rowData.id,
          remark: this.remark,
        })
        .then(res => {
          this.reqSuccess(res.msg);
        })
        .catch(e => {
          this.disable = false;
        });
    },
    reqSuccess(msg) {
      this.disable = false;
      this.visible = false;
      this.$emit("refreshList");
      msg = $utils.Tools.transMsgLang(msg);
      this.$success(msg);
    },
  },
};
</script>

<style lang="less" scoped>
.tips {
  margin-bottom: 15px;
  cursor: default;

  &:hover {
    color: #409eff;
  }
}
</style>
