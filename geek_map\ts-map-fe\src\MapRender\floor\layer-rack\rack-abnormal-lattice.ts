/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

class LayerAbnormalLattice implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private shader: any;
  private fragment: number;
  private abnormalLatticeGeometries: any = [];
  private meshList: any = [];
  private abnormalVColor: any;
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "rackAbnormalLattice";
    container.zIndex = utils.getLayerZIndex("rack");
    container.interactiveChildren = false;
    container.visible = false;
    this.container = container;

    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.shader = utils.getShader("iconColor", utils.getResources("rack"));
    this.abnormalVColor = utils.getShaderColor("RACK_ABNORMAL"); //货箱架 异常颜色

    this.utils = utils;
  }

  render(): void {
    const utils = this.utils;
    const fragment = this.fragment;

    const abnormalLatticeGeometries = this.abnormalLatticeGeometries;
    const shader = this.shader;
    for (let i = 0, len = Math.ceil(abnormalLatticeGeometries.length / fragment); i < len; i++) {
      const arr = abnormalLatticeGeometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, shader);
      mesh.name = "rackAbnormalLattice";
      mesh.mapType = "rackAbnormalLattice";
      mesh.interactive = mesh.buttonMode = false;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }
  }

  drawGeometryAbnormalLattice(options: mShelfData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry(
      "iconColor",
      options["position"],
      this.abnormalVColor,
    );
    _this.abnormalLatticeGeometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.abnormalLatticeGeometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.abnormalLatticeGeometries = null;
    this.shader = null;
    this.container = null;
    this.fragment = null;
    this.utils = null;
    this.meshList = null;
  }
}
export default LayerAbnormalLattice;
