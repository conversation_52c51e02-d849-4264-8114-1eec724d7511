<template>
  <div class="edit-map-bg-status-choose">
    <strong>{{ $t("lang.rms.fed.wallOperation") }}:</strong>
    <gp-cascader
      v-model="type"
      :options="options"
      size="mini"
      class="edit-cascader"
      popper-class="edit-map-bg-cascader"
      @change="handleChange('type')"
    />
    <gp-input-number
      v-model="boundary"
      size="mini"
      :min="1"
      :max="60"
      :step="1"
      step-strictly
      @change="handleChange('boundary')"
    />
  </div>
</template>

<script>
export default {
  name: "ToolWall",
  props: {
    isShow: {
      type: Boolean,
      require: true,
    },
    defaultBoundary: {
      type: Number,
      require: true,
    },
  },
  data() {
    return {
      type: ["wallErase"],
      options: [
        {
          value: "wallErase",
          label: this.$t("lang.rms.fed.erase"),
        },
        {
          value: "addWall",
          label: this.$t("lang.rms.fed.addTo1"),
          children: [
            { value: "wallFree", label: this.$t("lang.rms.fed.free") },
            { value: "wallStraightLine", label: this.$t("lang.rms.web.map.segment.type.line") },
          ],
        },
      ],
      boundary: this.defaultBoundary,
    };
  },
  watch: {
    isShow(flag) {
      if (flag) {
        const last = this.type.length - 1;
        const type = this.type[last];
        this.$emit("change", type, this.boundary);
      }
    },
  },
  methods: {
    handleChange(key) {
      const last = this.type.length - 1;
      const type = this.type[last];

      if (key === "type") {
        this.$emit("change", type, null);
      } else if (key === "boundary") {
        this.$emit("change", null, this.boundary);
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
