/* ! <AUTHOR> at 2022/07/27 */

const MonitorMapSettings: MRender.MapSettings = {
  cellSpaceRatio: 0.1, // 格子之间的间隔比例
  floorSpace: 10, // 楼层间距 单位 m
  lineWidth: 3, // 线宽度：sLine | bezier 单位 cm
  lineArrowSize: 2, // 线空负载箭头大小：sLine | bezier 单位 cm
  robotPathWidth: 3, // 机器人轨迹线宽度：sLine | bezier 单位 cm

  // 各个元素层级
  zIndex: {
    background: 1,
    cell: 2,
    charger: 2,
    station: 2,
    device: 2,

    cellFeature: 3,
    cellFunc: 8,

    road: 4,
    load: 4,
    unload: 4,
    area: 4,

    robot: 5,
    robotBelt: 4,
    robotTrail: 4,
    robotOccupy: 4,

    shelf: 10,
    poppick: 10,
    xShelf: 10, // 四向车
    rack: 10,

    knockArea: 9,
    realtimeObstacle: 9,
    area4Next: 9,

    areaStopCover: 11,
  },
};

export default MonitorMapSettings;
