<template>
  <geek-main-structure>
    <gp-tabs :value="activeName" @tab-click="tabsNavChange" ref="myTabs">
      <gp-tab-pane
        v-for="item in permissionNavList"
        :label="$t(item.text)"
        :name="item.id"
        :key="item.id"
      ></gp-tab-pane>
    </gp-tabs>
    <keep-alive>
      <component :is="activeName" @goFirstTab="goFirstTab" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import ShelfQuery from "./shelfQuery";
import ShelfEnter from "./shelfEnter";
import ShelfOperations from "./shelfOperations";
import ShelfStatic from "./shelfStatic";
import ShelfStaticHistory from "./shelfStaticHistory";
import PositionQuery from "./positionQuery";

export default {
  components: { ShelfQuery, ShelfEnter, ShelfOperations, ShelfStatic, ShelfStaticHistory, PositionQuery },
  data() {
    return {
      permissionNavList: [],
      navList: [
        { permissionName: "TabShelfQueryManagePage", id: "ShelfQuery", text: "auth.rms.shelfQuery.page" },
        {
          permissionName: "TabPositionQueryManagePage",
          id: "PositionQuery",
          text: "lang.rms.fed.latticePage.latticeQuery",
        },
        { permissionName: "TabShelfAddManagePage", id: "ShelfEnter", text: "lang.rms.fed.shelfEntering" },
        {
          permissionName: "TabShelfHandleManagePage",
          id: "ShelfOperations",
          text: "lang.rms.fed.shelfOperations",
        },
        {
          permissionName: "TabShelfStaticAdjustControlPage",
          id: "ShelfStatic",
          text: "lang.rms.fed.shelfStaticAdjustmentControl",
        },
        {
          permissionName: "TabAdjustShelfControllerManagePage",
          id: "ShelfStaticHistory",
          text: "auth.rms.adjustShelfControllerManage.page",
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "shelfManage"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  beforeRouteLeave(to, from, next) {
    this.activeName = "";
    next();
  },
  methods: {
    goFirstTab() {
      const activeName = this.permissionNavList.length ? this.permissionNavList[0].id : "";
      this.activeName = activeName;
      // this.$refs.tabsNav.trigger(activeName);
    },
    tabsNavChange(target) {
      this.activeName = this.$refs.myTabs.$data.currentName;
    },
  },
};
</script>
<style lang="less" scoped>
.shelf-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>

<style lang="less">
.shelf-manage-panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100% -60px);
  .shelf-manage-panel-wrap__table {
    flex: 1;
  }
}
</style>
