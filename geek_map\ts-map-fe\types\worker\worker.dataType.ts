/* ! <AUTHOR> at 2023/04/20 */

namespace MWorker {
  export type dataType =
    | "wsInitFloors"
    | "wsInitDisplay"
    | "wsUpdateDisplay"
    | "wsLogicAreasDisplay"
    | "wsMapConfig"
    | "wsMessageCount"
    | "wsMessageCard"
    | "wsMessageDevice"
    | "wsMessageRobot"
    | "wsDeadLockRobots"
    | "wsQueryData"
    | "wsMessageTask"
    | "wsSpeedLimitAreasDisplay"
    | "wsMessageBoxUnRecoverable"
    | "wsClearRobotAreasDisplay";
}
