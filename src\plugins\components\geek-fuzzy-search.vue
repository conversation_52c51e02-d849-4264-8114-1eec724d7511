<!--模糊查询组件-->
<template>
  <gp-autocomplete
    v-model="disease"
    :fetch-suggestions="querySearch"
    :placeholder="$t(currentPlaceholder)"
    :trigger-on-focus="false"
    :style="{ width: width }"
    @select="handleSelect"
    @blur="onBlurHandler"
  />
</template>

<script>
export default {
  name: "FuzzySearch",
  data() {
    return {
      diseaseList: [],
      disease: "",
      robotStates: [],
      width: "100%",
      currentPlaceholder: "",
    };
  },
  props: ["queryType", "id"],

  mounted() {
    this.adapter();
  },

  watch: {
    queryType: {
      handler(nv, ov) {
        this.queryType = nv.queryType;
        this.adapter();
      },
    },
    id: {
      handler(nv, ov) {
        if (nv === null) {
          this.disease = "";
        } else {
          this.disease = String(nv);
        }
      },
    },
    deep: true,
  },

  deactivated() {
    Object.assign(this.$data, this.$options.data.call(this));
  },

  methods: {
    fetchDate(fetchType, parms) {
      let currentFetch;
      switch (fetchType) {
        case "Qrobot":
          currentFetch = "/athena/robotStatus/queryRobotIds";
          break;
        case "Qshelf":
          currentFetch = "/athena/shelf/queryShelfCodes";
          break;
        case "Qstation":
          currentFetch = "/athena/station/queryStationIds";
          break;
      }
      $req.get(currentFetch, parms).then(res => {
        this.diseaseList.length = 0;
        if (res.code === 0) {
          for (const i of res.data) {
            const item = {
              value: String(i),
            };
            this.diseaseList.push(item);
          }
        }
      });
    },
    querySearch(queryString, cb) {
      const diseaseList = this.diseaseList;
      const results = queryString ? diseaseList.filter(this.createFilter(queryString)) : diseaseList;
      cb(results);
    },
    createFilter(queryString) {
      return disease => {
        return String(disease.value).toLowerCase().indexOf(queryString.toLowerCase()) !== -1;
      };
    },
    handleSelect(item) {
      if ($utils.Type.isObject(item) && item.hasOwnProperty("value")) {
        this.fetchVal(item.value);
      } else {
        this.fetchVal(item);
      }
    },
    onBlurHandler(event) {
      if (this.selectVal === "" || this.selectVal === undefined) {
        this.fetchVal(event.currentTarget.value);
      }
    },
    fetchVal(val) {
      this.$emit("fuzzySearchBub", {
        type: this.queryType,
        pointer: val,
      });
    },
    adapter() {
      switch (this.queryType) {
        case "Qrobot":
          this.currentPlaceholder = "lang.rms.fed.pleaseEnterTheRobotID";
          this.fetchDate("Qrobot", {
            info: this.disease,
            robotStates: this.robotStates + "",
          });
          break;
        case "Qshelf":
          this.currentPlaceholder = "lang.rms.fed.pleaseEnterTheShelfID";

          this.fetchDate("Qshelf", { info: this.disease });
          break;
        case "Qstation":
          this.currentPlaceholder = "lang.rms.fed.pleaseEnter";

          if (this.disease !== "") {
            this.fetchDate("Qstation", { info: this.disease });
          }
          break;
      }
    },
    getDate() {
      return this.disease;
    },
  },
};
</script>
<style>
.gp-input__inner {
  padding: 0 0 0 8px;
}
</style>
