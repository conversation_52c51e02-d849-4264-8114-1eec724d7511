<template>
  <geek-main-structure class="panel-wrap">
    <div class="panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        @row-add="rowAdd"
        @row-del="rowDel"
        @row-export="rowExport"
        @row-application="rowApply"
      />
    </div>
    <editDialog ref="editDialog" :language-option="languageOption" @updateTableList="getTableList" />
  </geek-main-structure>
</template>


<script>
import editDialog from "./components/editDialog";
// import LanguageList from "./components/languageList";
// import LanguageAdd from "./components/languageAdd";

export default {
  name: "LanguageManage",
  components: { editDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabI18nControllerManageHasPage",
          id: "LanguageList",
          text: "lang.rms.fed.languageAdded", // "已添加语言"
        },
        {
          permissionName: "TabI18nControllerManageNewPage",
          id: "LanguageAdd",
          text: "lang.rms.fed.addNewLanguage", //"添加新语言"
        },
      ],
      activeName: "",
      tableConfig: {
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.fed.languageName", prop: "languageName" }, // "语言名称"
          { label: "lang.rms.fed.languageField", prop: "languageCode" }, // "语言字段"
          { label: "lang.rms.fed.uploadTime", prop: "createTime", width: "240" },
          { label: "lang.rms.fed.updateTime", prop: "updateTime", width: "240" },
          {
            label: "lang.rms.fed.listOperation",
            width: "200",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonExport",
                handler: "row-export",
              },
              {
                label: "lang.rms.fed.application",
                permission,
                handler: "row-application",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
                type: "danger",
              },
            ],
          },
        ],
      },

      languageOption: [],
      tableData: [],

    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "i18nControllerManage"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
    this.getTableList();
  },
  methods: {
    // tabsNavChange(id) {
    //   if (this.activeName === id) return;
    //   this.activeName = id;
    // },
    tabsNavChange(target) {
      this.activeName = this.$refs.myTabs.$data.currentName;
    },
    getTableList() {
      $req.get("/athena/api/coreresource/i18n/findAllLanguages").then(res => {
        let list = res?.data || [];
        list = list.map(item => {
          item.languageCode = item.code;
          item.languageName = item.name;
          return item;
        });
        this.tableData = list.filter(i => i.isDeleted === 0);
        this.languageOption = list;
      });
    },
    rowAdd() {
      this.$refs.editDialog.open();
    },
    rowDel(rowData) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req
          .postParams("/athena/api/coreresource/i18n/deleteLanguage", {
            languageCode: rowData.languageCode,
          })
          .then(res => {
            if (res.code === 0) {
              this.$success();
              this.getTableList();
            }
          });
      });
    },

    // 应用
    rowApply(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/applyLanguage", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            this.$success();
            $utils.Data.setLocalLang(rowData.languageCode);
            this.$emit("getTableList");
          }
        });
    },

    // 导出
    rowExport(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/exportI18nItem", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            if (window.location.host == "127.0.0.1" || window.location.host == "localhost") {
              window.open("http://" + process.env.VUE_APP_serverIP + res.data);
            } else {
              window.open(window.location.origin + res.data);
            }
          }
        });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.panel-wrap {
  display: flex;
  flex-direction: column;
  height: calc(100%);
  .panel-wrap__table {
    flex: 1;
  }
}
</style>
