/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerPPP implements MRender.Layer {
  private floorId: floorId;
  private mapCore: any;
  private container: PIXI.Container;
  private shader: any;
  private fragment: number;
  private geometries: Array<any> = [];
  private meshList: any = [];
  private data: Array<mShelfData> = [];
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "poppick";
    container.zIndex = utils.getLayerZIndex("poppick");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.shader = utils.getShader("iconColor", utils.getResources("poppick"));
    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const mapCore = _this.mapCore;
    const utils = mapCore.utils;
    const fragment = _this.fragment;
    const shader = _this.shader;

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);
      if (!arr.length) return;

      const sliceData = _this.data.slice(i * fragment, i * fragment + fragment);

      let meshKey = sliceData[0].code;
      let mesh = utils.createMesh(arr, shader);
      mesh.name = "poppick";
      mesh.name = meshKey;
      mesh.mapType = "poppick";
      mesh.interactive = mesh.buttonMode = true;

      mapCore.meshData.poppick.setData(meshKey, sliceData);
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
    this.geometries = [];
    this.data = [];
  }

  setGeometry(options: mShelfData, geometry: any): void {
    const _this = this;
    const mapCore = _this.mapCore;
    mapCore.mapEvent.updateSelect("poppick", options);
    mapCore.mapData.shelf.setData(options["code"], options);
    _this.data.push(options);
    _this.geometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(type?: "heat"): void {
    if (!type) {
      this.mapCore.meshData.poppick.delByFloorId(this.floorId);
    }
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
    this.data = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.shader = null;
    this.meshList = null;
    this.mapCore = null;
    this.data = null;
    this.floorId = undefined;
  }
}
export default LayerPPP;
