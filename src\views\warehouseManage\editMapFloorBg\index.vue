<template>
  <geek-main-structure :space="false" class="container">
    <bg-toolbar
      :current-active-init="currentActiveInit"
      :has-operate-history="!!bgHistory"
      :export-bg-show="exportBgShow"
      @change="changeToolbar"
      @save="saveBg"
      @exit="exitBg"
      @export="exportBg"
      @updateBgImage="updateBgImage"
      @change-bg="changeBg"
    />

    <models-tab
      class="modelsTabStyle"
      v-if="mapData"
      :bg-history="bgHistory"
      :robot-type-tabs="robotTypeTabs"
      :newactive-name="activeName"
      @addTab="handleAddTab"
      @activeName="handleActiveName"
    >
      <template #default="{ item }">
        <div v-if="item === activeName" class="canvas-draw-box">
          <keep-alive>
            <bg-canvas
              ref="floorCanvas"
              :map-data="mapData"
              :active-name="activeName"
              :zoom.sync="zoom"
              :min-zoom="minZoom"
              :max-zoom="maxZoom"
              :action-type="actionType"
              :action-attr="actionAttr"
              @setZoomMin="setZoomMin"
              @rotateChange="rotateChange"
              @historyChange="historyChange"
            />
          </keep-alive>
        </div>
      </template>
    </models-tab>
    <bg-upload
      v-show="!mapData || openBgBtn"
      class="bg-upload"
      :is-update-bg-image="isUpdateBgImage"
      :query-data="$route.query"
      :open-bg-btn="openBgBtn"
      :map-data="mapData"
      :add-data="addData"
      @openDialog="handleAddTab"
      @closeBgBtn="closeBgBtn"
      @exportBgShow="handExportBgShow"
    />
    <map-loading v-show="loading" />
    <ExportResources :export-visible="exportVisible" :active-name="activeName" @close="close" />
  </geek-main-structure>
</template>

<script>
import BgToolbar from "./components/bg-toolbar";
import ToolRotate from "./components/tool-rotate";
import ToolWall from "./components/tool-wall";
import ToolGround from "./components/tool-ground";
import CurrentOperation from "./components/current-operation";
import BgCanvas from "./components/bg-canvas";
import RotateCompass from "./components/rotate-compass";
import MapLoading from "../../components/map-loading";
import BgUpload from "./components/bg-upload";
import modelsTab from "./components/models-tab";
import ExportResources from "./components/export-resources";
import dealImage from "../../../libs/dealImage";
export default {
  name: "EditMapFloorBg",
  components: {
    BgToolbar,
    ToolRotate,
    ToolWall,
    ToolGround,
    CurrentOperation,
    BgCanvas,
    RotateCompass,
    MapLoading,
    BgUpload,
    modelsTab,
    ExportResources,
  },
  data() {
    return {
      mapId: "",
      floorId: "",
      mapData: null,
      apiReqData: null,

      action: "movement",
      subAction: "",
      actionAttr: {
        angle: -1,
        wallBoundary: 15,
        groundBoundary: 15,
      },
      zoom: -1,
      maxZoom: 300,
      minZoom: -1,

      bgHistory: 0,
      $floorCanvas: null,
      rotateCompassShow: false,
      loading: false,
      openBgBtn: false,
      exportBgShow: false,
      robotTypeTabs: [],
      activeName: undefined,
      currentActiveInit: "",
      exportVisible: false,
      addData: false,
      isUpdateBgImage: false,
    };
  },
  computed: {
    actionType() {
      return this.subAction || this.action;
    },
  },
  activated() {
    const { mapId, floorId } = this.$route.query;
    this.loading = true;
    this.mapId = mapId;
    this.floorId = floorId;
    this.getRobotTypes();
  },
  deactivated() {
    Object.assign(this.$data, this.$options.data());
  },
  methods: {
    initFun() {
      this.getMapInfo();
    },
    getRobotTypes(data) {
      const params = "/" + this.mapId + "/" + this.floorId;
      $req.get("/athena/map/draw/supportRobotTypes" + params).then(res => {
        if (res.code === 0) {
          this.robotTypeTabs = res.data;
          if (data) this.activeName = data[0];
          if (!this.activeName) this.activeName = res.data.length > 0 ? this.robotTypeTabs[0] : undefined;
          this.handleActiveName(this.activeName);
        }
      });
    },
    changeToolbar(action) {
      if (action === "revoke") {
        // 撤销
        this.$floorCanvas.operateRevoke();
        return;
      }
      if (action === this.action) return;
      this.action = action;
      this.subAction = "";
      this.rotateCompassShow = false;
    },
    rotateChange(rotateType, angle) {
      if (rotateType) {
        this.subAction = rotateType;
        if (rotateType === "rotateCompass") this.rotateCompassShow = true;
        else this.rotateCompassShow = false;
      }
      if (angle !== null && angle !== this.actionAttr.angle) {
        this.$set(this.actionAttr, "angle", angle);
      }
    },
    wallChange(wallType, boundary) {
      if (wallType) this.subAction = wallType;
      if (boundary !== null) this.$set(this.actionAttr, "wallBoundary", boundary);
    },
    groundChange(groundType, boundary) {
      if (groundType) this.subAction = groundType;
      if (boundary !== null) this.$set(this.actionAttr, "groundBoundary", boundary);
    },
    historyChange(historyLen) {
      this.bgHistory = historyLen;
    },
    saveBg() {
      this.loading = true;
      try {
        const base64Text = this.$floorCanvas.getDataURL();
        const rotate = this.actionAttr.angle;
        const printing = base64 => {
          const apiReqData = this.apiReqData;
          const data = {
            mapId: apiReqData.mapId,
            floorId: apiReqData.floorId,
            locationX: apiReqData.locationX,
            locationY: apiReqData.locationY,
            base64Text,
            compressData: base64,
            robotType: this.activeName,
            resolution: apiReqData.resolution,
            // yaw: apiReqData.yaw,
            yaw: rotate,
            scale: apiReqData.scale,
          };
          $req.post("/athena/map/draw/updateBaseMap", data).then(res => {
            this.loading = false;
            this.bgHistory = 0;
            this.$success(this.$t(res.msg));
          });
        };
        dealImage(base64Text, printing);
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },

    updateBgImage() {
      // 上传背景图改成与更新资源一致了
      this.isUpdateBgImage = true;
      this.openBgBtn = true;

      // this.createUploadFile().then(file => {
      //   const { apiReqData } = this;
      //   const formData = new FormData();
      //   formData.append("file", file);
      //   $req
      //     .post(
      //       `/athena/map/draw/importShowBackgroundImage?mapId=${apiReqData.mapId}&floorId=${apiReqData.floorId}`,
      //       formData,
      //     )
      //     .then(({ code, msg }) => {
      //       if (!code) {
      //         this.$success(this.$t(msg));
      //       }
      //     });
      // });
    },

    createUploadFile() {
      return new Promise(resolve => {
        const upLoadEl = document.createElement("input");
        upLoadEl.type = "file";
        upLoadEl.name = "file";
        upLoadEl.accept = ".png";
        document.body.appendChild(upLoadEl);
        upLoadEl.addEventListener("change", () => {
          const fileData = upLoadEl.files[0];
          resolve(fileData);
        });
        upLoadEl.click();
      });
    },
    exitBg() {
      if (this.bgHistory > 0) {
        this.$confirm("lang.rms.fed.existUnsaveContent", {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.notSave"),
          type: "warning",
        }).catch(() => {
          this.$router.go(-1);
        });
      } else {
        this.$router.go(-1);
      }
    },
    async getMapInfo() {
      if (!this.activeName) {
        this.mapData = null;
        this.loading = false;
        return;
      }
      const params = "/" + this.mapId + "/" + this.floorId + "/" + this.activeName;
      try {
        const res = await $req.get("/athena/map/draw/mapImage" + params);
        const data = res.data;
        this.loading = false;
        if (!data) {
          this.$error("接口没有返回数据", false);
          return;
        }
        if (!data.compressData) {
          return false;
        }
        this.apiReqData = data;
        let mapData = {};
        const rotate = data.yaw || 0;
        this.exportBgShow = true;
        mapData = {
          zoomImage: true,
          resolution: data.resolution || 0.02,
          url: data.compressData,
          height: data.height,
          width: data.width,
          rotate,
          yaw: data.yaw,
          compressData: data.compressData,
          imageData: data.base64Text,
          locationX: data.locationX,
          locationY: data.locationY,
          offsetX: data.offsetX,
          offsetY: data.offsetY,
          robotType: this.activeName,
          display: data.display,
          resourceType: data.resourceType,
          backgroundId: data.backgroundId,
        };
        this.mapData = mapData;
        this.mapData.robotType = this.activeName;
        if (!mapData.url) {
          // this.$error("没有地图,请先绘制", false);
          return;
        }
        this.$nextTick(_ => {
          if (!this.$floorCanvas) {
            this.$floorCanvas = this.$refs.floorCanvas;
          }
          this.$floorCanvas.drawImage(mapData);
        });
        this.loading = false;
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },
    scaleImage() {
      this.$floorCanvas.scaleImage("slider");
    },
    setZoomMin(min) {
      this.minZoom = min;
    },
    // 导出
    exportBg() {
      this.exportVisible = true;
    },
    changeBg(val) {
      this.isUpdateBgImage = false;
      if (this.bgHistory > 0) {
        this.$confirm("lang.rms.fed.existUnsaveContent", {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.notSave"),
          type: "warning",
        }).catch(() => {
          if (!val) {
            this.loading = true;
            this.openBgBtn = true;
          }
          this.removeBg();
        });
      } else if (this.bgHistory <= 0 && !val) this.openBg();
    },
    removeBg() {
      this.currentActiveInit = this.activeName;
      this.action = "movement";
      this.subAction = "";
      this.bgHistory = 0;
      this.rotateCompassShow = false;
      this.$floorCanvas.drawImage(this.mapData);
      this.actionAttr = this.$options.data.call(this).actionAttr;
      this.zoom = -1;
      this.minZoom = -1;
    },
    async openBg() {
      this.loading = true;
      await this.activeTabFun(this.activeName);
      this.isUpdateBgImage = false;
      this.openBgBtn = true;
    },
    closeBgBtn() {
      this.isUpdateBgImage = false;
      this.openBgBtn = false;
      this.loading = false;
      this.addData = false;
    },
    handExportBgShow(data) {
      this.isUpdateBgImage = false;
      this.exportBgShow = true;
      this.openBgBtn = false;
      this.addData = false;
      this.getRobotTypes(data);
    },
    handleActiveName(val) {
      this.currentActiveInit = val;
      if (this.bgHistory > 0 && !this.openBgBtn) {
        this.changeBg(val);
      } else if (this.openBgBtn) {
        return false;
      } else {
        this.activeTabFun(val);
      }
    },
    activeTabFun(val) {
      this.action = "movement";
      this.subAction = "";
      this.bgHistory = 0;
      this.rotateCompassShow = false;
      this.$floorCanvas = null;
      this.actionAttr = this.$options.data.call(this).actionAttr;
      this.zoom = -1;
      this.minZoom = -1;
      this.activeName = val;
      this.getMapInfo();
    },
    handleAddTab() {
      this.isUpdateBgImage = false;
      this.openBgBtn = true;
      this.addData = true;
    },
    close() {
      this.exportVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.status-bar {
  .g-flex();
  padding: 10px 20px 0 20px;
  height: 32px;
  > .status-operation {
    margin-left: auto;
    text-align: right;
  }
}
.bg-upload {
  height: 100%;
}
.close-upload {
  display: none;
}

.canvas-draw-box {
  position: relative;
  width: 100%;
  height: calc(100% - 100px);
  padding: 0 20px;
  overflow: hidden;

  .zoom-bar {
    position: absolute;
    right: 20px;
    top: 50%;
    margin-top: -160px;

    > span {
      display: block;
      font-size: 13px;
      text-align: center;
      padding-bottom: 12px;
    }
  }
}
:deep(.gp-tabs, .gp-tabs__content, .gp-tab-pane) {
  width: 100%;
  height: calc(100% - 15px);
}
:deep(.gp-tabs__header) {
  padding-left: 20px;
}

.modelsTabStyle {
  display: flex;
  flex-direction: column;
  :deep(.el-tabs__content) {
    flex: 1;
  }

  :deep(.el-tab-pane) {
    height: 100%;
  }

  :deep(.canvas-draw-box) {
    height: 100%;
    overflow: hidden;
  }
}
</style>
