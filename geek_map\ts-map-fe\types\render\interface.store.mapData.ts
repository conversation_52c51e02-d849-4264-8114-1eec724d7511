/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /** MapData 类 */
  export interface MapDataMain {
    /** cell数据 */
    cell: MapData;
    /** segment数据 */
    segment: MapData;
    /** shelf数据 */
    shelf: MapData;
    /**rack数据 */
    rack: MapData;
    /** robot数据 */
    robot: MapData;
    /** charger数据 */
    charger: MapData;
    /** 机器人死锁数据 */
    deadRobot: MapData | any;
    /** device数据 */
    device: MapData;
    /** station数据 */
    station: MapData | any;
    /** 急停区域数据 */
    stopArea: MapData | any;
    /**
     * 数据卸载
     */
    uninstall(): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }

  /** layer 类 */
  export interface MapData {
    /**
     * 添加MapData
     * @param code 唯一code
     * @param data 相对应的数据类型
     */
    setData(key: code | floorId, data: any): void;

    /**
     * 根据code获取数据
     * @param code 唯一code
     */
    getData(code: code): any;

    /**
     * 获取floorId的当前数据
     * @param floorId
     */
    getByFloorId(floorId: floorId): any[] | void;

    /**
     * 获取所有MapData
     */
    getAll(): { [propName: code]: any };

    /**
     * 根据code删除数据
     * @param code 唯一code
     */
    delData(code: code): void;

    /**
     * 数据卸载
     */
    uninstall(data: any): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }
}
