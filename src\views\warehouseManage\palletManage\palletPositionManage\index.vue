<template>
  <section class="pallet-manage-panel-wrap">
    <geek-customize-form
      inSearchPage
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
    />
    <div class="pallet-manage-panel-wrap__table">
      <geek-customize-table
        :table-config="tableConfig"
        :data="tableData"
        :page="tablePage"
        @page-change="pageChange"
        @row-add="rowAdd"
        @row-edit="rowEdit"
        @row-del="rowDel"
      />
    </div>

    <pallet-position-manage-detail ref="detail" @updateList="getTableList" />
  </section>
</template>

<script>
import PalletPositionManageDetail from "./components/detail.vue";
export default {
  name: "PalletRackManage",
  components: { PalletPositionManageDetail },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      listVisible: true,
      form: { palletLatticeCode: "", palletRackCode: "" },
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          palletLatticeCode: {
            label: "lang.rms.palletPositionManage.palletLatticeCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          palletRackCode: {
            label: "lang.rms.palletPositionManage.palletRackCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          {
            label: "lang.rms.palletPositionManage.palletLatticeCode",
            prop: "palletLatticeCode",
          },
          {
            label: "lang.rms.palletPositionManage.palletRackCode",
            prop: "palletRackCode",
          },
          {
            label: "lang.rms.palletPositionManage.layer",
            prop: "layer",
          },
          {
            label: "lang.rms.palletPositionManage.height",
            prop: "height",
          },
          {
            label: "lang.rms.palletPositionManage.applyPalletCode",
            prop: "applyPalletCode",
          },
          {
            label: "lang.rms.palletPositionManage.occupyPalletCode",
            prop: "occupyPalletCode",
          },
          {
            label: "lang.rms.palletPositionManage.palletLatticeHostCode",
            prop: "palletLatticeHostCode",
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                fixed: "right",
                width: "140",
                operations: [
                  {
                    label: "lang.rms.fed.buttonEdit",
                    handler: "row-edit",
                  },
                  {
                    label: "auth.rms.mapManage.button.delete",
                    handler: "row-del",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.detail.open("add", {});
    },
    rowEdit(row) {
      this.$refs.detail.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req.get("/athena/palletLattice/remove", { palletLatticeCode: row.palletLatticeCode }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.rms.api.result.ok"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 列表接口请求
    getTableList() {
      const params = {
        ...this.form,
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        params: true,
      };
      $req.get("/athena/palletLattice/findList", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          total: result.recordCount || 0,
        });
      });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scope></style>
