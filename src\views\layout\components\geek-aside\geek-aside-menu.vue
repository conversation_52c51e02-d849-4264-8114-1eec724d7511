<template>
  <gp-menu
    :collapse="sidebarCollapse"
    :collapse-transition="false"
    :default-active="$route.path"
    :background-color="null"
    class="geek-aside-menu"
    :class="{ 'geek-aside-menu': true, 'geek-aside-menu--dark': isDark }"
    with-collapse
    @with-collapse-change="val => $emit('with-collapse-change', val)"
  >
    <template v-for="(item, index) in menuList">
      <gp-menu-item v-if="!item.children.length" :key="item.path" :index="item.path">
        <gp-icon :name="`gp-icon-${iconMap[item.icon]}`"></gp-icon>
        <span slot="title">{{ $t(item.title) }}</span>
        <a :href="'#' + item.path" class="custom-href"> </a>
      </gp-menu-item>
      <gp-submenu
        v-else
        :key="`${index}_${item.path}`"
        :index="item.path"
        popper-append-to-body
        :popper-class="`geek-aside-pop-menu submenu-${item.icon} ${isDark ? 'geek-aside-pop-menu--dark' : ''}`"
      >
        <template slot="title">
          <!-- <i class="menu-icon" :class="`gp-icon-${item.icon}`"></i> -->
          <gp-icon :name="`gp-icon-${iconMap[item.icon]}`"></gp-icon>
          <span slot="title">{{ $t(item.title) }}</span>
        </template>

        <template v-for="subItem in item.children">
          <a :href="'#' + subItem.path" class="menu-a-link" v-if="!subItem.children">
            <gp-menu-item :key="subItem.path" :index="subItem.path">
              <span slot="title">{{ $t(subItem.title) }}</span>
            </gp-menu-item>
          </a>
          <gp-submenu
            v-else
            :key="`${index}_${subItem.path}`"
            :index="subItem.path"
            popper-append-to-body
            :popper-class="`geek-aside-pop-menu ${isDark ? 'geek-aside-pop-menu--dark' : ''}`"
          >
            <template slot="title">{{ $t(subItem.title) }}</template>
            <a :href="'#' + ccItem.path" class="menu-a-link" v-for="ccItem in subItem.children">
              <gp-menu-item :key="ccItem.path" :index="ccItem.path">
                <span slot="title">{{ $t(ccItem.title) }}</span>
              </gp-menu-item>
            </a>
          </gp-submenu>
        </template>
      </gp-submenu>
    </template>

    <template #footer>
      <div :class="{ 'geek-aside-menu__switch': true, 'active-dark': darkTheme === 'dark' }">
        <gp-icon
          name="gp-icon-sunny"
          :class="{ 'is-active': darkTheme === 'light' }"
          @click="() => (darkTheme = 'light')"
        ></gp-icon>
        <gp-icon
          name="gp-icon-moon"
          :class="{ 'is-active': darkTheme === 'dark' }"
          @click="() => (darkTheme = 'dark')"
        ></gp-icon>
      </div>
    </template>
  </gp-menu>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "GeekAsideMenu",
  props: ["sidebarCollapse"],
  computed: {
    ...mapState(["menuList"]),
    isDark() {
      return this.darkTheme === "dark";
    },
  },
  data() {
    return {
      darkTheme: "dark",
      iconMap: {
        dashboard: "s-home",
        location: "location",
        info: "info",
        warehouse: "warehouse",
        component: "menu",
        example: "s-help",
        documentation: "documentation",
        peoples: "user-solid",
      },
    };
  },
};
</script>

<style lang="less" scoped>
.geek-aside-menu {
  width: 240px;
  &.gp-menu-wrap--collapse {
    width: 48px;
  }
  :deep(.gp-menu) {
    width: 100% !important;
  }
  :deep(a, a:active) {
    color: inherit;
    font-weight: inherit;
  }
  span {
    font-weight: inherit;
  }
}

.geek-aside-menu--dark {
  --gp-color-white: rgba(20, 32, 46, 0.9);
  --gp-color-icon1-primary: rgba(200, 205, 211, 1);
  --gp-color-line4-light: rgba(75, 86, 104, 1);
  --gp-color-Bg4-light: rgba(45, 58, 74, 1);
  --gp-color-text2-primary: rgba(255, 255, 255, 1);
  --menu-item-active-font-color: #fff;
  --menu-item-hover-fill: rgba(42, 69, 97, 1);
  --menu-submenu-title-color: #fff;
  background: rgba(26, 35, 47, 1);
  :deep(.is-active .gp-icon) {
    color: #fff !important;
  }
}

.geek-aside-menu__switch {
  width: 80%;
  margin: 0 auto;
  display: flex;
  text-align: center;
  position: relative;
  height: 24px;
  .gp-icon {
    color: rgba(75, 86, 104, 1);
    font-size: 14px;
    background: transparent;
    z-index: 2;
    flex: 1;
    &.is-active {
      color: rgba(134, 144, 156, 1);
    }
  }
  &::after {
    content: "";
    display: block;
    width: 50%;
    height: 24px;
    border-radius: 4px;
    background: rgba(229, 230, 235, 1);

    z-index: 1;
    position: absolute;
    transition: all 0.3s ease;
    left: 0;
  }
  &.active-dark {
    &::after {
      background: rgba(45, 58, 74, 1);
      left: 50%;
    }
  }
}
</style>
<style lang="less">
.geek-aside-pop-menu--dark {
  a,
  a:active {
    color: inherit;
    font-weight: inherit;
  }
  --gp-color-line4-light: rgba(20, 32, 46, 0.9);
  --gp-color-white: rgba(20, 32, 46, 0.9);
  --gp-color-text2-primary: rgba(255, 255, 255, 1);
  --menu-item-hover-fill: rgba(42, 69, 97, 1);
  :deep(.is-active .gp-icon) {
    color: #fff !important;
  }
}
.custom-href {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background: transparent;
}
</style>
