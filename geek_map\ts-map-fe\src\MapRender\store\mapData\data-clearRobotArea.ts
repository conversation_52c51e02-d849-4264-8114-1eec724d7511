/* ! <AUTHOR> at 2023/06/28 */
/** 新增清除区域 */
type clearRobotArea = { element: any; isShow: boolean };

class clearRobotAreasData implements MRender.MapData {
  private mapData: { [propName: code]: clearRobotArea } = {};
  /** 当前清除区域的 areaID */
  private currentClearRobotAreaIds: Array<code> = [];

  setData(code: code, data: clearRobotArea) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  getAll() {
    return this.mapData;
  }

  /**
   * 获取当前显示的clearRobotAreaIds
   * @returns ids
   */
  getIds(): Array<code> {
    return this.currentClearRobotAreaIds;
  }

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  /**
   * 根据传入的areaIds显示限速area
   * @param areaIds 传入的需要显示的areaId
   */
  showClearRobotArea(areaIds: Array<code> = []) {
    const mapData = this.mapData;

    areaIds.forEach(areaId => {
      if (mapData[areaId].isShow) return;
      mapData[areaId].element.visible = true;
    });
  }

  /**
   * 根据传入的areaIds自动判断显隐
   * @param areaIds
   */
  toggleClearRobotArea(areaIds: Array<code>) {
    // debugger;
    this.currentClearRobotAreaIds = areaIds;
    const clearRobotAreas = this.mapData;
    let clearRobotArea;
    for (let areaId in clearRobotAreas) {
      clearRobotArea = clearRobotAreas[areaId];
      if (areaIds.includes(areaId)) {
        if (clearRobotArea.isShow) continue;
        else {
          clearRobotArea.isShow = true;
          clearRobotArea.element.visible = true;
        }
      } else {
        if (!clearRobotArea.isShow) continue;
        clearRobotArea.isShow = false;
        clearRobotArea.element.visible = false;
      }
    }
  }

  /**
   * 根据当前数据更新一下clearRobotArea
   */
  repaint() {
    const mapData = this.mapData;

    let clearRobotArea;
    for (let areaId in mapData) {
      clearRobotArea = mapData[areaId];
      if (clearRobotArea.isShow) continue;
      else clearRobotArea.element.visible = false;
    }
  }

  uninstall() {
    const data = this.mapData;

    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};

    this.currentClearRobotAreaIds = [];
  }

  destroy() {
    this.uninstall();
  }
}

export default clearRobotAreasData;
