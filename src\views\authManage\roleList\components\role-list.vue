<template>
  <!-- 角色列表 -->
  <geek-main-structure>
    <geek-customize-form ref="roleForm" :form-config="formConfig" @on-query="getRoleList" @on-reset="getRoleList" />

    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @row-add="rowAdd"
      @row-edit="rowEdit"
      @row-delete="rowDelete"
      @row-status="rowStatus"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #status="{ row }">
        <gp-tag v-if="row.status == 0" size="mini" type="danger">{{ $t("lang.rms.fed.textProhibit") }}</gp-tag>
        <gp-tag v-else size="mini" type="success">{{ $t("lang.rms.fed.textEnable") }}</gp-tag>
      </template>
    </geek-customize-table>
  </geek-main-structure>
</template>

<script>
export default {
  data() {
    return {
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          userNameAsFuzz: {
            label: "lang.rms.fed.inputRoleName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          descr: {
            label: "lang.rms.fed.inputRoleRoleDescription",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          phoneAsFuzz: {
            label: "lang.rms.fed.listPermissionType",
            default: "",
            tag: "input",
            disabled: true,
            placeholder: "lang.rms.fed.pagePermission",
          },
          status: {
            label: "lang.rms.fed.listState",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "1",
                label: "lang.rms.fed.textEnable",
              },
              {
                value: "0",
                label: "lang.rms.fed.buttonProhibit",
              },
            ],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
            permission: this.checkPermission("AuthAddRolePermission"),
          },
        ],
        columns: [
          {
            label: "lang.rms.fed.inputRoleName",
            prop: "name",
          },
          {
            label: "lang.rms.fed.inputRoleRoleDescription",
            prop: "descr",
          },
          {
            label: "lang.rms.fed.inputPermissionType",
            prop: "type",
            formatter: (row, column) => {
              return row[column] == 1 ? this.$t("lang.rms.fed.page") : "";
            },
          },
          {
            label: "lang.rms.fed.system",
            width: "80",
            formatter: (row, column) => {
              return "RMS";
            },
          },
          {
            label: "lang.rms.fed.inputState",
            prop: "status",
            slotName: "status",
            width: "90",
            align: "center",
          },
          {
            label: "lang.rms.fed.listCreationTime",
            prop: "createTime",
            formatter: (row, column) => {
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listEditingTime",
            prop: "updateTime",
            formatter: (row, column) => {
              return $utils.Tools.formatDate(row[column], "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listEditor",
            prop: "editer",
          },
          {
            label: "lang.rms.fed.listOperation",
            operations: [
              {
                label: "lang.rms.fed.buttonEdit",
                handler: "row-edit",
                permission: this.checkPermission("AuthEditRolePermission"),
              },
              {
                label: "lang.rms.fed.buttonDelete",
                handler: "row-delete",
                isDisabled: row => {
                  return [1, 2].includes(row.roleId);
                },
                permission: this.checkPermission("AuthDelRole"),
              },
              {
                handler: "row-status",
                labelFormat: row => {
                  let buttonStatus = "";
                  if (row.status === 1) {
                    buttonStatus = "lang.rms.fed.textProhibit";
                  } else {
                    buttonStatus = "lang.rms.fed.textEnable";
                  }
                  return buttonStatus;
                },
                isDisabled: row => {
                  return [1, 2].includes(row.roleId);
                },
                permission: this.checkPermission("AuthEnableRole"),
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getRoleList();
  },
  methods: {
    rowAdd() {
      this.$emit("setCurrentOperation", "add", null);
    },
    rowEdit(row) {
      this.$emit("setCurrentOperation", "edit", row);
    },
    rowDelete(row) {
      this.$geekConfirm(this.$t("lang.venus.web.common.isDeleted")).then(() => {
        $req
          .post("/athena/api/coreresource/auth/role/delRole/v1", {
            roleId: row.roleId,
          })
          .then(res => {
            this.getRoleList();
          });
      });
    },
    rowStatus(row) {
      // 0表示禁用，1表示启用
      if (row.status === 1) {
        $req
          .post("/athena/api/coreresource/auth/role/disableRole/v1", {
            roleId: row.roleId,
            name: row.name,
          })
          .then(res => {
            this.getRoleList();
          });
      } else {
        $req
          .post("/athena/api/coreresource/auth/role/enableRole/v1", {
            roleId: row.roleId,
            name: row.name,
          })
          .then(res => {
            this.getRoleList();
          });
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getRoleList();
    },
    // 列表接口请求
    getRoleList() {
      const formData = this.$refs.roleForm.getData();

      const params = Object.assign({}, formData, {
        pageSize: this.tablePage.pageSize,
        currentPage: this.tablePage.currentPage,
      });
      $req
        .get("/athena/api/coreresource/auth/role/pageQuery/v1", {
          ...params,
          params: true,
        })
        .then(res => {
          const data = res.data || {};
          this.tableData = data.recordList;
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: data.currentPage || 1,
            total: data.recordCount,
          });
        });
    },
  },
};
</script>

<style lang="less" scope></style>
