<template>
  <section class="shelf-manage-panel-wrap">
    <geek-customize-form
      ref="customizeFormRef"
      :form-config="formConfig"
      @get-shelf="getShelf"
      @on-reset="onReset"
      style="padding: 5px 0"
    />
    <gp-divider content-position="left">{{ $t("lang.rms.fed.shelfInTheTask") }}</gp-divider>
    <div class="shelf-manage-panel-wrap__table">
      <geek-customize-table :table-config="tableConfig" :data="tableData" @send-shelf="sendShelf">
        <template #shelfSide="{ row }">
          <div v-if="row.shelfState === 'READY'">
            <gp-button
              v-for="side in shelfSideList"
              :key="side.value"
              :type="row.shelfSide.slice(0, 1) === side.value ? 'success' : 'primary'"
              :disabled="row.shelfSide === side.value || checkoutSideDisabled(side.value)"
              size="small"
              @click="turnShelf(row, side.value)"
            >
              <span>{{ $t(side.label) }}</span>
            </gp-button>
          </div>
        </template>
        <template #operation="{ row }">
          <gp-button v-if="row.shelfState === 'READY'" size="small" @click="sendShelf(row)">
            <span>{{ $t("lang.rms.fed.deliveryShelf") }}</span>
          </gp-button>
        </template>
      </geek-customize-table>
    </div>
  </section>
</template>

<script>
export default {
  data() {
    const shelfSideList = [
      { value: "F", label: "lang.rms.fed.fSide" },
      { value: "B", label: "lang.rms.fed.bSide" },
      { value: "L", label: "lang.rms.fed.lSide" },
      { value: "R", label: "lang.rms.fed.rSide" },
    ];
    return {
      shelfSideList,
      form: {
        stationId: "",
        shelfCode: "",
        shelfSide: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          stationId: {
            label: "lang.rms.fed.selectWorkstation",
            default: "",
            tag: "select",
            options: [],
          },
          shelfCode: {
            label: "lang.rms.fed.shelfNumber",
            default: "",
            tag: "input",
          },
          shelfSide: {
            label: "lang.rms.fed.selectShelfOrientation",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: shelfSideList,
          },
        },
        operations: [
          {
            label: "lang.rms.fed.pick-upShelves",
            handler: "get-shelf",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tableConfig: {
        attrs: {
          index: true,
        },
        columns: [
          { label: "lang.rms.fed.shelfNumber", prop: "shelfCode", width: "100" },
          { label: "lang.rms.fed.robot", prop: "robotId" },
          {
            label: "lang.rms.fed.state",
            prop: "shelfState",
            formatter: (row, column) => {
              switch (row[column]) {
                case "READY":
                  return this.$t("lang.rms.fed.inTheOperation");
                case "QUEUING":
                  return this.$t("lang.rms.fed.queueing");
                case "FETCHING":
                  return this.$t("lang.rms.fed.beingCarried");
                case "GO_TURN":
                  return this.$t("lang.rms.fed.turningSide");
                case "GO_RETURN":
                  return this.$t("lang.rms.fed.return");
                default:
                  return "--";
              }
            },
          },
          { label: "lang.rms.fed.turnTheShelf", prop: "shelfSide", slotName: "shelfSide" },
          { label: "lang.rms.fed.operation", prop: "operation", slotName: "operation" },
        ],
      },

      taskTimmer: null,
    };
  },
  activated() {
    this.getStations();
    this.getTaskByStation();
    // this.taskSetInterval = setInterval(() => {
    //   this.getTaskByStation();
    // }, 2000);
  },
  deactivated() {
    if (this.taskTimmer) {
      clearTimeout(this.taskTimmer);
      this.taskTimmer = null;
    }
  },
  methods: {
    getTaskByStation() {
      if (this.taskTimmer) {
        clearTimeout(this.taskTimmer);
        this.taskTimmer = null;
      }

      this.form = this.$refs.customizeFormRef.getData();

      $req.get("/athena/task/findByStation", { stationId: this.form.stationId }).then(res => {
        this.tableData = res?.data || [];

        this.taskTimmer = setTimeout(() => {
          this.getTaskByStation();
        }, 2000);
      });
    },
    getShelf(formData) {
      this.form = formData;
      if (!formData.shelfCode || !formData.stationId) return;
      $req.get("/athena/shelf/exists", { shelfCode: formData.shelfCode }).then(res => {
        if (res.data.status === 2) {
          this.$error(this.$t(res?.data?.descr || ""));
        } else {
          $req
            .get("/athena/task/deleverToStation/fetch", {
              stationId: formData.stationId,
              shelfCode: formData.shelfCode,
              shelfSide: formData.shelfSide,
            })
            .then(res => {
              this.getTaskByStation();
            });
        }
      });
    },
    onReset(val) {
      this.form = Object.assign({}, val);
    },
    turnShelf(value, side) {
      $req
        .post(
          "/athena/task/deleverToStation/turn",
          $utils.Tools.getParams({
            taskId: value.taskId,
            stationId: value.stationId,
            shelfSide: side,
          }),
          { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
        )
        .then(res => {
          this.getTaskByStation();
        });
    },
    sendShelf(value) {
      $req
        .get("/athena/task/deleverToStation/return", {
          taskId: value.taskId,
          stationId: value.stationId,
        })
        .then(res => {
          this.getTaskByStation();
        });
    },
    checkoutSideDisabled(value) {
      const $rmsConfig = $utils.Data.getRMSConfig();
      return $rmsConfig.disableShelfSide && $rmsConfig.disableShelfSide.includes(value);
    },

    getStations() {
      $req.get("/athena/station/findAll").then(res => {
        let list = res?.data || [];
        this.formConfig.configs.stationId.options = list.map(item => {
          return { value: item.id, label: item.id, appendText: "lang.rms.fed.workstation" };
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.form-content {
  .gp-select {
    width: 100%;
  }
}
:deep(.gp-divider--horizontal) {
  margin: 16px 0;
}
</style>
