<template>
  <gp-dialog
    :title="$t('lang.rms.fed.changePassword')"
    :visible.sync="visible"
    append-to-body
    :before-close="handleCancel"
    width="460px"
  >
    <gp-row>
      <gp-col :span="12">
        <div class="grid-content">
          <gp-row><span class="redStar">*</span>{{ $t("lang.rms.fed.inputUserName") }}</gp-row>
          <gp-row class="spaceV">
            <gp-input v-model="userName" :disabled="true" :placeholder="$t('lang.rms.fed.pleaseEnterContent')" />
            <p v-show="!userName" class="redStar">
              {{ $t("lang.auth.UserAPI.item0120") }}
            </p>
          </gp-row>
        </div>
      </gp-col>
      <gp-col :span="12">
        <div class="grid-content">
          <gp-row><span class="redStar">*</span>{{ $t("lang.rms.fed.password") }}</gp-row>
          <gp-row class="spaceV">
            <!-- <gp-input
              v-model="oldPassword"
              type="password"
              :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              @blur="oldPasswordShow = !oldPassword"
            /> -->
            <input
              v-model="oldPassword"
              :type="oldPasswordType"
              :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              @blur="oldPasswordShow = !oldPassword"
              autocomplete="on"
              class="pass-input"
            />
            <span class="pwd-eye" :class="{ show: oldPasswordEyeShow }" @click="changeOldPwdShow" />
            <p v-show="!oldPassword && oldPasswordShow" class="redStar">
              {{ $t("lang.rms.fed.password") + $t("lang.rms.fed.canNotBeEmpty") }}
            </p>
          </gp-row>
        </div>
      </gp-col>
    </gp-row>
    <gp-row>
      <gp-col :span="12">
        <div class="grid-content">
          <gp-row><span class="redStar">*</span>{{ $t("lang.rms.fed.newPassword") }}</gp-row>
          <gp-row class="spaceV">
            <!-- <gp-input
              v-model="password"
              type="password"
              :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              @blur="passwordShow = !password"
            /> -->
            <input
              v-model="password"
              :type="passwordType"
              :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              @blur="passwordShow = !password"
              autocomplete="on"
              class="pass-input"
            />
            <span class="pwd-eye" :class="{ show: passwordEyeShow }" @click="changePwdShow" />
            <p v-show="!password && passwordShow" class="redStar">
              {{ $t("lang.rms.fed.newPassword") + $t("lang.rms.fed.canNotBeEmpty") }}
            </p>
          </gp-row>
        </div>
      </gp-col>
    </gp-row>
    <span slot="footer">
      <gp-button key="back" @click="handleCancel">{{ $t("lang.rms.fed.cancel") }}</gp-button>
      <gp-button key="submit" type="primary" :loading="loading" @click="handleOk">
        {{ $t("lang.rms.fed.save") }}
      </gp-button>
    </span>
  </gp-dialog>
</template>
<script>
import md5 from "js-md5";
import PasswordEye from "./password-eye";
export default {
  components: {
    PasswordEye,
  },
  data() {
    return {
      loading: false,
      visible: false,
      userName: "",
      oldPassword: "",
      password: "",
      oldPasswordShow: false,
      passwordShow: false,
      passwordType: "password",
      oldPasswordType: "password",
      oldPasswordEyeShow: false,
      passwordEyeShow: false,
    };
  },
  props: {
    changePasswordShow: {
      type: Boolean,
      default: false,
    },
    changeName: {
      type: String,
      default: "",
    },
    overdueStatus: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    changePasswordShow(n) {
      this.visible = this.changePasswordShow;
      this.userName = this.changeName || localStorage.getItem("Geek_userInfo");
    },
  },
  mounted() {},
  methods: {
    async handleOk(e) {
      if (!this.userName || !this.oldPassword || !this.password) {
        this.oldPasswordShow = true;
        this.passwordShow = true;
        return false;
      }
      if (this.oldPassword === this.password) {
        this.$message({
          message: this.$t("lang.auth.PwdMgrAPI.item0008"),
          type: "error",
        });
        return false;
      }
      // 密码8-20位、数字、字母、特殊符号组成
      let passReg = /^(?=.*?[a-zA-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><\[\]}{\-=+_\\|》《。，、？’‘“”~ ]).{8,20}$/;
      if (!passReg.test(this.password)) {
        this.$message.error(this.$t("lang.auth.PwdMgrAPI.item0002"));
        return false;
      }
      let api = "";
      this.overdueStatus
        ? (api = "/athena/api/coreresource/auth/user/forceUpdatePassword/v1?isRsa=true")
        : (api = "/athena/api/coreresource/auth/user/updatePassword/v1?isRsa=true");
      const { data } = await $req.get("/athena/api/coreresource/auth/rsaKey/get/v1");
      const oldPsd = await $utils.rsa(md5(this.oldPassword + this.userName), data);
      const newPsd = await $utils.rsa(md5(this.password + this.userName), data);
      $req
        .post(api, {
          userName: this.userName,
          // password: md5(this.password + this.userName),
          // oldPassword: md5(this.oldPassword + this.userName),
          oldPassword: oldPsd,
          password: newPsd,
        })
        .then(res => {
          if (res.code === 0) {
            this.$message({
              message: this.$t(res.msg),
              type: "success",
            });
            this.handleCancel();
          } else {
            this.visible = true;
          }
        })
        .catch(err => {
          if (err.code === 2) {
            this.$message({
              message: this.$t(err.msg),
              type: "success",
            });
            this.$router.push("/login");
          }
          console.error(err);
        });
      this.loading = false;
    },
    handleCancel(e) {
      this.visible = false;
      this.$emit("updateVisible", false);
      this.userName = "";
      this.oldPassword = "";
      this.password = "";
    },
    changePwdShow() {
      const ps = !this.passwordEyeShow;
      this.passwordEyeShow = ps;
      this.passwordType = ps ? "text" : "password";
    },
    changeOldPwdShow() {
      const ps = !this.oldPasswordEyeShow;
      this.oldPasswordEyeShow = ps;
      this.oldPasswordType = ps ? "text" : "password";
    },
  },
};
</script>
<style lang="less" scoped>
.grid-content {
  border-radius: 4px;
  min-height: 36px;
  padding: 0 10px;
}

.spaceV {
  margin: 5px 0px;
  position: relative;
}

.redStar {
  color: #ff0000;
}

:deep(.gp-dialog__footer) {
  border-top: 1px solid #e8e8e8;
}

:deep(.gp-dialog__title) {
  font-weight: 500;
}
.pass-input {
  width: 100%;
}
.pwd-eye {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  width: 28px;
  height: 28px;
  z-index: 1;
  cursor: pointer;
  background-image: url(~@imgs/login/icon-eye-invisible.png);
  background-size: 18px;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  &.show {
    background-image: url(~@imgs/login/icon-eye.png);
  }
}
</style>
