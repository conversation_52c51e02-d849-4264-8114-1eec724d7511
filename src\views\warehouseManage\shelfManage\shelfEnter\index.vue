<template>
  <div>
    <gp-card>
      <div slot="header">
        <span>{{ warehouseName }}</span>
        <!-- <gp-switch
          v-model="autoAdvancedSelection"
          class="switch ml-20"
          :active-text="$t('lang.rms.fed.advanced')"
          :inactive-text="$t('lang.rms.fed.optionNormal')"
        /> -->
        <gp-switch
          v-if="checkPermission('MapManageZone4Automanual', 'natural')"
          v-model="autoLogicSelection"
          class="switch"
          :active-text="$t('lang.rms.fed.manualZone')"
          :inactive-text="$t('lang.rms.fed.automaticZone')"
        />
      </div>
      <gp-form ref="form" :model="form" label-width="80px" :rules="rules" label-position="top">
        <gp-row>
          <gp-col :span="5" />
        </gp-row>
        <gp-row :gutter="15">
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.shelfNumber')" prop="shelfCode">
              <gp-input
                ref="shelfCodeInput"
                v-model="form.shelfCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                @keyup.enter.native="handleShelfCodeInput"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.enteringPoint')" prop="cellCode">
              <gp-input
                ref="cellCodeInput"
                v-model="form.cellCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                :disabled="!shelfCodeComplate"
                @keyup.enter.native="handleCellCodeInput"
              />
            </gp-form-item>
          </gp-col>
          <gp-col v-if="autoAdvancedSelection" :span="5">
            <gp-form-item :label="$t('lang.rms.fed.placementCode')" prop="placementCellCode">
              <gp-input
                ref="placementCellCodeInput"
                v-model="form.placementCellCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                @keyup.enter.native="handlePlacementCellCodeInput"
              />
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <gp-form-item :label="$t('lang.rms.fed.selectTheZone')">
              <div v-if="!autoLogicSelection">{{ $t("lang.rms.fed.scanTheShelf") }}</div>
              <gp-select
                v-if="autoLogicSelection"
                v-model="form.logicSelection"
                :placeholder="$t('lang.rms.fed.pleaseChoose')"
              >
                <gp-option v-for="item in logics" :key="item.logicId" :label="item.logicName" :value="item.logicId" />
              </gp-select>
            </gp-form-item>
          </gp-col>
        </gp-row>
        <gp-row :gutter="15">
          <gp-col v-if="autoAdvancedSelection" :span="5">
            <gp-form-item :label="$t('lang.rms.fed.angle') + ':'">
              <gp-input-number
                v-model="form.angle"
                controls-position="right"
                :step="90"
                :min="-180"
                :max="180"
                @change="handleChange"
              />
            </gp-form-item>
          </gp-col>
          <gp-col v-if="autoAdvancedSelection" :span="5">
            <gp-form-item :label="$t('lang.rms.fed.shelfModel') + ':'">
              <gp-select v-model="form.classCode">
                <gp-option v-for="item in shelfModels" :key="item.id" :label="item.modelName" :value="item.modelType" />
              </gp-select>
            </gp-form-item>
          </gp-col>
          <gp-col v-if="autoAdvancedSelection" :span="5">
            <gp-form-item :label="$t('lang.rms.fed.shelfCodeRule') + ':'">
              <gp-select
                v-model="form.pattern"
                filterable
                allow-create
                clearable
                default-first-option
                :placeholder="$t('lang.rms.fed.selectShelfCodeRule')"
                @change="validatePattern"
              >
                <gp-option v-for="item in shelfPatterns" :key="item.value" :label="item.label" :value="item.value" />
              </gp-select>
            </gp-form-item>
          </gp-col>
          <gp-col :span="5">
            <div v-if="checkPermission('MapManageZoneSubmit', 'natural')" class="btnwarp">
              <gp-button
                v-if="autoLogicSelection || autoAdvancedSelection"
                type="primary"
                :disabled="(!autoLogicSelection && !autoAdvancedSelection) || !shelfCodeComplate || !cellCodeComplate"
                @click="requestShelfEnter"
              >
                {{ $t("lang.rms.fed.submit") }}
              </gp-button>
            </div>
          </gp-col>
        </gp-row>
      </gp-form>
    </gp-card>
    <gp-row :gutter="40">
      <gp-col :span="12">
        <gp-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.shelfInTheTask") }}</span>
          </div>
          <gp-table :data="shelfStatus" style="width: 100%">
            <gp-table-column type="index" width="50" />
            <gp-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" />
            <gp-table-column prop="logicName" :label="$t('lang.rms.fed.allocateZone')" />
            <gp-table-column prop="placement" :label="$t('lang.rms.fed.coordinate')">
              <template slot-scope="scope">
                {{ scope.row.placement.x }}, {{ scope.row.placement.y }},
                {{ scope.row.placement.z }}
              </template>
            </gp-table-column>
            <gp-table-column prop="status" :label="$t('lang.rms.fed.state')">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 'GO_RETURN'">{{ $t("lang.rms.fed.beingCarried") }}</span>
                <span v-if="scope.row.status === 'FETCHING'">{{ $t("lang.rms.fed.robotIsOnTheWay") }}</span>
                <span v-if="scope.row.status !== 'FETCHING' && scope.row.status !== 'GO_RETURN'">{{
                  $t("lang.rms.fed.malfunction")
                }}</span>
              </template>
            </gp-table-column>
          </gp-table>
        </gp-card>

        <gp-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.queryShelfCode") }}</span>
            <gp-input
              v-model="searchShelfCode"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              style="float: right; width: 200px; margin: -5px 0"
            >
              <gp-button slot="append" icon="gp-icon-search" @click="handleShelfSearch" />
            </gp-input>
          </div>
          <gp-table :data="shelfSearchData" style="width: 100%">
            <gp-table-column type="index" width="50" />
            <gp-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" />
            <gp-table-column prop="logicId" :label="$t('lang.rms.fed.allocateZone')">
              <template slot-scope="scope">{{ logicsDict[scope.row.logicId] }}</template>
            </gp-table-column>
            <gp-table-column prop="placement" :label="$t('lang.rms.fed.coordinate')">
              <template slot-scope="scope">
                {{ scope.row.placement.x }}, {{ scope.row.placement.y }},
                {{ scope.row.placement.z }}
              </template>
            </gp-table-column>
            <gp-table-column prop="placement" :label="$t('lang.rms.fed.state')">
              <template slot-scope="scope">{{ scope.row.state }}</template>
            </gp-table-column>
          </gp-table>
        </gp-card>
      </gp-col>
      <gp-col :span="12" style="padding-left: 0px">
        <gp-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.warehouseShelfLocationAvailable") }}</span>
            <span style="float: right">
              {{ $t("lang.rms.fed.shelfEntered") }}：{{ shelfCount }};
              {{ $t("lang.rms.fed.available") }}
              / {{ $t("lang.rms.fed.total") }}：{{ idleCount }}/{{ maxCount }}
            </span>
          </div>
          <gp-row :gutter="20">
            <gp-col v-for="item in logicAreas" :key="item.logicId" :span="12" class="logicareas">
              <div class="title">
                <span class="count">{{ item.maxCount - item.shelfCount }}/{{ item.maxCount }}</span>
                {{ item.logicName }}
              </div>
              <div class="range">
                <span
                  :style="{
                    width: Math.floor(((item.maxCount - item.shelfCount) / item.maxCount) * 100) + '%',
                  }"
                />
              </div>
            </gp-col>
          </gp-row>
        </gp-card>
      </gp-col>
    </gp-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        shelfCode: "",
        cellCode: "",
        placementCellCode: "",
        logicSelection: "",
        angle: "",
        classCode: "",
        pattern: "",
      },
      autoLogicSelection: false,
      autoAdvancedSelection: true,
      shelfCodeComplate: false,
      cellCodeComplate: false,
      shelfStatusDict: {
        FETCHING: this.$t("lang.rms.fed.robotIsOnTheWay"),
        GO_RETURN: this.$t("lang.rms.fed.beingCarried"),
      },
      rules: {
        shelfCode: [
          { required: true, message: this.$t("lang.rms.fed.pleaseEnterShelfCode"), trigger: "blur" },
          { validator: this.validateShelfCode, trigger: "blur" },
        ],
        cellCode: [
          { required: true, message: this.$t("lang.rms.fed.pleaseEnterCellCode"), trigger: "blur" },
          { validator: this.validateCellCode, trigger: "blur" },
        ],
        placementCellCode: [{ validator: this.validatePlacementCode, trigger: "blur" }],
        pattern: [{ validator: this.validateShelfCode1, trigger: "blur" }],
      },
      logics: [],
      logicsDict: {},
      shelfCount: 0,
      idleCount: 0,
      maxCount: 0,
      searchShelfCode: "",
      shelfSearchData: [],
      warehouseName: "",
      warehouseId: 0,
      logicAreas: [],
      shelfStatus: [],
      shelfStatusHead: [
        {
          prop: "shelfCode",
          label: this.$t("lang.rms.fed.shelfNumber"),
        },
        {
          prop: "logicName",
          label: this.$t("lang.rms.fed.allocateZone"),
        },
        {
          prop: "placement",
          label: this.$t("lang.rms.fed.coordinate"),
        },
        {
          prop: "status",
          label: this.$t("lang.rms.fed.state"),
        },
      ],
      tasksetInterval: null,
      shelfModels: [],
      shelfPatterns: [
        {
          value: "^[A-Z]{1}[0-9]{1,7}$",
          label: "^[A-Z]{1}[0-9]{1,7}$",
        },
        {
          value: "^[0-9]{2,8}$",
          label: "^[0-9]{2,8}$",
        },
        {
          value: "^[A-Z]{1}[0-9]{6}$",
          label: "^[A-Z]{1}[0-9]{6}$",
        },
      ],
    };
  },
  activated() {
    this.initLogic();
    this.queryAllWarehouse();
    this.initShelfModelInfo();
  },
  deactivated() {
    clearTimeout(this.tasksetInterval);
    this.tasksetInterval = null;
  },
  methods: {
    initLogic() {
      $req.get("/athena/warehouse/logic/findAll").then(res => {
        if (res.data && res.data.length > 0) {
          this.logics = res.data.filter(logic => logic.areaType === "SHELF");
          this.logics.forEach(item => {
            this.logicsDict[item.logicId] = item.logicName;
          });
        }
      });
    },
    validatePattern() {
      this.$refs["form"].validateField("shelfCode");
    },
    validateShelfCode(rule, value, callback) {
      var regx = this.form.pattern;
      if (regx) {
        regx = new RegExp(regx);
        if (!regx.test(this.form.shelfCode)) {
          var msg = this.$t("lang.rms.fed.noMatchRules");
          callback(new Error(msg));
          this.shelfCodeComplate = false;
          this.$refs["shelfCodeInput"].select();
          return;
        }
      }
      $req.get("/athena/shelf/exists", { shelfCode: value }).then(res => {
        if (res.data.status === 2) {
          callback();
          this.shelfCodeComplate = true;
          this.$nextTick(function () {
            // this.$refs["cellCodeInput"].focus();
          });
        } else {
          const str = res.data.descr.split(",");
          const mag = this.$t(this.$t(...str));
          callback(new Error(mag));
          this.$refs["shelfCodeInput"].select();
        }
      });
    },
    validateCellCode(rule, value, callback) {
      $req.get("/athena/map/cell/exists", { cellCode: value }).then(res => {
        if (res.data.status === 0) {
          callback();
          this.cellCodeComplate = true;
          if (!this.autoLogicSelection && !this.autoAdvancedSelection) {
            this.requestShelfEnter();
          }
        } else {
          const str = res.data.descr.split(",");
          const mag = this.$t(this.$t(...str));
          callback(new Error(mag));
          this.$refs["cellCodeInput"].select();
        }
      });
    },
    requestShelfEnter() {
      $req
        .post("/athena/shelf/add", {
          shelfCode: this.form.shelfCode,
          cellCode: this.form.cellCode,
          logicId: this.autoLogicSelection ? this.form.logicSelection : "",
          warehouseId: this.warehouseId,
          classCode: this.autoAdvancedSelection ? this.form.classCode : "",
          angle: this.autoAdvancedSelection ? this.form.angle : "",
          placementCellCode: this.autoAdvancedSelection ? this.form.placementCellCode : "",
        })
        .then(res => {
          if (res.code === 0) {
            this.$message({
              message: this.$t(res.msg),
              type: "success",
            });
          }
        });
    },
    handleShelfSearch() {
      $req
        .get("/athena/shelf/find", {
          shelfCode: this.searchShelfCode,
          warehouseId: this.warehouseId,
        })
        .then(res => {
          this.shelfSearchData = res.data;
        });
    },
    handleShelfCodeInput(event) {
      event.target.blur();
    },
    handleCellCodeInput(event) {
      event.target.blur();
    },
    handlePlacementCellCodeInput(event) {
      event.target.blur();
    },
    handlePatternInput(event) {
      event.target.blur();
    },
    async intervlaInfo() {
      if (this.tasksetInterval) {
        clearTimeout(this.tasksetInterval);
        this.tasksetInterval = null;
      }
      await this.initShelfStatus();
      await this.initLogicShelfInfo();
      this.tasksetInterval = setTimeout(() => {
        this.intervlaInfo();
      }, 2000);
    },
    queryAllWarehouse() {
      $req.get("/athena/warehouse/findAll").then(res => {
        const warehouse = res.data[0];
        this.warehouseId = warehouse.warehouseId;
        this.warehouseName = warehouse.warehouseName;
        this.intervlaInfo();
      });
    },
    async initShelfStatus() {
      let res = await $req.get("/athena/shelf/getEnterProcessing", { warehouseId: this.warehouseId });
      this.shelfStatus = res.data;
    },
    async initLogicShelfInfo() {
      let res = await $req.get("/athena/warehouse/getInfo", { warehouseId: this.warehouseId });
      this.shelfCount = res.data.shelfCount;
      this.idleCount = res.data.idleCount;
      this.maxCount = res.data.maxCount;
      this.logicAreas = res.data.logicAreas || [];
    },
    initShelfModelInfo() {
      $req.get("/athena/shelfModel/list").then(res => {
        this.shelfModels = res.data;
      });
    },
    validatePlacementCode(rule, value, callback) {
      if (value === "") {
        // FIXME:为什么这么写 什么都没有写个if判断 zhl
      } else {
        const data = new FormData();
        data.append("placementCellCode", value);
        data.append("logicId", this.autoLogicSelection ? this.form.logicSelection : "");

        $req.post("/athena/shelf/isPlacementLegal", data).then(res => {
          if (res.data.status === 0) {
            callback();
            // this.$refs["placementCellCodeInput"].values("");
            this.$nextTick(function () {
              // this.$refs["cellCodeInput"].focus();
            });
          } else {
            const str = res.data.descr.split(",");
            const mag = this.$t(this.$t(...str));
            callback(new Error(mag));
            // this.$refs["placementCellCodeInput"].select();
          }
        });
      }
    },
    handleChange(value) {
      console.log(value);
    },
  },
};
</script>

<style lang="less" scoped>
.gp-select {
  width: 100%;
}

.btnwarp {
  padding: 30px 0 0;
}

.switch {
  float: right;
  padding: 7px 0 0;
}

.ml-20 {
  margin-left: 20px;
}

.mt-5 {
  margin-top: 5px;
}

.logicareas {
  margin: 0 0 20px;
  font-size: 12px;

  .title {
    margin: 0 0 5px;
  }

  .count {
    float: right;
  }

  .range {
    background: #ebebeb;
    width: 100%;
    height: 10px;

    span {
      display: inline-block;
      background: #049c50;
      height: 10px;
      width: 0%;
    }
  }
}
</style>
