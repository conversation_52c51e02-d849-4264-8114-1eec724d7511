/* ! <AUTHOR> at 2022/08/27 */
import * as PIX<PERSON> from "pixi.js";

class LayerLockedbox implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private shader: any;
  private fragment: number;
  private lockedBoxGeometries: any = [];
  private meshList: any = [];
  private abnormalVColor: any;
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "rackLockedBox";
    container.zIndex = utils.getLayerZIndex("rack");
    container.interactiveChildren = false;
    container.visible = false;
    this.container = container;

    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.shader = utils.getShader("iconColor", utils.getResources("rack"));
    this.abnormalVColor = utils.getShaderColor("RACK_ABNORMAL"); //货箱架 异常颜色

    this.utils = utils;
  }

  render(): void {
    const utils = this.utils;
    const fragment = this.fragment;

    const lockedBoxGeometries = this.lockedBoxGeometries;
    const shader = this.shader;
    for (let i = 0, len = Math.ceil(lockedBoxGeometries.length / fragment); i < len; i++) {
      const arr = lockedBoxGeometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, shader);
      mesh.name = "rackLockedBox";
      mesh.mapType = "rackLockedBox";
      mesh.interactive = mesh.buttonMode = false;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }
  }

  drawGeometryLockedBox(options: mShelfData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry(
      "iconColor",
      options["position"],
      this.abnormalVColor,
    );
    _this.lockedBoxGeometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.lockedBoxGeometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.lockedBoxGeometries = null;
    this.shader = null;
    this.container = null;
    this.fragment = null;
    this.utils = null;
    this.meshList = null;
  }
}
export default LayerLockedbox;
